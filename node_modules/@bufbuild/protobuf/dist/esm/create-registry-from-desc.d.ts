import type { IEnumTypeRegistry, IExtensionRegistry, IMessageTypeRegistry, IServiceTypeRegistry } from "./type-registry.js";
import { FileDescriptorSet } from "./google/protobuf/descriptor_pb.js";
import type { DescriptorSet } from "./descriptor-set.js";
/**
 * Create a registry from a set of descriptors. The types returned by this
 * registry behave exactly like types from generated code.
 *
 * This function accepts google.protobuf.FileDescriptorSet in serialized or
 * deserialized form. Alternatively, it also accepts a DescriptorSet (see
 * createDescriptorSet()).
 *
 * By default, all well-known types with a specialized JSON representation
 * are replaced with their generated counterpart in this package.
 */
export declare function createRegistryFromDescriptors(input: DescriptorSet | FileDescriptorSet | Uint8Array, replaceWkt?: boolean): IMessageTypeRegistry & IEnumTypeRegistry & IExtensionRegistry & IServiceTypeRegistry;
