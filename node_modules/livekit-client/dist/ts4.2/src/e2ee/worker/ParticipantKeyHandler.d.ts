import type TypedEventEmitter from 'typed-emitter';
import type { ParticipantKeyHandlerCallbacks } from '../events';
import type { KeyProviderOptions, KeySet, RatchetResult } from '../types';
declare const ParticipantKeyHandler_base: new () => TypedEventEmitter<ParticipantKeyHandlerCallbacks>;
/**
 * ParticipantKeyHandler is responsible for providing a cryptor instance with the
 * en-/decryption key of a participant. It assumes that all tracks of a specific participant
 * are encrypted with the same key.
 * Additionally it exposes a method to ratchet a key which can be used by the cryptor either automatically
 * if decryption fails or can be triggered manually on both sender and receiver side.
 *
 */
export declare class ParticipantKeyHandler extends ParticipantKeyHandler_base {
    private currentKeyIndex;
    private cryptoKeyRing;
    private decryptionFailureCounts;
    private keyProviderOptions;
    private ratchetPromiseMap;
    private participantIdentity;
    /**
     * true if the current key has not been marked as invalid
     */
    get hasValidKey(): boolean;
    constructor(participantIdentity: string, keyProviderOptions: KeyProviderOptions);
    /**
     * Returns true if the key at the given index is marked as invalid.
     *
     * @param keyIndex the index of the key
     */
    hasInvalidKeyAtIndex(keyIndex: number): boolean;
    /**
     * Informs the key handler that a decryption failure occurred for an encryption key.
     * @internal
     * @param keyIndex the key index for which the failure occurred. Defaults to the current key index.
     */
    decryptionFailure(keyIndex?: number): void;
    /**
     * Informs the key handler that a frame was successfully decrypted using an encryption key.
     * @internal
     * @param keyIndex the key index for which the success occurred. Defaults to the current key index.
     */
    decryptionSuccess(keyIndex?: number): void;
    /**
     * Call this after user initiated ratchet or a new key has been set in order to make sure to mark potentially
     * invalid keys as valid again
     *
     * @param keyIndex the index of the key. Defaults to the current key index.
     */
    resetKeyStatus(keyIndex?: number): void;
    /**
     * Ratchets the current key (or the one at keyIndex if provided) and
     * returns the ratcheted material
     * if `setKey` is true (default), it will also set the ratcheted key directly on the crypto key ring
     * @param keyIndex
     * @param setKey
     */
    ratchetKey(keyIndex?: number, setKey?: boolean): Promise<RatchetResult>;
    /**
     * takes in a key material with `deriveBits` and `deriveKey` set as key usages
     * and derives encryption keys from the material and sets it on the key ring buffer
     * together with the material
     * also resets the valid key property and updates the currentKeyIndex
     */
    setKey(material: CryptoKey, keyIndex?: number): Promise<void>;
    /**
     * takes in a key material with `deriveBits` and `deriveKey` set as key usages
     * and derives encryption keys from the material and sets it on the key ring buffers
     * together with the material
     * also updates the currentKeyIndex
     */
    setKeyFromMaterial(material: CryptoKey, keyIndex: number, ratchetedResult?: RatchetResult | null): Promise<void>;
    setKeySet(keySet: KeySet, keyIndex: number, ratchetedResult?: RatchetResult | null): void;
    setCurrentKeyIndex(index: number): Promise<void>;
    getCurrentKeyIndex(): number;
    /**
     * returns currently used KeySet or the one at `keyIndex` if provided
     * @param keyIndex
     * @returns
     */
    getKeySet(keyIndex?: number): KeySet | undefined;
}
export {};
//# sourceMappingURL=ParticipantKeyHandler.d.ts.map
