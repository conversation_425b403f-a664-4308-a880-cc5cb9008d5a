import type { DataPacket } from '@livekit/protocol';
import type { ByteStreamHandler, TextStreamHandler } from './StreamReader';
export default class IncomingDataStreamManager {
    private log;
    private byteStreamControllers;
    private textStreamControllers;
    private byteStreamHandlers;
    private textStreamHandlers;
    registerTextStreamHandler(topic: string, callback: TextStreamHandler): void;
    unregisterTextStreamHandler(topic: string): void;
    registerByteStreamHandler(topic: string, callback: ByteStreamHandler): void;
    unregisterByteStreamHandler(topic: string): void;
    clearHandlersAndControllers(): void;
    validateParticipantHasNoActiveDataStreams(participantIdentity: string): void;
    handleDataStreamPacket(packet: DataPacket): Promise<void>;
    private handleStreamHeader;
    private handleStreamChunk;
    private handleStreamTrailer;
}
//# sourceMappingURL=IncomingDataStreamManager.d.ts.map
