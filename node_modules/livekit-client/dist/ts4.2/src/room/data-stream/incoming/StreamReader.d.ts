import type { DataStream_Chunk } from '@livekit/protocol';
import type { BaseStreamInfo, ByteStreamInfo, TextStreamInfo } from '../../types';
import { Future } from '../../utils';
export type BaseStreamReaderReadAllOpts = {
    /** An AbortSignal can be used to terminate reads early. */
    signal?: AbortSignal;
};
declare abstract class BaseStreamReader<T extends BaseStreamInfo> {
    protected reader: ReadableStream<DataStream_Chunk>;
    protected totalByteSize?: number;
    protected _info: T;
    protected bytesReceived: number;
    protected outOfBandFailureRejectingFuture?: Future<never>;
    get info(): T;
    /** @internal */
    protected validateBytesReceived(doneReceiving?: boolean): void;
    constructor(info: T, stream: ReadableStream<DataStream_Chunk>, totalByteSize?: number, outOfBandFailureRejectingFuture?: Future<never>);
    protected abstract handleChunkReceived(chunk: DataStream_Chunk): void;
    onProgress?: (progress: number | undefined) => void;
    abstract readAll(opts?: BaseStreamReaderReadAllOpts): Promise<string | Array<Uint8Array>>;
}
export declare class ByteStreamReader extends BaseStreamReader<ByteStreamInfo> {
    protected handleChunkReceived(chunk: DataStream_Chunk): void;
    onProgress?: (progress: number | undefined) => void;
    signal?: AbortSignal;
    [Symbol.asyncIterator](): {
        next: () => Promise<IteratorResult<Uint8Array>>;
        return(): Promise<IteratorResult<Uint8Array>>;
    };
    /**
     * Injects an AbortSignal, which if aborted, will terminate the currently active
     * stream iteration operation.
     *
     * Note that when using AbortSignal.timeout(...), the timeout applies across
     * the whole iteration operation, not just one individual chunk read.
     */
    withAbortSignal(signal: AbortSignal): this;
    readAll(opts?: BaseStreamReaderReadAllOpts): Promise<Array<Uint8Array>>;
}
/**
 * A class to read chunks from a ReadableStream and provide them in a structured format.
 */
export declare class TextStreamReader extends BaseStreamReader<TextStreamInfo> {
    private receivedChunks;
    signal?: AbortSignal;
    /**
     * A TextStreamReader instance can be used as an AsyncIterator that returns the entire string
     * that has been received up to the current point in time.
     */
    constructor(info: TextStreamInfo, stream: ReadableStream<DataStream_Chunk>, totalChunkCount?: number, outOfBandFailureRejectingFuture?: Future<never>);
    protected handleChunkReceived(chunk: DataStream_Chunk): void;
    /**
     * @param progress - progress of the stream between 0 and 1. Undefined for streams of unknown size
     */
    onProgress?: (progress: number | undefined) => void;
    /**
     * Async iterator implementation to allow usage of `for await...of` syntax.
     * Yields structured chunks from the stream.
     *
     */
    [Symbol.asyncIterator](): {
        next: () => Promise<IteratorResult<string>>;
        return(): Promise<IteratorResult<string>>;
    };
    /**
     * Injects an AbortSignal, which if aborted, will terminate the currently active
     * stream iteration operation.
     *
     * Note that when using AbortSignal.timeout(...), the timeout applies across
     * the whole iteration operation, not just one individual chunk read.
     */
    withAbortSignal(signal: AbortSignal): this;
    readAll(opts?: BaseStreamReaderReadAllOpts): Promise<string>;
}
export type ByteStreamHandler = (reader: ByteStreamReader, participantInfo: {
    identity: string;
}) => void;
export type TextStreamHandler = (reader: TextStreamReader, participantInfo: {
    identity: string;
}) => void;
export {};
//# sourceMappingURL=StreamReader.d.ts.map
