import type { StructuredLogger } from '../../../logger';
import type RTCEngine from '../../RTCEngine';
import type { SendFileOptions, SendTextOptions, StreamBytesOptions, StreamTextOptions, TextStreamInfo } from '../../types';
import { ByteStreamWriter, TextStreamWriter } from './StreamWriter';
/**
 * Manages sending custom user data via data channels.
 * @internal
 */
export default class OutgoingDataStreamManager {
    protected engine: RTCEngine;
    protected log: StructuredLogger;
    constructor(engine: RTCEngine, log: StructuredLogger);
    setupEngine(engine: RTCEngine): void;
    /** {@inheritDoc LocalParticipant.sendText} */
    sendText(text: string, options?: SendTextOptions): Promise<TextStreamInfo>;
    /**
     * @internal
     * @experimental CAUTION, might get removed in a minor release
     */
    streamText(options?: StreamTextOptions): Promise<TextStreamWriter>;
    sendFile(file: File, options?: SendFileOptions): Promise<{
        id: string;
    }>;
    private _sendFile;
    streamBytes(options?: StreamBytesOptions): Promise<ByteStreamWriter>;
}
//# sourceMappingURL=OutgoingDataStreamManager.d.ts.map
