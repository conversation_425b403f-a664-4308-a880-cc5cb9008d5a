import type { InternalRoomConnectOptions, InternalRoomOptions } from '../options';
import type { AudioCaptureOptions, TrackPublishDefaults, VideoCaptureOptions } from './track/options';
export declare const defaultVideoCodec = "vp8";
export declare const publishDefaults: TrackPublishDefaults;
export declare const audioDefaults: AudioCaptureOptions;
export declare const videoDefaults: VideoCaptureOptions;
export declare const roomOptionDefaults: InternalRoomOptions;
export declare const roomConnectOptionDefaults: InternalRoomConnectOptions;
//# sourceMappingURL=defaults.d.ts.map