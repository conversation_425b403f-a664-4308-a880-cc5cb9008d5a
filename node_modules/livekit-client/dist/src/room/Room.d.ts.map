{"version": 3, "file": "Room.d.ts", "sourceRoot": "", "sources": ["../../../src/room/Room.ts"], "names": [], "mappings": "AACA,OAAO,EAIL,eAAe,EACf,gBAAgB,EAIhB,YAAY,EAGZ,qBAAqB,EAErB,UAAU,EAEV,OAAO,EAGP,iBAAiB,EAOjB,oBAAoB,IAAI,yBAAyB,EAGlD,MAAM,mBAAmB,CAAC;AAE3B,OAAO,KAAK,YAAY,MAAM,eAAe,CAAC;AAC9C,OAAO,gBAAgB,CAAC;AAIxB,OAAO,KAAK,EAEV,mBAAmB,EACnB,kBAAkB,EAClB,WAAW,EACZ,MAAM,YAAY,CAAC;AAGpB,OAAO,SAAS,MAAM,aAAa,CAAC;AAGpC,OAAO,EACL,KAAK,iBAAiB,EACtB,KAAK,iBAAiB,EACvB,MAAM,qCAAqC,CAAC;AAW7C,OAAO,gBAAgB,MAAM,gCAAgC,CAAC;AAC9D,OAAO,KAAK,WAAW,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,KAAK,iBAAiB,EAAmB,MAAM,2BAA2B,CAAC;AACpF,OAAO,iBAAiB,MAAM,iCAAiC,CAAC;AAChE,OAAO,EAA+B,KAAK,iBAAiB,EAAc,MAAM,OAAO,CAAC;AAIxF,OAAO,qBAAqB,MAAM,+BAA+B,CAAC;AAElE,OAAO,KAAK,WAAW,MAAM,qBAAqB,CAAC;AACnD,OAAO,sBAAsB,MAAM,gCAAgC,CAAC;AACpE,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAIjE,OAAO,EACL,KAAK,WAAW,EAChB,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACvB,KAAK,oBAAoB,EAC1B,MAAM,SAAS,CAAC;AAwBjB,oBAAY,eAAe;IACzB,YAAY,iBAAiB;IAC7B,UAAU,eAAe;IACzB,SAAS,cAAc;IACvB,YAAY,iBAAiB;IAC7B,kBAAkB,uBAAuB;CAC1C;yBAYmC,UAAU,YAAY,CAAC,kBAAkB,CAAC;AAR9E;;;;;;;GAOG;AACH,cAAM,IAAK,SAAQ,SAA4D;IAC7E,KAAK,EAAE,eAAe,CAAgC;IAEtD;;OAEG;IACH,kBAAkB,EAAE,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;IAEnD;;;OAGG;IACH,cAAc,EAAE,WAAW,EAAE,CAAM;IAEnC,gBAAgB;IAChB,MAAM,EAAG,SAAS,CAAC;IAEnB,8BAA8B;IAC9B,gBAAgB,EAAE,gBAAgB,CAAC;IAEnC,sBAAsB;IACtB,OAAO,EAAE,mBAAmB,CAAC;IAE7B,qEAAqE;IACrE,aAAa,EAAE,OAAO,CAAS;IAE/B,UAAU,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;IAEjC,OAAO,CAAC,QAAQ,CAAC,CAAY;IAE7B,OAAO,CAAC,aAAa,CAAsB;IAE3C,8BAA8B;IAC9B,OAAO,CAAC,WAAW,CAAC,CAA6B;IAEjD,OAAO,CAAC,YAAY,CAAQ;IAE5B,OAAO,CAAC,YAAY,CAAC,CAAe;IAEpC,gEAAgE;IAChE,OAAO,CAAC,eAAe,CAAC,CAAkB;IAE1C,yDAAyD;IACzD,OAAO,CAAC,aAAa,CAAC,CAAe;IAErC,OAAO,CAAC,cAAc,CAAQ;IAE9B,OAAO,CAAC,WAAW,CAA8B;IAEjD,OAAO,CAAC,2BAA2B,CAAC,CAAiC;IAErE,OAAO,CAAC,iBAAiB,CAAC,CAAoB;IAE9C,OAAO,CAAC,SAAS,CAAC,CAAS;IAE3B,OAAO,CAAC,sBAAsB,CAAkB;IAEhD,OAAO,CAAC,GAAG,CAAO;IAElB,OAAO,CAAC,cAAc,CAAkB;IAExC,OAAO,CAAC,UAAU,CAAkB;IAEpC;;OAEG;IACH,OAAO,CAAC,0BAA0B,CAAsB;IAExD,OAAO,CAAC,yBAAyB,CAA4B;IAE7D,OAAO,CAAC,yBAAyB,CAA4B;IAE7D,OAAO,CAAC,WAAW,CAAwE;IAE3F;;;OAGG;gBACS,OAAO,CAAC,EAAE,WAAW;IA8EjC,yBAAyB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,iBAAiB;IAIpE,2BAA2B,CAAC,KAAK,EAAE,MAAM;IAIzC,yBAAyB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,iBAAiB;IAIpE,2BAA2B,CAAC,KAAK,EAAE,MAAM;IAIzC;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,OAAO,CAAC,MAAM,CAAC;IASvF;;;;OAIG;IACH,mBAAmB,CAAC,MAAM,EAAE,MAAM;IAIlC;;OAEG;IACG,cAAc,CAAC,OAAO,EAAE,OAAO;IAWrC,OAAO,CAAC,SAAS;IAuBjB,OAAO,KAAK,UAAU,GAOrB;IAED;;QAEI;IACJ,IAAI,WAAW,IAAI,OAAO,CAEzB;IAED;;;OAGG;IACG,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC;IAsB/B,iDAAiD;IACjD,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,oBAAoB;IACpB,IAAI,QAAQ,IAAI,MAAM,GAAG,SAAS,CAEjC;IAED,IAAI,eAAe,IAAI,MAAM,CAE5B;IAED,IAAI,aAAa,IAAI,MAAM,CAE1B;IAED,OAAO,CAAC,iBAAiB;IA6GzB;;;;;;OAMG;IACH,MAAM,CAAC,eAAe,CACpB,IAAI,CAAC,EAAE,eAAe,EACtB,kBAAkB,GAAE,OAAc,GACjC,OAAO,CAAC,eAAe,EAAE,CAAC;IAI7B,MAAM,CAAC,eAAe,qCAEqB,IAAI,EAE1C;IAEL;;;;;;;;OAQG;IACG,iBAAiB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM;IAwBnD,OAAO,GAAU,KAAK,MAAM,EAAE,OAAO,MAAM,EAAE,OAAO,kBAAkB,KAAG,OAAO,CAAC,IAAI,CAAC,CAiIpF;IAEF,OAAO,CAAC,aAAa,CAkDnB;IAEF,OAAO,CAAC,iBAAiB,CAwBvB;IAEF,OAAO,CAAC,iBAAiB,CAiGvB;IAEF;;OAEG;IACH,UAAU,GAAU,oBAAiB,mBAsCnC;IAEF;;;;OAIG;IACH,wBAAwB,CAAC,QAAQ,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;IAOnE,OAAO,CAAC,sBAAsB;IAI9B;;OAEG;IACG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,EAAE,GAAG,CAAC,EAAE,GAAG;IAwH9D,OAAO,CAAC,WAAW,CAGjB;IAEF;;;;;;OAMG;IACH,UAAU,sBAqER;IAEF,UAAU,sBAyBR;IAEF;;OAEG;IACH,IAAI,gBAAgB,IAAI,OAAO,CAE9B;IAED;;OAEG;IACH,IAAI,gBAAgB,IAAI,OAAO,CAE9B;IAED,eAAe,CAAC,IAAI,EAAE,eAAe,GAAG,MAAM,GAAG,SAAS;IAI1D;;;;;;;;;OASG;IACG,kBAAkB,CAAC,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAE,OAAc;IAkFvF,OAAO,CAAC,2BAA2B;IAmBnC,OAAO,CAAC,cAAc;IActB,OAAO,CAAC,YAAY;IAqFpB,OAAO,CAAC,gBAAgB,CAatB;IAEF,OAAO,CAAC,qBAAqB,CAiC3B;IAEF,OAAO,CAAC,gBAAgB;IAuExB,OAAO,CAAC,wBAAwB,CAwB9B;IAEF,OAAO,CAAC,6BAA6B;IAkBrC,OAAO,CAAC,0BAA0B,CAgChC;IAGF,OAAO,CAAC,qBAAqB,CA8B3B;IAEF,OAAO,CAAC,uBAAuB,CAsB7B;IAEF,OAAO,CAAC,kCAAkC,CAWxC;IAEF,OAAO,CAAC,uBAAuB,CAa7B;IAEF,OAAO,CAAC,gBAAgB,CA8BtB;IAEF,OAAO,CAAC,gBAAgB,CAStB;IAEF,OAAO,CAAC,aAAa,CAKnB;IAEF,OAAO,CAAC,mBAAmB,CAgBzB;IAEF,OAAO,CAAC,iBAAiB,CAMvB;IAEF,OAAO,CAAC,aAAa,CAEnB;IAEF,OAAO,CAAC,gBAAgB,CAEtB;YAEY,wBAAwB;IA8DtC,gBAAgB,EAAE,GAAG,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAa;IAErE,OAAO,CAAC,0BAA0B,CAMhC;IAEF,OAAO,CAAC,yBAAyB,CAO/B;IAEF,OAAO,CAAC,0BAA0B,CAKhC;IAEF,OAAO,CAAC,yBAAyB,CAK/B;IAEF;;OAEG;YACW,oBAAoB;IAiElC,OAAO,CAAC,kBAAkB,CAMxB;IAEF,OAAO,CAAC,gBAAgB,CAStB;IAEF,OAAO,CAAC,6BAA6B,CAWnC;YAEY,mBAAmB;IAmCjC,OAAO,CAAC,iBAAiB;IAgCzB,OAAO,CAAC,sBAAsB;IA6G9B,OAAO,CAAC,aAAa;IASrB;;;OAGG;IACH,OAAO,CAAC,mBAAmB;IAU3B,OAAO,CAAC,yBAAyB;IAOjC,OAAO,CAAC,2BAA2B;IAoCnC,OAAO,CAAC,wBAAwB;IAMhC,OAAO,CAAC,yBAAyB;IAUjC,OAAO,CAAC,kBAAkB;IAO1B,OAAO,CAAC,iBAAiB;IAkBzB,OAAO,CAAC,iCAAiC,CAEvC;IAEF,OAAO,CAAC,6BAA6B,CAEnC;IAEF,OAAO,CAAC,wBAAwB,CAE9B;IAEF,OAAO,CAAC,iBAAiB,CAEvB;IAEF,OAAO,CAAC,mBAAmB,CAEzB;IAEF,OAAO,CAAC,sBAAsB,CAE5B;IAEF,OAAO,CAAC,qBAAqB,CAuB3B;IAEF,OAAO,CAAC,uBAAuB,CAI7B;IAEF,OAAO,CAAC,qBAAqB,CAe3B;IAEF,OAAO,CAAC,+BAA+B,CAErC;IAEF,OAAO,CAAC,mBAAmB,CAEzB;IAEF,OAAO,CAAC,oCAAoC,CAE1C;IAEF,OAAO,CAAC,sBAAsB,CAE5B;IAEF;;;;OAIG;IACG,oBAAoB,CAAC,OAAO,EAAE,iBAAiB;IA+IrD,IAAI,CAAC,CAAC,SAAS,MAAM,kBAAkB,EACrC,KAAK,EAAE,CAAC,EACR,GAAG,IAAI,EAAE,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,GACzC,OAAO;CASX;AAiBD,eAAe,IAAI,CAAC;AAEpB,MAAM,MAAM,kBAAkB,GAAG;IAC/B,SAAS,EAAE,MAAM,IAAI,CAAC;IACtB,YAAY,EAAE,MAAM,IAAI,CAAC;IACzB,kBAAkB,EAAE,MAAM,IAAI,CAAC;IAC/B,WAAW,EAAE,MAAM,IAAI,CAAC;IACxB,YAAY,EAAE,CAAC,MAAM,CAAC,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAClD,sBAAsB,EAAE,CAAC,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;IACzD,KAAK,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;IAC9B,mBAAmB,EAAE,MAAM,IAAI,CAAC;IAChC,oBAAoB,EAAE,CAAC,WAAW,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC/D,uBAAuB,EAAE,CAAC,WAAW,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAClE,cAAc,EAAE,CAAC,WAAW,EAAE,sBAAsB,EAAE,WAAW,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC9F,eAAe,EAAE,CACf,KAAK,EAAE,WAAW,EAClB,WAAW,EAAE,sBAAsB,EACnC,WAAW,EAAE,iBAAiB,KAC3B,IAAI,CAAC;IACV,uBAAuB,EAAE,CACvB,QAAQ,EAAE,MAAM,EAChB,WAAW,EAAE,iBAAiB,EAC9B,MAAM,CAAC,EAAE,iBAAiB,KACvB,IAAI,CAAC;IACV,gBAAgB,EAAE,CAAC,WAAW,EAAE,sBAAsB,EAAE,WAAW,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAChG,iBAAiB,EAAE,CACjB,KAAK,EAAE,WAAW,EAClB,WAAW,EAAE,sBAAsB,EACnC,WAAW,EAAE,iBAAiB,KAC3B,IAAI,CAAC;IACV,UAAU,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;IAC9E,YAAY,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;IAChF,mBAAmB,EAAE,CAAC,WAAW,EAAE,qBAAqB,EAAE,WAAW,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACjG,qBAAqB,EAAE,CACrB,WAAW,EAAE,qBAAqB,EAClC,WAAW,EAAE,gBAAgB,KAC1B,IAAI,CAAC;IACV,yBAAyB,EAAE,CAAC,WAAW,EAAE,qBAAqB,KAAK,IAAI,CAAC;IACxE,0BAA0B,EAAE,CAC1B,QAAQ,EAAE,MAAM,GAAG,SAAS,EAC5B,WAAW,EAAE,iBAAiB,GAAG,gBAAgB,KAC9C,IAAI,CAAC;IACV,sBAAsB,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,iBAAiB,GAAG,gBAAgB,KAAK,IAAI,CAAC;IAClG,6BAA6B,EAAE,CAC7B,eAAe,EAAE,qBAAqB,GAAG,SAAS,EAClD,WAAW,EAAE,iBAAiB,GAAG,gBAAgB,KAC9C,IAAI,CAAC;IACV,4BAA4B,EAAE,CAC5B,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EACzC,WAAW,EAAE,iBAAiB,GAAG,gBAAgB,KAC9C,IAAI,CAAC;IACV,qBAAqB,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC;IAC9D,mBAAmB,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IAChD,YAAY,EAAE,CACZ,OAAO,EAAE,UAAU,EACnB,WAAW,CAAC,EAAE,iBAAiB,EAC/B,IAAI,CAAC,EAAE,eAAe,EACtB,KAAK,CAAC,EAAE,MAAM,KACX,IAAI,CAAC;IACV,eAAe,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC1E,qBAAqB,EAAE,CACrB,aAAa,EAAE,oBAAoB,EAAE,EACrC,WAAW,CAAC,EAAE,WAAW,EACzB,WAAW,CAAC,EAAE,gBAAgB,KAC3B,IAAI,CAAC;IACV,wBAAwB,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;IACzF,iBAAiB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,eAAe,KAAK,IAAI,CAAC;IAClE,uBAAuB,EAAE,CACvB,WAAW,EAAE,sBAAsB,EACnC,WAAW,EAAE,KAAK,CAAC,WAAW,EAC9B,WAAW,EAAE,iBAAiB,KAC3B,IAAI,CAAC;IACV,kCAAkC,EAAE,CAClC,WAAW,EAAE,sBAAsB,EACnC,MAAM,EAAE,gBAAgB,CAAC,gBAAgB,EACzC,WAAW,EAAE,iBAAiB,KAC3B,IAAI,CAAC;IACV,8BAA8B,EAAE,CAC9B,WAAW,EAAE,sBAAsB,EACnC,MAAM,EAAE,gBAAgB,CAAC,kBAAkB,EAC3C,WAAW,EAAE,iBAAiB,KAC3B,IAAI,CAAC;IACV,oBAAoB,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IACjD,oBAAoB,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IACjD,eAAe,EAAE,MAAM,IAAI,CAAC;IAC5B,sBAAsB,EAAE,CAAC,SAAS,EAAE,OAAO,KAAK,IAAI,CAAC;IACrD,kCAAkC,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE,WAAW,KAAK,IAAI,CAAC;IAC5F,eAAe,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IACxC,qBAAqB,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,eAAe,KAAK,IAAI,CAAC;IACvE,mBAAmB,EAAE,CAAC,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IACvE,WAAW,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,gBAAgB,KAAK,IAAI,CAAC;IAChG,oBAAoB,EAAE,CAAC,WAAW,EAAE,qBAAqB,EAAE,WAAW,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAClG,eAAe,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,WAAW,CAAC,EAAE,WAAW,KAAK,IAAI,CAAC;IAC5E,iBAAiB,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;CACvD,CAAC"}