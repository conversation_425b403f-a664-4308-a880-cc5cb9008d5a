{"version": 3, "file": "utils.d.ts", "sourceRoot": "", "sources": ["../../../src/room/utils.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,WAAW,IAAI,gBAAgB,EAC/B,UAAU,EAEV,gBAAgB,EAChB,aAAa,IAAI,kBAAkB,EACpC,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,KAAK,cAAc,EAAc,MAAM,wBAAwB,CAAC;AAEzE,OAAO,EAAE,KAAK,eAAe,EAAyB,MAAM,UAAU,CAAC;AACvE,OAAO,KAAK,gBAAgB,MAAM,gCAAgC,CAAC;AACnE,OAAO,KAAK,WAAW,MAAM,2BAA2B,CAAC;AACzD,OAAO,KAAK,iBAAiB,MAAM,iCAAiC,CAAC;AAErE,OAAO,KAAK,eAAe,MAAM,yBAAyB,CAAC;AAC3D,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,qBAAqB,MAAM,+BAA+B,CAAC;AACvE,OAAO,KAAK,eAAe,MAAM,yBAAyB,CAAC;AAC3D,OAAO,KAAK,gBAAgB,MAAM,0BAA0B,CAAC;AAC7D,OAAO,KAAK,WAAW,MAAM,qBAAqB,CAAC;AACnD,OAAO,KAAK,sBAAsB,MAAM,gCAAgC,CAAC;AACzE,OAAO,KAAK,gBAAgB,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,EAAE,KAAK,UAAU,EAAe,MAAM,iBAAiB,CAAC;AAE/D,OAAO,KAAK,EAAE,WAAW,EAA0B,oBAAoB,EAAE,MAAM,SAAS,CAAC;AAGzF,eAAO,MAAM,cAAc,4FACgE,CAAC;AAE5F,wBAAgB,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,CAMvD;AAED,wBAAsB,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAE3D;AAED,gBAAgB;AAChB,wBAAgB,mBAAmB,YAElC;AAED,gBAAgB;AAChB,wBAAgB,gBAAgB,YAE/B;AAED,wBAAgB,sBAAsB,YAErC;AAED,wBAAgB,gBAAgB,YAE/B;AAED,wBAAgB,WAAW,IAAI,OAAO,CAoBrC;AAED,wBAAgB,WAAW,IAAI,OAAO,CAmCrC;AAED,wBAAgB,YAAY,IAAI,OAAO,CAgBtC;AAED,wBAAgB,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAElD;AAED,wBAAgB,iBAAiB,CAAC,GAAG,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAQjE;AAED,wBAAgB,kBAAkB,YAKjC;AAED,wBAAgB,SAAS,IAAI,OAAO,CAEnC;AAED,wBAAgB,eAAe,IAAI,OAAO,CAEzC;AAED,wBAAgB,QAAQ,IAAI,OAAO,CAElC;AAED,wBAAgB,aAAa,IAAI,OAAO,CAGvC;AAED,wBAAgB,eAAe,IAAI,OAAO,CAMzC;AAED,wBAAgB,cAAc,CAAC,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAWhE;AAED,wBAAgB,QAAQ,IAAI,OAAO,CAQlC;AAED,wBAAgB,wBAAwB,wBAqBvC;AAED,wBAAgB,KAAK,IAAI,OAAO,CAE/B;AAED,wBAAgB,aAAa,IAAI,OAAO,CAGvC;AAED,wBAAgB,OAAO,CAAC,SAAS,EAAE,GAAG,WAIrC;AAaD,wBAAgB,gBAAgB,IAAI,MAAM,GAAG,SAAS,CAWrD;AAED,wBAAgB,mBAAmB,IAAI,MAAM,CAa5C;AAED;;;;;;;GAOG;AACH,wBAAgB,eAAe,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,MAAM,CAiB9D;AAeD,eAAO,MAAM,iBAAiB,sBAG7B,CAAC;AAGF,eAAO,MAAM,uBAAuB,4BAQnC,CAAC;AAEF,MAAM,WAAW,sBAAuB,SAAQ,gBAAgB;IAC9D,YAAY,EAAE,CAAC,KAAK,EAAE,mBAAmB,KAAK,IAAI,CAAC;IACnD,uBAAuB,EAAE,CAAC,KAAK,EAAE,yBAAyB,KAAK,IAAI,CAAC;CACrE;AAED,wBAAgB,aAAa,IAAI,UAAU,CAW1C;AAID,wBAAgB,wBAAwB,qBAKvC;AAED,wBAAgB,2BAA2B,CACzC,KAAK,GAAE,MAAW,EAClB,MAAM,GAAE,MAAW,EACnB,OAAO,GAAE,OAAe,EACxB,YAAY,GAAE,OAAe,oBAwB9B;AAID,wBAAgB,wBAAwB,qBAkBvC;AAED,qBAAa,MAAM,CAAC,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IAEpB,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC;IAE3B,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IAE1B,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC;IAEvB,IAAI,UAAU,IAAI,OAAO,CAExB;IAED,OAAO,CAAC,WAAW,CAAkB;gBAGnC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,IAAI,KAAK,IAAI,EAC1E,SAAS,CAAC,EAAE,MAAM,IAAI;CAczB;AAED,MAAM,MAAM,oBAAoB,GAAG;IACjC;;;;OAIG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB,CAAC;AAEF;;;;GAIG;AACH,wBAAgB,mBAAmB,CACjC,KAAK,EAAE,eAAe,GAAG,gBAAgB,EACzC,OAAO,CAAC,EAAE,oBAAoB;;;;EAgD/B;AAED,wBAAgB,YAAY,CAAC,UAAU,EAAE,MAAM,GAAG,UAAU,IAAI,UAAU,CAEzE;AAED,wBAAgB,gBAAgB,CAAC,UAAU,EAAE,kBAAkB,GAAG,MAAM,CAAC;AACzE,wBAAgB,gBAAgB,CAAC,UAAU,EAAE,cAAc,GAAG,MAAM,CAAC;AAwBrE,wBAAgB,cAAc,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAKlD;AAED,wBAAgB,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAK7C;AAED,wBAAgB,4BAA4B,CAC1C,aAAa,EAAE,kBAAkB,EACjC,qBAAqB,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GACzC,oBAAoB,EAAE,CAoBxB;AAED,wBAAgB,kBAAkB,CAAC,GAAG,EAAE,gBAAgB,GAAG,WAAW,CAQrE;AAED,wBAAgB,sCAAsC,CAAC,CAAC,EAAE,eAAe,oBAaxE;AAED,6DAA6D;AAC7D,wBAAgB,cAAc,CAAC,CAAC,SAAS,MAAM,GAAG,SAAS,EACzD,KAAK,EAAE,CAAC,GACP,CAAC,SAAS,MAAM,GAAG,MAAM,GAAG,SAAS,CAEvC;AAED,6DAA6D;AAC7D,wBAAgB,cAAc,CAAC,CAAC,SAAS,MAAM,GAAG,SAAS,EACzD,KAAK,EAAE,CAAC,GACP,CAAC,SAAS,MAAM,GAAG,MAAM,GAAG,SAAS,CAEvC;AAED,wBAAgB,YAAY,CAAC,KAAK,EAAE,KAAK,GAAG,gBAAgB,GAAG,SAAS,GAAG,KAAK,IAAI,UAAU,CAE7F;AAED,wBAAgB,YAAY,CAC1B,KAAK,EAAE,KAAK,GAAG,SAAS,GACvB,KAAK,IAAI,eAAe,GAAG,gBAAgB,CAE7C;AAED,wBAAgB,YAAY,CAC1B,KAAK,EAAE,KAAK,GAAG,SAAS,GACvB,KAAK,IAAI,eAAe,GAAG,gBAAgB,CAE7C;AAED,wBAAgB,iBAAiB,CAC/B,KAAK,EAAE,KAAK,GAAG,gBAAgB,GAAG,SAAS,GAC1C,KAAK,IAAI,eAAe,CAE1B;AAED,wBAAgB,iBAAiB,CAC/B,KAAK,EAAE,KAAK,GAAG,gBAAgB,GAAG,SAAS,GAC1C,KAAK,IAAI,eAAe,CAE1B;AAED,wBAAgB,aAAa,CAAC,KAAK,EAAE,KAAK,GAAG,SAAS,GAAG,KAAK,IAAI,WAAW,CAE5E;AAED,wBAAgB,WAAW,CAAC,GAAG,EAAE,gBAAgB,GAAG,SAAS,GAAG,GAAG,IAAI,sBAAsB,CAE5F;AAED,wBAAgB,UAAU,CAAC,GAAG,EAAE,gBAAgB,GAAG,SAAS,GAAG,GAAG,IAAI,qBAAqB,CAE1F;AAED,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,KAAK,GAAG,SAAS,GAAG,KAAK,IAAI,gBAAgB,CAEtF;AAED,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,IAAI,gBAAgB,CAExE;AAED,wBAAgB,mBAAmB,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,IAAI,iBAAiB,CAE1E;AAED,wBAAgB,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,UAAU,EAAE,CAuB5D"}