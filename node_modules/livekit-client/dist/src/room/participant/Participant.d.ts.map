{"version": 3, "file": "Participant.d.ts", "sourceRoot": "", "sources": ["../../../../src/room/participant/Participant.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,eAAe,EACf,eAAe,EAEf,oBAAoB,IAAI,eAAe,EACvC,qBAAqB,EACrB,iBAAiB,IAAI,YAAY,EACjC,KAAK,OAAO,EACZ,iBAAiB,EAClB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,KAAK,YAAY,MAAM,eAAe,CAAC;AAC9C,OAAY,EAAe,KAAK,gBAAgB,EAAa,MAAM,cAAc,CAAC;AAElF,OAAO,KAAK,qBAAqB,MAAM,gCAAgC,CAAC;AACxE,OAAO,KAAK,eAAe,MAAM,0BAA0B,CAAC;AAC5D,OAAO,KAAK,WAAW,MAAM,sBAAsB,CAAC;AACpD,OAAO,KAAK,sBAAsB,MAAM,iCAAiC,CAAC;AAC1E,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACvC,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAElE,OAAO,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,oBAAoB,EAAE,MAAM,UAAU,CAAC;AACjF,OAAO,EAAE,MAAM,EAAgB,MAAM,UAAU,CAAC;AAEhD,oBAAY,iBAAiB;IAC3B,SAAS,cAAc;IACvB,IAAI,SAAS;IACb,IAAI,SAAS;IACb;;;OAGG;IACH,IAAI,SAAS;IACb,OAAO,YAAY;CACpB;AAiBD,OAAO,EAAE,eAAe,EAAE,CAAC;gCAE+B,UAAU,YAAY,CAAC,yBAAyB,CAAC;AAA3G,MAAM,CAAC,OAAO,OAAO,WAAY,SAAQ,gBAAmE;IAC1G,SAAS,CAAC,eAAe,CAAC,EAAE,eAAe,CAAC;IAE5C,sBAAsB,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAEtD,sBAAsB,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAEtD,+CAA+C;IAC/C,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAEjD,kEAAkE;IAClE,UAAU,EAAE,MAAM,CAAK;IAEvB,2CAA2C;IAC3C,UAAU,EAAE,OAAO,CAAS;IAE5B,gCAAgC;IAChC,GAAG,EAAE,MAAM,CAAC;IAEZ,qDAAqD;IACrD,QAAQ,EAAE,MAAM,CAAC;IAEjB,yDAAyD;IACzD,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd,yCAAyC;IACzC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB,OAAO,CAAC,WAAW,CAAyB;IAE5C,WAAW,CAAC,EAAE,IAAI,GAAG,SAAS,CAAC;IAE/B,WAAW,CAAC,EAAE,qBAAqB,CAAC;IAEpC,SAAS,CAAC,KAAK,EAAE,eAAe,CAAC;IAEjC,OAAO,CAAC,kBAAkB,CAAgD;IAE1E,SAAS,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC;IAEtC,SAAS,CAAC,GAAG,EAAE,gBAAgB,CAAO;IAEtC,SAAS,CAAC,aAAa,CAAC,EAAE,aAAa,CAAC;IAExC,SAAS,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IAEtC,SAAS,KAAK,UAAU;;MAIvB;IAED,IAAI,WAAW,YAKd;IAED,IAAI,OAAO,YAEV;IAED,IAAI,QAAQ,YAEX;IAED,IAAI,IAAI,oBAEP;IAED,0EAA0E;IAC1E,IAAI,UAAU,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAEjD;IAED,gBAAgB;gBAEd,GAAG,EAAE,MAAM,EACX,QAAQ,EAAE,MAAM,EAChB,IAAI,CAAC,EAAE,MAAM,EACb,QAAQ,CAAC,EAAE,MAAM,EACjB,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EACnC,aAAa,CAAC,EAAE,aAAa,EAC7B,IAAI,GAAE,eAA0C;IAmBlD,oBAAoB,IAAI,gBAAgB,EAAE;IAI1C;;;OAGG;IACH,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,GAAG,gBAAgB,GAAG,SAAS;IAQvE;;OAEG;IACH,yBAAyB,CAAC,IAAI,EAAE,MAAM,GAAG,gBAAgB,GAAG,SAAS;IAQrE;;;OAGG;IACH,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;IAkBhC,IAAI,iBAAiB,IAAI,iBAAiB,CAEzC;IAED,IAAI,eAAe,IAAI,OAAO,CAG7B;IAED,IAAI,mBAAmB,IAAI,OAAO,CAGjC;IAED,IAAI,oBAAoB,IAAI,OAAO,CAGlC;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,uCAAuC;IACvC,IAAI,QAAQ,IAAI,IAAI,GAAG,SAAS,CAK/B;IAED,gBAAgB;IAChB,UAAU,CAAC,IAAI,EAAE,eAAe,GAAG,OAAO;IAiC1C;;QAEI;IACJ,OAAO,CAAC,YAAY;IAUpB,OAAO,CAAC,QAAQ;IAShB;;QAEI;IACJ,OAAO,CAAC,cAAc;IAStB,gBAAgB;IAChB,cAAc,CAAC,WAAW,EAAE,qBAAqB,GAAG,OAAO;IAqB3D,gBAAgB;IAChB,aAAa,CAAC,QAAQ,EAAE,OAAO;IAW/B,gBAAgB;IAChB,oBAAoB,CAAC,CAAC,EAAE,YAAY;IAQpC;;OAEG;IACH,eAAe;IAOf;;OAEG;IACH,eAAe,CAAC,GAAG,EAAE,YAAY,GAAG,SAAS;IAO7C,SAAS,CAAC,mBAAmB,CAAC,WAAW,EAAE,gBAAgB;CA2B5D;AAED,MAAM,MAAM,yBAAyB,GAAG;IACtC,cAAc,EAAE,CAAC,WAAW,EAAE,sBAAsB,KAAK,IAAI,CAAC;IAC9D,eAAe,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,sBAAsB,KAAK,IAAI,CAAC;IACnF,uBAAuB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAChF,gBAAgB,EAAE,CAAC,WAAW,EAAE,sBAAsB,KAAK,IAAI,CAAC;IAChE,iBAAiB,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,sBAAsB,KAAK,IAAI,CAAC;IACrF,UAAU,EAAE,CAAC,WAAW,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACpD,YAAY,EAAE,CAAC,WAAW,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACtD,mBAAmB,EAAE,CAAC,WAAW,EAAE,qBAAqB,KAAK,IAAI,CAAC;IAClE,qBAAqB,EAAE,CAAC,WAAW,EAAE,qBAAqB,KAAK,IAAI,CAAC;IACpE,wBAAwB,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,qBAAqB,KAAK,IAAI,CAAC;IAC/F,kBAAkB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IACjE,0BAA0B,EAAE,CAAC,YAAY,EAAE,MAAM,GAAG,SAAS,EAAE,WAAW,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IAC1F,sBAAsB,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;IAC/C,YAAY,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,eAAe,KAAK,IAAI,CAAC;IACnE,eAAe,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC;IACzC,qBAAqB,EAAE,CACrB,aAAa,EAAE,oBAAoB,EAAE,EACrC,WAAW,CAAC,EAAE,gBAAgB,KAC3B,IAAI,CAAC;IACV,iBAAiB,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC;IAC/C,wBAAwB,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,KAAK,IAAI,CAAC;IACzE,uBAAuB,EAAE,CACvB,WAAW,EAAE,sBAAsB,EACnC,WAAW,EAAE,KAAK,CAAC,WAAW,KAC3B,IAAI,CAAC;IACV,kCAAkC,EAAE,CAClC,WAAW,EAAE,sBAAsB,EACnC,MAAM,EAAE,gBAAgB,CAAC,gBAAgB,KACtC,IAAI,CAAC;IACV,iBAAiB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,eAAe,KAAK,IAAI,CAAC;IAClE,mBAAmB,EAAE,MAAM,IAAI,CAAC;IAChC,6BAA6B,EAAE,CAAC,eAAe,CAAC,EAAE,qBAAqB,KAAK,IAAI,CAAC;IACjF,8BAA8B,EAAE,CAC9B,WAAW,EAAE,sBAAsB,EACnC,MAAM,EAAE,gBAAgB,CAAC,kBAAkB,KACxC,IAAI,CAAC;IACV,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC;IACvE,oBAAoB,EAAE,CAAC,gBAAgB,EAAE,qBAAqB,KAAK,IAAI,CAAC;IACxE,WAAW,EAAE,CAAC,GAAG,EAAE,WAAW,KAAK,IAAI,CAAC;IACxC,MAAM,EAAE,MAAM,IAAI,CAAC;CACpB,CAAC"}