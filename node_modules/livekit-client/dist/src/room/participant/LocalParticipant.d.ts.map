{"version": 3, "file": "LocalParticipant.d.ts", "sourceRoot": "", "sources": ["../../../../src/room/participant/LocalParticipant.ts"], "names": [], "mappings": "AAAA,OAAO,EAKL,KAAK,EAKL,eAAe,EAahB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAC;AAEzD,OAAO,KAAK,SAAS,MAAM,cAAc,CAAC;AAC1C,OAAO,KAAK,yBAAyB,MAAM,mDAAmD,CAAC;AAC/F,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,sCAAsC,CAAC;AAW7E,OAAO,EAEL,KAAK,gBAAgB,EAErB,KAAK,iBAAiB,EAEvB,MAAM,QAAQ,CAAC;AAEhB,OAAO,UAAU,MAAM,qBAAqB,CAAC;AAC7C,OAAO,qBAAqB,MAAM,gCAAgC,CAAC;AAEnE,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AAEvC,OAAO,KAAK,EACV,mBAAmB,EACnB,gBAAgB,EAChB,wBAAwB,EACxB,yBAAyB,EACzB,mBAAmB,EACnB,mBAAmB,EACpB,MAAM,kBAAkB,CAAC;AAU1B,OAAO,EACL,KAAK,WAAW,EAChB,KAAK,kBAAkB,EACvB,KAAK,eAAe,EACpB,KAAK,eAAe,EACpB,KAAK,kBAAkB,EACvB,KAAK,iBAAiB,EACtB,KAAK,cAAc,EACpB,MAAM,UAAU,CAAC;AAkBlB,OAAO,WAAW,MAAM,eAAe,CAAC;AACxC,OAAO,KAAK,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAE/E,OAAO,KAAK,iBAAiB,MAAM,qBAAqB,CAAC;AAOzD,MAAM,CAAC,OAAO,OAAO,gBAAiB,SAAQ,WAAW;IACvD,sBAAsB,EAAE,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;IAE3D,sBAAsB,EAAE,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;IAE3D,+CAA+C;IAC/C,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;IAEtD,gBAAgB;IAChB,MAAM,EAAE,SAAS,CAAC;IAElB,gBAAgB;IAChB,eAAe,EAAE,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAE9C,OAAO,CAAC,iBAAiB,CAA2B;IAEpD,OAAO,CAAC,sBAAsB,CAAyD;IAEvF,OAAO,CAAC,gBAAgB,CAA4B;IAEpD,OAAO,CAAC,WAAW,CAAoB;IAEvC,OAAO,CAAC,eAAe,CAAoB;IAE3C,OAAO,CAAC,2BAA2B,CAAyC;IAE5E,OAAO,CAAC,iCAAiC,CAAiB;IAG1D,OAAO,CAAC,WAAW,CAAsB;IAEzC,OAAO,CAAC,cAAc,CAAyC;IAE/D,OAAO,CAAC,eAAe,CAAC,CAAe;IAEvC,OAAO,CAAC,qBAAqB,CAAC,CAAe;IAE7C,OAAO,CAAC,iBAAiB,CAAC,CAA4B;IAEtD,OAAO,CAAC,gBAAgB,CAAC,CAAoB;IAE7C,OAAO,CAAC,WAAW,CAA4D;IAE/E,OAAO,CAAC,6BAA6B,CAA4B;IAEjE,OAAO,CAAC,qBAAqB,CAO3B;IAEF,OAAO,CAAC,yBAAyB,CAAe;IAEhD,OAAO,CAAC,WAAW,CAA2E;IAE9F,OAAO,CAAC,gBAAgB,CAMpB;IAEJ,gBAAgB;gBAEd,GAAG,EAAE,MAAM,EACX,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,SAAS,EACjB,OAAO,EAAE,mBAAmB,EAC5B,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,EAC1E,6BAA6B,EAAE,yBAAyB;IAsB1D,IAAI,eAAe,IAAI,KAAK,GAAG,SAAS,CAEvC;IAED,IAAI,mBAAmB,IAAI,KAAK,GAAG,SAAS,CAE3C;IAED,IAAI,aAAa,IAAI,OAAO,CAE3B;IAED,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,GAAG,qBAAqB,GAAG,SAAS;IAO5E,yBAAyB,CAAC,IAAI,EAAE,MAAM,GAAG,qBAAqB,GAAG,SAAS;IAO1E;;OAEG;IACH,WAAW,CAAC,MAAM,EAAE,SAAS;IAgC7B,OAAO,CAAC,kBAAkB,CAIxB;IAEF,OAAO,CAAC,iBAAiB,CAIvB;IAEF,OAAO,CAAC,aAAa,CAcnB;IAEF,OAAO,CAAC,qBAAqB,CAS3B;IAEF,OAAO,CAAC,2BAA2B,CASjC;IAEF,OAAO,CAAC,gBAAgB,CAmBtB;IAEF;;;;;OAKG;IACG,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAIlD;;;;;OAKG;IACG,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI1C;;;;;OAKG;IACG,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;YAIxC,qBAAqB;IAmDnC;;;;;OAKG;IACH,gBAAgB,CACd,OAAO,EAAE,OAAO,EAChB,OAAO,CAAC,EAAE,mBAAmB,EAC7B,cAAc,CAAC,EAAE,mBAAmB,GACnC,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC;IAI7C;;;;;OAKG;IACH,oBAAoB,CAClB,OAAO,EAAE,OAAO,EAChB,OAAO,CAAC,EAAE,mBAAmB,EAC7B,cAAc,CAAC,EAAE,mBAAmB,GACnC,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC;IAI7C;;;OAGG;IACH,qBAAqB,CACnB,OAAO,EAAE,OAAO,EAChB,OAAO,CAAC,EAAE,yBAAyB,EACnC,cAAc,CAAC,EAAE,mBAAmB,GACnC,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC;IAI7C,gBAAgB;IACV,cAAc,CAAC,OAAO,EAAE,OAAO;IAKrC;;;;OAIG;YACW,eAAe;IAkJ7B;;;OAGG;IACG,yBAAyB;IAwB/B;;;;OAIG;IACG,YAAY,CAAC,OAAO,CAAC,EAAE,wBAAwB,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;IA0C7E;;;;OAIG;IACG,kBAAkB,CAAC,OAAO,CAAC,EAAE,yBAAyB,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAgDzF;;;;OAIG;IACG,YAAY,CAAC,KAAK,EAAE,UAAU,GAAG,gBAAgB,EAAE,OAAO,CAAC,EAAE,mBAAmB;YAIxE,uBAAuB;IA2KrC,OAAO,CAAC,wBAAwB;IAOhC,OAAO,CAAC,uBAAuB;YAuBjB,OAAO;IA2ZrB,IAAa,OAAO,IAAI,OAAO,CAE9B;IAED;;OAEG;IACG,8BAA8B,CAClC,KAAK,EAAE,UAAU,GAAG,gBAAgB,EACpC,UAAU,EAAE,gBAAgB,EAC5B,OAAO,CAAC,EAAE,mBAAmB;IAoFzB,cAAc,CAClB,KAAK,EAAE,UAAU,GAAG,gBAAgB,EACpC,eAAe,CAAC,EAAE,OAAO,GACxB,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC;IA8GvC,eAAe,CACnB,MAAM,EAAE,UAAU,EAAE,GAAG,gBAAgB,EAAE,GACxC,OAAO,CAAC,qBAAqB,EAAE,CAAC;IAK7B,kBAAkB,CAAC,OAAO,CAAC,EAAE,mBAAmB,EAAE,aAAa,GAAE,OAAc;IAkDrF;;;;;;OAMG;IACG,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,IAAI,CAAC;IAqBpF;;;;;OAKG;IACG,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAe7D,yDAAyD;IACnD,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC;IAsBpF,yDAAyD;IACnD,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,eAAe,EAAE,WAAW;;;;;;;IAqBpE;;;;;;OAMG;IACG,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,cAAc,CAAC;IAIhF;;;;;;;;OAQG;IACG,UAAU,CAAC,OAAO,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAIxE;;;;OAIG;IACG,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC;QAAE,EAAE,EAAE,MAAM,CAAA;KAAE,CAAC;IAI9E;;;;;OAKG;IACG,WAAW,CAAC,OAAO,CAAC,EAAE,kBAAkB;IAI9C;;;;;OAKG;IACG,UAAU,CAAC,EACf,mBAAmB,EACnB,MAAM,EACN,OAAO,EACP,eAAuB,GACxB,EAAE,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC;IAiErC;;OAEG;IACH,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,OAAO,CAAC,MAAM,CAAC;IAUvF;;OAEG;IACH,mBAAmB,CAAC,MAAM,EAAE,MAAM;IAIlC;;;;;;;;;;;;;;;;OAgBG;IACH,+BAA+B,CAC7B,sBAAsB,EAAE,OAAO,EAC/B,2BAA2B,GAAE,0BAA0B,EAAO;IAShE,OAAO,CAAC,oBAAoB;IAU5B,OAAO,CAAC,yBAAyB;IAcjC,gBAAgB;YACF,iBAAiB;IAyB/B,gBAAgB;IAChB,6BAA6B,CAAC,mBAAmB,EAAE,MAAM;IAezD,gBAAgB;IAChB,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE;IAMvC,gBAAgB;IAChB,UAAU,CAAC,IAAI,EAAE,eAAe,GAAG,OAAO;IA0B1C,OAAO,CAAC,kCAAkC,CAUxC;IAEF,gBAAgB;IAChB,cAAc,CAAC,KAAK,EAAE,iBAAiB,GAAG,SAAS;IAanD,OAAO,CAAC,2BAA2B;IAUnC,gBAAgB;IAChB,OAAO,CAAC,cAAc,CAEpB;IAGF,gBAAgB;IAChB,OAAO,CAAC,YAAY,CAclB;IAEF,OAAO,CAAC,qBAAqB,CAM3B;IAEF,OAAO,CAAC,sBAAsB,CAM5B;IAEF,OAAO,CAAC,oBAAoB,CAU1B;IAEF,OAAO,CAAC,qBAAqB,CAM3B;IAEF,OAAO,CAAC,6BAA6B,CAyBnC;IAEF,OAAO,CAAC,2BAA2B,CAUjC;IAEF,OAAO,CAAC,gBAAgB,CA8DtB;IAEF,OAAO,CAAC,sBAAsB;YAwBhB,iCAAiC;CAchD"}