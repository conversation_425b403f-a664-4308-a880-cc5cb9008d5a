{"version": 3, "file": "RTCEngine.d.ts", "sourceRoot": "", "sources": ["../../../src/room/RTCEngine.ts"], "names": [], "mappings": "AACA,OAAO,EACL,KAAK,eAAe,EAGpB,KAAK,uBAAuB,EAG5B,UAAU,EACV,eAAe,EACf,gBAAgB,EAChB,KAAK,YAAY,EAGjB,eAAe,EAGf,eAAe,EACf,IAAI,IAAI,SAAS,EACjB,iBAAiB,EAIjB,WAAW,EACX,KAAK,iBAAiB,EACtB,uBAAuB,EACvB,KAAK,4BAA4B,EACjC,KAAK,oBAAoB,EAEzB,SAAS,EAET,wBAAwB,EACxB,aAAa,EAGd,MAAM,mBAAmB,CAAC;AAG3B,OAAO,KAAK,iBAAiB,MAAM,eAAe,CAAC;AACnD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,EACL,YAAY,EAGb,MAAM,qBAAqB,CAAC;AAE7B,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAGtD,OAAO,WAAyB,MAAM,eAAe,CAAC;AACtD,OAAO,EAAE,kBAAkB,EAAoB,MAAM,sBAAsB,CAAC;AAE5E,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAU7D,OAAO,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAEjC,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,qBAAqB,MAAM,+BAA+B,CAAC;AACvE,OAAO,eAAe,MAAM,yBAAyB,CAAC;AACtD,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAClE,OAAO,KAAK,sBAAsB,MAAM,gCAAgC,CAAC;AACzE,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,KAAK,EAAE,mBAAmB,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;8BA2Bf,UAAU,iBAAiB,CAAC,oBAAoB,CAAC;AADzG,gBAAgB;AAChB,MAAM,CAAC,OAAO,OAAO,SAAU,SAAQ,cAAmE;IAkG5F,OAAO,CAAC,OAAO;IAjG3B,MAAM,EAAE,YAAY,CAAC;IAErB,SAAS,EAAE,gBAAgB,CAAM;IAEjC,qBAAqB,EAAE,MAAM,CAAmD;IAEhF,mBAAmB,EAAE,OAAO,CAAS;IAErC,SAAS,CAAC,EAAE,kBAAkB,CAAC;IAE/B;;OAEG;IACH,kBAAkB,CAAC,EAAE,YAAY,CAAC;IAElC;;OAEG;IACH,mBAAmB,EAAE,MAAM,CAAK;IAEhC,IAAI,QAAQ,YAEX;IAED,IAAI,gBAAgB,YAEnB;IAED,OAAO,CAAC,OAAO,CAAC,CAAiB;IAGjC,OAAO,CAAC,UAAU,CAAC,CAAiB;IAEpC,OAAO,CAAC,UAAU,CAAC,CAAiB;IAEpC,OAAO,CAAC,cAAc,CAAgC;IAGtD,OAAO,CAAC,aAAa,CAAC,CAAiB;IAEvC,OAAO,CAAC,iBAAiB,CAAkB;IAE3C,OAAO,CAAC,OAAO,CAAwB;IAEvC,OAAO,CAAC,SAAS,CAAiB;IAElC,OAAO,CAAC,qBAAqB,CAEtB;IAGP,OAAO,CAAC,GAAG,CAAC,CAAS;IAErB,OAAO,CAAC,KAAK,CAAC,CAAS;IAEvB,OAAO,CAAC,UAAU,CAAC,CAAgB;IAEnC,OAAO,CAAC,iBAAiB,CAAa;IAEtC,OAAO,CAAC,cAAc,CAAa;IAEnC,OAAO,CAAC,mBAAmB,CAAC,CAAsB;IAElD,OAAO,CAAC,mBAAmB,CAAkB;IAE7C,OAAO,CAAC,eAAe,CAAkB;IAEzC,OAAO,CAAC,gBAAgB,CAAC,CAAgC;IAEzD,OAAO,CAAC,cAAc,CAAC,CAAS;IAEhC,yEAAyE;IACzE,OAAO,CAAC,YAAY,CAAa;IAEjC,yEAAyE;IACzE,OAAO,CAAC,eAAe,CAAa;IAEpC,OAAO,CAAC,WAAW,CAAQ;IAE3B,OAAO,CAAC,eAAe,CAAQ;IAE/B,OAAO,CAAC,cAAc,CAAkB;IAExC,OAAO,CAAC,iBAAiB,CAAC,CAAoB;IAE9C,OAAO,CAAC,GAAG,CAAO;IAElB,OAAO,CAAC,aAAa,CAAgB;IAErC,OAAO,CAAC,0BAA0B,CAA4B;IAE9D,OAAO,CAAC,oBAAoB,CAAa;IAEzC,OAAO,CAAC,qBAAqB,CAA0B;IAEvD,OAAO,CAAC,qBAAqB,CAA8D;gBAEvE,OAAO,EAAE,mBAAmB;IAgChD,gBAAgB;IAChB,IAAI,UAAU;;;;;MAOb;IAEK,IAAI,CACR,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,aAAa,EACnB,WAAW,CAAC,EAAE,WAAW,GACxB,OAAO,CAAC,YAAY,CAAC;IA0ClB,KAAK;IAoBL,sBAAsB;IA4BtB,aAAa;IAKnB,QAAQ,CAAC,GAAG,EAAE,eAAe,GAAG,OAAO,CAAC,SAAS,CAAC;IA4BlD;;;;;OAKG;IACH,WAAW,CAAC,MAAM,EAAE,YAAY,GAAG,OAAO;IAiB1C,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;IAIjD,IAAI,wBAAwB,IAAI,MAAM,GAAG,SAAS,CAEjD;IAEK,yBAAyB,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAK9D,oBAAoB,CAAC,QAAQ,EAAE,iBAAiB;YAIlC,SAAS;IA4EvB,OAAO,CAAC,0BAA0B;IA0GlC,OAAO,CAAC,oBAAoB;IAyC5B,OAAO,CAAC,kBAAkB;IAyC1B,OAAO,CAAC,iBAAiB,CAavB;IAEF,OAAO,CAAC,iBAAiB,CAsCvB;IAEF,OAAO,CAAC,eAAe,CAarB;IAEF,OAAO,CAAC,uBAAuB,CAM7B;IAEI,YAAY,CAChB,KAAK,EAAE,UAAU,EACjB,IAAI,EAAE,mBAAmB,EACzB,SAAS,CAAC,EAAE,wBAAwB,EAAE;IAclC,qBAAqB,CACzB,KAAK,EAAE,eAAe,EACtB,cAAc,EAAE,kBAAkB,EAClC,IAAI,EAAE,mBAAmB,EACzB,SAAS,CAAC,EAAE,wBAAwB,EAAE;YAc1B,6BAA6B;YAgC7B,gCAAgC;YAyBhC,kBAAkB;IAUhC,OAAO,CAAC,gBAAgB,CA+CtB;YAEY,gBAAgB;IAwD9B,OAAO,CAAC,iBAAiB;YAWX,iBAAiB;YAiEjB,gBAAgB;IAwExB,0BAA0B,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,eAAe,CAAC,EAAE,eAAe;YAOtE,oBAAoB;IAqBlC,gBAAgB,sBAgBd;IAEF,gBAAgB;IACV,kBAAkB,CACtB,mBAAmB,EAAE,MAAM,EAC3B,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,GAAG,IAAI,EACtB,KAAK,EAAE,QAAQ,GAAG,IAAI;IAmBxB,gBAAgB;IACV,aAAa,CAAC,mBAAmB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;IAgB5D,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,eAAe;YAyBhD,+BAA+B;IAY7C,OAAO,CAAC,2BAA2B,CAMjC;IAEF,OAAO,CAAC,iBAAiB,CAQvB;IAEF,sBAAsB,CAAC,IAAI,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IAgB5D;;OAEG;IACG,4BAA4B,CAChC,IAAI,EAAE,eAAe,EACrB,UAAU,GAAE,OAAgC;YAwDhC,wBAAwB;IAQtC,eAAe,IAAI,OAAO;IAgB1B,gBAAgB;IACV,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC;IA6DhC,kBAAkB,CAAC,IAAI,EAAE,eAAe,EAAE,GAAG,CAAC,EAAE,OAAO,GAAG,cAAc,GAAG,SAAS;IAkBpF,gBAAgB;IAChB,aAAa,CAAC,YAAY,EAAE,sBAAsB,EAAE,EAAE,WAAW,EAAE,qBAAqB,EAAE;IA2D1F,QAAQ;IAKR,OAAO,CAAC,gBAAgB;IAoBxB,OAAO,CAAC,qBAAqB;IAM7B,OAAO,CAAC,qBAAqB;IAK7B,OAAO,CAAC,mBAAmB,CAMzB;IAEF,OAAO,CAAC,sBAAsB;IAM9B,OAAO,CAAC,wBAAwB;CAKjC;AAID,MAAM,MAAM,oBAAoB,GAAG;IACjC,SAAS,EAAE,CAAC,QAAQ,EAAE,YAAY,KAAK,IAAI,CAAC;IAC5C,YAAY,EAAE,CAAC,MAAM,CAAC,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAClD,QAAQ,EAAE,MAAM,IAAI,CAAC;IACrB,OAAO,EAAE,MAAM,IAAI,CAAC;IACpB,UAAU,EAAE,MAAM,IAAI,CAAC;IACvB,SAAS,EAAE,MAAM,IAAI,CAAC;IACtB,aAAa,EAAE,MAAM,IAAI,CAAC;IAC1B,eAAe,EAAE,CAAC,QAAQ,EAAE,YAAY,KAAK,IAAI,CAAC;IAClD,OAAO,EAAE,MAAM,IAAI,CAAC;IACpB,eAAe,EAAE,CACf,KAAK,EAAE,gBAAgB,EACvB,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,cAAc,KACrB,IAAI,CAAC;IACV,oBAAoB,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC;IAC7D,kBAAkB,EAAE,CAAC,MAAM,EAAE,UAAU,KAAK,IAAI,CAAC;IACjD,qBAAqB,EAAE,CAAC,aAAa,EAAE,aAAa,KAAK,IAAI,CAAC;IAC9D,iBAAiB,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,KAAK,IAAI,CAAC;IAC7E,gBAAgB;IAChB,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,KAAK,IAAI,CAAC;IAC/D,iBAAiB,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;IAC7D,qBAAqB,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,eAAe,KAAK,IAAI,CAAC;IACvE,iBAAiB,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,KAAK,IAAI,CAAC;IACtD,UAAU,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK,IAAI,CAAC;IACtC,SAAS,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC7C,uBAAuB,EAAE,CAAC,MAAM,EAAE,uBAAuB,KAAK,IAAI,CAAC;IACnE,eAAe,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,KAAK,IAAI,CAAC;IACzD,kBAAkB,EAAE,CAAC,MAAM,EAAE,iBAAiB,KAAK,IAAI,CAAC;IACxD,iBAAiB,EAAE,CAAC,IAAI,EAAE,oBAAoB,KAAK,IAAI,CAAC;IACxD,4BAA4B,EAAE,CAAC,MAAM,EAAE,4BAA4B,KAAK,IAAI,CAAC;IAC7E,uBAAuB,EAAE,CAAC,MAAM,EAAE,uBAAuB,KAAK,IAAI,CAAC;IACnE,qBAAqB,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,KAAK,IAAI,CAAC;IAC/E,oBAAoB,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IACjD,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC;IACvD,OAAO,EAAE,MAAM,IAAI,CAAC;IACpB,qBAAqB,EAAE,CAAC,QAAQ,EAAE,eAAe,KAAK,IAAI,CAAC;IAC3D,eAAe,EAAE,CAAC,QAAQ,EAAE,YAAY,KAAK,IAAI,CAAC;CACnD,CAAC"}