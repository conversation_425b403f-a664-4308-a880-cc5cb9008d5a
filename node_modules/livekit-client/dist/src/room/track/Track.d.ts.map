{"version": 3, "file": "Track.d.ts", "sourceRoot": "", "sources": ["../../../../src/room/track/Track.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,iBAAiB,EAEjB,WAAW,IAAI,gBAAgB,EAC/B,WAAW,EACX,SAAS,EACV,MAAM,mBAAmB,CAAC;AAE3B,OAAO,KAAK,iBAAiB,MAAM,eAAe,CAAC;AACnD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAY,EAAe,KAAK,gBAAgB,EAAa,MAAM,cAAc,CAAC;AAElF,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAE9C,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AASxD,oBAAY,YAAY;IACtB,GAAG,IAAmB;IACtB,MAAM,IAAsB;IAC5B,IAAI,IAAoB;CACzB;0BAG0B,UAAU,iBAAiB,CAAC,mBAAmB,CAAC;AAF3E,8BAAsB,KAAK,CACzB,SAAS,SAAS,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CACzC,SAAQ,UAAkE;IAC1E,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC;IAEzB,gBAAgB,EAAE,gBAAgB,EAAE,CAAM;IAE1C,OAAO,EAAE,OAAO,CAAS;IAEzB,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC;IAErB,OAAO,CAAC,YAAY,CAA+C;IAEnE;;OAEG;IACH,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC;IAEhB;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B;;;OAGG;IACH,IAAI,WAAW,IAAI,KAAK,CAAC,WAAW,CAEnC;IAED,gBAAgB;IAChB,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,WAAW;IAIvC,gBAAgB;IAChB,YAAY,EAAE,MAAM,GAAG,SAAS,CAAC;IAEjC,SAAS,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IAE9C,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC;IAEjC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAS;IAE1C,OAAO,CAAC,iBAAiB,CAA4C;IAErE,OAAO,CAAC,eAAe,CAAmC;IAE1D,SAAS,CAAC,cAAc,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7C,SAAS,CAAC,eAAe,EAAE,MAAM,CAAK;IAEtC,SAAS,CAAC,eAAe,CAAC,EAAE,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC;IAE3D,SAAS,CAAC,GAAG,EAAE,gBAAgB,CAAO;IAEtC,SAAS,aACP,UAAU,EAAE,gBAAgB,EAC5B,IAAI,EAAE,SAAS,EACf,aAAa,GAAE,aAAkB;IAanC,SAAS,KAAK,UAAU;;MAKvB;IAED,sCAAsC;IACtC,IAAI,cAAc,IAAI,MAAM,CAE3B;IAED,IAAI,gBAAgB,qBAEnB;IAED,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC;IAEhC;;;;OAIG;IACH,IAAI,aAAa,IAAI,MAAM,CAE1B;IAED;;OAEG;IACH,MAAM,IAAI,gBAAgB;IAE1B;;OAEG;IACH,MAAM,CAAC,OAAO,EAAE,gBAAgB,GAAG,gBAAgB;IA0EnD;;OAEG;IACH,MAAM,IAAI,gBAAgB,EAAE;IAE5B;;;OAGG;IACH,MAAM,CAAC,OAAO,EAAE,gBAAgB,GAAG,gBAAgB;IAiCnD,IAAI;IAKJ,SAAS,CAAC,MAAM;IAIhB,SAAS,CAAC,OAAO;IAKjB,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,YAAY,GAAG,IAAI;IAGxD,WAAW;IASX,gBAAgB;IAChB,mBAAmB,CAAC,aAAa,EAAE,aAAa;IAShD,OAAO,CAAC,cAAc;IAgBtB,SAAS,CAAC,4BAA4B,aAcpC;cAEc,0BAA0B;IAe1C,SAAS,CAAC,wBAAwB;IASlC,SAAS,CAAC,2BAA2B;CAKtC;AAED,wBAAgB,eAAe,CAAC,KAAK,EAAE,gBAAgB,EAAE,OAAO,EAAE,gBAAgB,QAuDjF;AAED,gBAAgB;AAChB,wBAAgB,WAAW,CAAC,KAAK,EAAE,gBAAgB,EAAE,OAAO,EAAE,gBAAgB,QAU7E;AAED,yBAAiB,KAAK,CAAC;IACrB,KAAY,IAAI;QACd,KAAK,UAAU;QACf,KAAK,UAAU;QACf,OAAO,YAAY;KACpB;IACD,KAAY,GAAG,GAAG,MAAM,CAAC;IACzB,KAAY,MAAM;QAChB,MAAM,WAAW;QACjB,UAAU,eAAe;QACzB,WAAW,iBAAiB;QAC5B,gBAAgB,uBAAuB;QACvC,OAAO,YAAY;KACpB;IAED,KAAY,WAAW;QACrB,MAAM,WAAW;QACjB,MAAM,WAAW;QACjB,OAAO,YAAY;KACpB;IAED,UAAiB,UAAU;QACzB,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;KAChB;IAED,gBAAgB;IAChB,SAAgB,WAAW,CAAC,CAAC,EAAE,IAAI,GAAG,SAAS,CAU9C;IAED,gBAAgB;IAChB,SAAgB,aAAa,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAS5D;IAED,gBAAgB;IAChB,SAAgB,aAAa,CAAC,CAAC,EAAE,MAAM,GAAG,WAAW,CAapD;IAED,gBAAgB;IAChB,SAAgB,eAAe,CAAC,CAAC,EAAE,WAAW,GAAG,MAAM,CAatD;IAED,gBAAgB;IAChB,SAAgB,oBAAoB,CAAC,CAAC,EAAE,gBAAgB,GAAG,WAAW,CASrE;CACF;AAED,MAAM,MAAM,mBAAmB,GAAG;IAChC,OAAO,EAAE,MAAM,IAAI,CAAC;IACpB,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IAC7B,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IAC/B,SAAS,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IACjC,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IAC7B,cAAc,EAAE,MAAM,IAAI,CAAC;IAC3B,kBAAkB,EAAE,MAAM,IAAI,CAAC;IAC/B,oBAAoB,EAAE,MAAM,IAAI,CAAC;IACjC,mBAAmB,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC;IAC7C,oBAAoB,EAAE,MAAM,IAAI,CAAC;IACjC,iBAAiB,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IAC3D,sBAAsB,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IAC5E,oBAAoB,EAAE,MAAM,IAAI,CAAC;IACjC,mBAAmB,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC;IAC7C,eAAe,EAAE,CAAC,OAAO,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACrD,eAAe,EAAE,CAAC,OAAO,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACrD,cAAc,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,CAAC;IACrC,eAAe,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,CAAC;IACtC,oBAAoB,EAAE,CAAC,SAAS,CAAC,EAAE,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC;IAC5E,uBAAuB,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IAC5F,cAAc,EAAE,CAAC,MAAM,EAAE;QAAE,SAAS,EAAE,MAAM,CAAC;QAAC,YAAY,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,CAAC;IAC9E,uBAAuB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,IAAI,CAAC;IACxD,cAAc,EAAE,MAAM,IAAI,CAAC;CAC5B,CAAC"}