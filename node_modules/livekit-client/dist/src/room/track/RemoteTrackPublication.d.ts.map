{"version": 3, "file": "RemoteTrackPublication.d.ts", "sourceRoot": "", "sources": ["../../../../src/room/track/RemoteTrackPublication.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,iBAAiB,EACjB,SAAS,EAGV,MAAM,mBAAmB,CAAC;AAE3B,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAE9C,OAAO,KAAK,WAAW,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAC9C,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAGtD,MAAM,CAAC,OAAO,OAAO,sBAAuB,SAAQ,gBAAgB;IAClE,KAAK,CAAC,EAAE,WAAW,CAAa;IAEhC,gBAAgB;IAChB,SAAS,CAAC,OAAO,UAAQ;IAGzB,SAAS,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;IAE/B,SAAS,CAAC,iBAAiB,EAAE,OAAO,GAAG,SAAS,CAAa;IAE7D,SAAS,CAAC,OAAO,EAAE,OAAO,CAAQ;IAElC,SAAS,CAAC,6BAA6B,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;IAE3D,SAAS,CAAC,wBAAwB,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;IAEtD,SAAS,CAAC,mBAAmB,CAAC,EAAE,YAAY,CAAC;IAE7C,SAAS,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC;IAEvB,SAAS,CAAC,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;gBAG9C,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,EAAE,EAAE,SAAS,EACb,aAAa,EAAE,OAAO,GAAG,SAAS,EAClC,aAAa,CAAC,EAAE,aAAa;IAO/B;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,OAAO;IA2BjC,IAAI,kBAAkB,IAAI,gBAAgB,CAAC,kBAAkB,CAQ5D;IAED,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,gBAAgB,CAIxD;IAED;;OAEG;IACH,IAAI,YAAY,IAAI,OAAO,CAK1B;IAGD,IAAI,SAAS,IAAI,OAAO,CAEvB;IAED,IAAI,SAAS,IAAI,OAAO,CAMvB;IAED,IAAI,OAAO,YAEV;IAED;;;;;OAKG;IACH,UAAU,CAAC,OAAO,EAAE,OAAO;IAS3B;;;;;;OAMG;IACH,eAAe,CAAC,OAAO,EAAE,YAAY;IAUrC;;;;;;OAMG;IACH,kBAAkB,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU;IAkB/C,WAAW,CAAC,GAAG,EAAE,MAAM;IAiBvB,IAAI,YAAY,IAAI,YAAY,GAAG,SAAS,CAE3C;IAED,gBAAgB;IAChB,QAAQ,CAAC,KAAK,CAAC,EAAE,WAAW;IA4B5B,gBAAgB;IAChB,UAAU,CAAC,OAAO,EAAE,OAAO;IAQ3B,gBAAgB;IAChB,oBAAoB,CAAC,KAAK,EAAE,iBAAiB;IAI7C,gBAAgB;IAChB,UAAU,CAAC,IAAI,EAAE,SAAS;IAW1B,OAAO,CAAC,+BAA+B;IAQvC,OAAO,CAAC,6BAA6B;IAarC,OAAO,CAAC,wBAAwB;IAQhC,SAAS,CAAC,WAAW,GAAI,OAAO,WAAW,UAGzC;IAEF,SAAS,KAAK,gBAAgB,IAAI,OAAO,CAExC;IAED,SAAS,CAAC,sBAAsB,GAAI,SAAS,OAAO,UAOlD;IAEF,SAAS,CAAC,2BAA2B,GAAI,YAAY,KAAK,CAAC,UAAU,UAOnE;IAGF,eAAe;CAoEhB"}