{"version": 3, "file": "options.d.ts", "sourceRoot": "", "sources": ["../../../../src/room/track/options.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACrC,OAAO,KAAK,EACV,qBAAqB,EACrB,cAAc,EACd,qBAAqB,EACtB,MAAM,mBAAmB,CAAC;AAE3B,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC;IAE9B;;;;;;;;;OASG;IACH,WAAW,CAAC,EAAE,IAAI,GAAG,KAAK,GAAG;QAAE,KAAK,EAAE,gBAAgB,CAAC;QAAC,QAAQ,CAAC,EAAE,aAAa,CAAA;KAAE,CAAC;IAEnF;;;;;;;;;;;OAWG;IACH,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IAEtC;;OAEG;IACH,mBAAmB,CAAC,EAAE,aAAa,CAAC;IAEpC;;;OAGG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC;IAExB;;;OAGG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IAEd;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IAEd;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;;;OAIG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IAEpB;;;OAGG;IACH,eAAe,CAAC,EAAE,eAAe,CAAC;IAElC;;OAEG;IACH,qBAAqB,CAAC,EAAE,wBAAwB,CAAC;IAEjD;;;;;;;;;;;;;;OAcG;IACH,oBAAoB,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;IAE1C;;;OAGG;IACH,0BAA0B,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;IAEhD;;;;;;;OAOG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED;;GAEG;AACH,MAAM,WAAW,mBAAoB,SAAQ,oBAAoB;IAC/D;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC;IAEtB;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,wBAAwB;IACvC;;;OAGG;IACH,KAAK,CAAC,EAAE,OAAO,GAAG,mBAAmB,CAAC;IAEtC;;;OAGG;IACH,KAAK,CAAC,EAAE,OAAO,GAAG,mBAAmB,CAAC;CACvC;AAED,MAAM,WAAW,mBAAmB;IAClC;;;OAGG;IACH,QAAQ,CAAC,EAAE,kBAAkB,CAAC;IAE9B;;OAEG;IACH,SAAS,CAAC,EAAE,eAAe,CAAC;IAE5B;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,aAAa,GAAG,MAAM,GAAG,OAAO,CAAC;IAEvD,UAAU,CAAC,EAAE,eAAe,CAAC;IAE7B;;OAEG;IACH,SAAS,CAAC,EAAE,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;CACrE;AAED,MAAM,WAAW,yBAAyB;IACxC;;;OAGG;IACH,KAAK,CAAC,EAAE,OAAO,GAAG,mBAAmB,CAAC;IAEtC;;;OAGG;IACH,KAAK,CAAC,EAAE,IAAI,GAAG;QAAE,cAAc,CAAC,EAAE,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAA;KAAE,CAAC;IAErE;;;;;OAKG;IACH,UAAU,CAAC,EAAE,eAAe,CAAC;IAE7B,qIAAqI;IACrI,UAAU,CAAC,EAAE,OAAO,CAAC;IAErB,gGAAgG;IAChG,kBAAkB,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;IAE3C,2IAA2I;IAC3I,gBAAgB,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;IAEzC,yHAAyH;IACzH,WAAW,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;IAEpC,oGAAoG;IACpG,WAAW,CAAC,EAAE,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC;IAE3C;;;OAGG;IACH,0BAA0B,CAAC,EAAE,OAAO,CAAC;IAErC;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,eAAe,CAAC,EAAE,gBAAgB,CAAC;IAEnC;;OAEG;IACH,YAAY,CAAC,EAAE,cAAc,CAAC;IAE9B;;;OAGG;IACH,QAAQ,CAAC,EAAE,kBAAkB,CAAC;IAE9B;;OAEG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IAEpC;;OAEG;IACH,OAAO,CAAC,EAAE,eAAe,CAAC;IAE1B;;OAEG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IAEpC;;;;;OAKG;IACH,cAAc,CAAC,EAAE,gBAAgB,CAAC;IAElC;;OAEG;IACH,UAAU,CAAC,EAAE,cAAc,CAAC;IAE5B;;OAEG;IACH,UAAU,CAAC,EAAE,cAAc,CAAC;IAE5B;;OAEG;IACH,SAAS,CAAC,EAAE,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;CACrE;AAED,MAAM,WAAW,kBAAkB;IACjC;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,eAAe;IAC9B,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,aAAa;IAC5B,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,eAAe,CAAC;CAC5B;AAED,MAAM,WAAW,kBAAkB;IACjC,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,eAAe,CAAC;CAC5B;AAED,qBAAa,WAAW;IACtB,QAAQ,EAAE,aAAa,CAAC;IAExB,KAAK,EAAE,MAAM,CAAC;IAEd,MAAM,EAAE,MAAM,CAAC;IAEf,WAAW,CAAC,EAAE,MAAM,CAAC;gBAET,kBAAkB,EAAE,kBAAkB;gBAEhD,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,MAAM,EAClB,YAAY,CAAC,EAAE,MAAM,EACrB,QAAQ,CAAC,EAAE,eAAe;IAgC5B,IAAI,UAAU,IAAI,eAAe,CAOhC;CACF;AAED,MAAM,WAAW,WAAW;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,eAAe,CAAC;CAC5B;AAED,QAAA,MAAM,YAAY,0BAA2B,CAAC;AAE9C,eAAO,MAAM,WAAW,gDAAiD,CAAC;AAE1E,MAAM,MAAM,UAAU,GAAG,CAAC,OAAO,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AAEtD,MAAM,MAAM,gBAAgB,GAAG,CAAC,OAAO,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;AAE7D,wBAAgB,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,KAAK,IAAI,gBAAgB,CAEtE;AAED,oBAAY,iBAAiB;IAE3B,iBAAiB,IAAI;IAErB,SAAS,IAAI;IAEb,UAAU,IAAI;CACf;AAED;;GAEG;AACH,MAAM,MAAM,eAAe,GACvB,MAAM,GACN,MAAM,GACN,MAAM,GACN,MAAM,GACN,OAAO,GACP,UAAU,GACV,MAAM,GACN,OAAO,GACP,UAAU,GACV,MAAM,GACN,OAAO,GACP,UAAU,GACV,MAAM,GACN,OAAO,GACP,UAAU,GACV,MAAM,GACN,OAAO,GACP,UAAU,GACV,MAAM,GACN,OAAO,GACP,UAAU,CAAC;AAEf,yBAAiB,YAAY,CAAC;IACrB,MAAM,SAAS,EAAE,WAEvB,CAAC;IACK,MAAM,MAAM,EAAE,WAEpB,CAAC;IACK,MAAM,KAAK,EAAE,WAEnB,CAAC;IACK,MAAM,WAAW,EAAE,WAEzB,CAAC;IACK,MAAM,gBAAgB,EAAE,WAE9B,CAAC;IACK,MAAM,sBAAsB,EAAE,WAEpC,CAAC;CACH;AAED;;GAEG;AACH,eAAO,MAAM,YAAY;;;;;;;;;;CAUf,CAAC;AAEX;;GAEG;AACH,eAAO,MAAM,cAAc;;;;;;;;;;CAUjB,CAAC;AAEX,eAAO,MAAM,kBAAkB;;;;;;;;;CAUrB,CAAC"}