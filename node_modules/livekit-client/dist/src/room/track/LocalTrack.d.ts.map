{"version": 3, "file": "LocalTrack.d.ts", "sourceRoot": "", "sources": ["../../../../src/room/track/LocalTrack.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AAMvC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAE9C,OAAO,EAAE,KAAK,EAAgC,MAAM,SAAS,CAAC;AAC9D,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAC5C,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAwB,MAAM,UAAU,CAAC;AACpE,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAKnD,MAAM,CAAC,OAAO,CAAC,QAAQ,OAAO,UAAU,CACtC,SAAS,SAAS,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CACzC,SAAQ,KAAK,CAAC,SAAS,CAAC;IACxB,SAAS,CAAC,OAAO,CAAC,EAAE,YAAY,CAAC;IAEjC,OAAO,CAAC,wBAAwB,CAA4C;IAE5E,gBAAgB;IAChB,IAAI,MAAM,IAAI,YAAY,GAAG,SAAS,CAErC;IAED,gBAAgB;IAChB,IAAI,MAAM,CAAC,MAAM,EAAE,YAAY,GAAG,SAAS,EAE1C;IAED,gBAAgB;IAChB,KAAK,CAAC,EAAE,UAAU,CAAC;IAEnB,IAAI,WAAW,0BAEd;IAED,IAAI,mBAAmB,YAEtB;IAED,SAAS,CAAC,YAAY,EAAE,qBAAqB,CAAC;IAE9C,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC;IAElC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC;IAElC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC;IAE1B,SAAS,CAAC,iBAAiB,EAAE,KAAK,CAAC;IAEnC,SAAS,CAAC,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IAE9C,SAAS,CAAC,SAAS,CAAC,EAAE,cAAc,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAErD,SAAS,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC;IAEtC,SAAS,CAAC,eAAe,EAAE,OAAO,CAAS;IAE3C,SAAS,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,OAAO,IAAI,CAAC,GAAG,SAAS,CAAC;IAE1E,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC;IAEjC;;;;;;OAMG;IACH,SAAS,aACP,UAAU,EAAE,gBAAgB,EAC5B,IAAI,EAAE,SAAS,EACf,WAAW,CAAC,EAAE,qBAAqB,EACnC,iBAAiB,UAAQ,EACzB,aAAa,CAAC,EAAE,aAAa;IAuB/B,IAAI,EAAE,IAAI,MAAM,CAEf;IAED,IAAI,UAAU,IAAI,KAAK,CAAC,UAAU,GAAG,SAAS,CAa7C;IAED,OAAO,CAAC,iBAAiB,CAAkB;IAE3C,IAAI,gBAAgB,YAEnB;IAED,IAAI,cAAc,YAEjB;IAED,IAAI,gBAAgB,qBAEnB;IAED,IAAI,OAAO,YAEV;IAED;;;OAGG;IACH,sBAAsB;YAIR,mBAAmB;IAkE3B,iBAAiB,CAAC,OAAO,SAA6B,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;IAsBlF,WAAW,CAAC,QAAQ,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAqBjE,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAE3D;;OAEG;IACG,WAAW,CAAC,SAAS,UAAO,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAa1D,IAAI;IAKJ,MAAM;IAKN,YAAY,CAAC,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,mBAAmB,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC;IAC1F,YAAY,CAAC,KAAK,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC;cAqC9E,OAAO,CAAC,WAAW,CAAC,EAAE,qBAAqB;IA2D3D,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO;IAYtC,SAAS,KAAK,kBAAkB,IAAI,OAAO,CAO1C;cAEe,0BAA0B;IAY1C,OAAO,CAAC,oBAAoB,CAGxB;IAEJ,OAAO,CAAC,yBAAyB,CAExB;IAET,OAAO,CAAC,sBAAsB,CAG5B;IAEF,OAAO,CAAC,WAAW,CAOjB;IAEF,IAAI;IAWJ;;;;;QAKI;IACE,aAAa;IA0Bb,cAAc;IAsBpB;;;;;OAKG;IACG,iBAAiB,IAAI,OAAO,CAAC,cAAc,GAAG,SAAS,CAAC;IAQ9D;;;;;;;;;OASG;IACG,YAAY,CAAC,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC,EAAE,0BAA0B,UAAO;IA4D1F,YAAY;IAIZ;;;;;;OAMG;IACG,aAAa,CAAC,WAAW,UAAO;IAStC;;;;OAIG;cACa,qBAAqB,CAAC,WAAW,UAAO;IAiBxD,gBAAgB;IAChB,qBAAqB,CAAC,SAAS,GAAE,MAAY;IAiC7C,gBAAgB;IAChB,oBAAoB;IAQpB,gBAAgB;IAChB,mBAAmB;IAInB,2BAA2B;IAI3B,SAAS,CAAC,QAAQ,CAAC,aAAa,IAAI,IAAI;CACzC"}