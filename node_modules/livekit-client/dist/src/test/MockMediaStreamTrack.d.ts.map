{"version": 3, "file": "MockMediaStreamTrack.d.ts", "sourceRoot": "", "sources": ["../../../src/test/MockMediaStreamTrack.ts"], "names": [], "mappings": "AAEA,MAAM,CAAC,OAAO,OAAO,oBAAqB,YAAW,gBAAgB;IACnE,WAAW,EAAE,MAAM,CAAM;IAEzB,OAAO,EAAE,OAAO,CAAQ;IAExB,EAAE,EAAE,MAAM,CAAQ;IAElB,IAAI,EAAE,MAAM,CAAW;IAEvB,KAAK,EAAE,MAAM,CAAW;IAExB,KAAK,EAAE,OAAO,CAAS;IAEvB,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAAQ;IAEpE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAAQ;IAEnE,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAAQ;IAErE,UAAU,EAAE,qBAAqB,CAAU;IAE3C,QAAQ,EAAE,OAAO,CAAS;IAE1B,iBAAiB,EAAE,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAAQ;IAG9E,gBAAgB,CAAC,WAAW,CAAC,EAAE,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC;IAIpE,KAAK,IAAI,gBAAgB;IAIzB,eAAe,IAAI,sBAAsB;IAIzC,cAAc,IAAI,qBAAqB;IAIvC,WAAW,IAAI,kBAAkB;IAIjC,IAAI,IAAI,IAAI;IAIZ,gBAAgB,CAAC,CAAC,SAAS,MAAM,wBAAwB,EACvD,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC,KAAK,GAAG,EAC1E,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC1C,IAAI;IACP,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,kCAAkC,EAC5C,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC1C,IAAI;IAKP,mBAAmB,CAAC,CAAC,SAAS,MAAM,wBAAwB,EAC1D,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC,KAAK,GAAG,EAC1E,OAAO,CAAC,EAAE,OAAO,GAAG,oBAAoB,GACvC,IAAI;IACP,mBAAmB,CACjB,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,kCAAkC,EAC5C,OAAO,CAAC,EAAE,OAAO,GAAG,oBAAoB,GACvC,IAAI;IAKP,aAAa,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;CAGrC"}