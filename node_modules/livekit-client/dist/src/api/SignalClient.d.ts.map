{"version": 3, "file": "SignalClient.d.ts", "sourceRoot": "", "sources": ["../../../src/api/SignalClient.ts"], "names": [], "mappings": "AACA,OAAO,EACL,eAAe,EACf,iBAAiB,EAEjB,uBAAuB,EAEvB,YAAY,EACZ,YAAY,EAGZ,eAAe,EAEf,eAAe,EACf,iBAAiB,EACjB,eAAe,EACf,IAAI,EACJ,iBAAiB,EACjB,kBAAkB,EAClB,aAAa,EAEb,YAAY,EACZ,gBAAgB,EAChB,WAAW,EACX,iBAAiB,EACjB,uBAAuB,EAEvB,4BAA4B,EAC5B,oBAAoB,EACpB,SAAS,EACT,eAAe,EACf,sBAAsB,EACtB,wBAAwB,EAIxB,kBAAkB,EAClB,mBAAmB,EAEnB,UAAU,EAEX,MAAM,mBAAmB,CAAC;AAI3B,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAEnD,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAIjD,UAAU,WAAY,SAAQ,aAAa;IACzC,eAAe;IACf,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,eAAe;IACf,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,eAAe;IACf,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAGD,MAAM,WAAW,aAAa;IAC5B,aAAa,EAAE,OAAO,CAAC;IACvB,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,OAAO,CAAC;IACrB,gBAAgB,EAAE,MAAM,CAAC;CAC1B;AAED,KAAK,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;AAmB9C,oBAAY,qBAAqB;IAC/B,UAAU,IAAA;IACV,SAAS,IAAA;IACT,YAAY,IAAA;IACZ,aAAa,IAAA;IACb,YAAY,IAAA;CACb;AAED,gBAAgB;AAChB,qBAAa,YAAY;IACvB,YAAY,EAAE,UAAU,CAAC;IAEzB,cAAc,EAAE,KAAK,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAE3C,OAAO,EAAE,OAAO,CAAC;IAEjB,iCAAiC;IACjC,GAAG,EAAE,MAAM,CAAK;IAEhB,sDAAsD;IACtD,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IAEnC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,yBAAyB,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IAEpE,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,yBAAyB,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IAGnE,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,MAAM,EAAE,YAAY,KAAK,IAAI,CAAC;IAEpE,mBAAmB,CAAC,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,IAAI,CAAC;IAE3D,qBAAqB,CAAC,EAAE,CAAC,GAAG,EAAE,sBAAsB,KAAK,IAAI,CAAC;IAE9D,oBAAoB,CAAC,EAAE,MAAM,IAAI,CAAC;IAElC,iBAAiB,CAAC,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,KAAK,IAAI,CAAC;IAEjD,mBAAmB,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC;IAEjE,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;IAEpC,mBAAmB,CAAC,EAAE,CAAC,MAAM,EAAE,uBAAuB,KAAK,IAAI,CAAC;IAEhE,mBAAmB,CAAC,EAAE,CAAC,MAAM,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAE1D,yBAAyB,CAAC,EAAE,CAAC,MAAM,EAAE,uBAAuB,KAAK,IAAI,CAAC;IAEtE,8BAA8B,CAAC,EAAE,CAAC,MAAM,EAAE,4BAA4B,KAAK,IAAI,CAAC;IAEhF,mBAAmB,CAAC,EAAE,CAAC,MAAM,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAE7D,uBAAuB,CAAC,EAAE,CAAC,GAAG,EAAE,wBAAwB,KAAK,IAAI,CAAC;IAElE,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IAEzC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,YAAY,KAAK,IAAI,CAAC;IAExC,iBAAiB,CAAC,EAAE,CAAC,QAAQ,EAAE,eAAe,KAAK,IAAI,CAAC;IAExD,sBAAsB,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IAEpD,WAAW,CAAC,EAAE,CAAC,GAAG,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAE/C,cAAc,CAAC,EAAE,WAAW,CAAC;IAE7B,EAAE,CAAC,EAAE,SAAS,CAAC;IAEf,IAAI,YAAY,0BAEf;IAED,IAAI,cAAc,YAKjB;IAED,OAAO,KAAK,wBAAwB,GAKnC;IAED,OAAO,CAAC,gBAAgB;IAKxB,OAAO,CAAC,OAAO,CAAC,CAAgB;IAEhC,OAAO,CAAC,WAAW,CAA4C;IAE/D,OAAO,CAAC,mBAAmB,CAAqB;IAEhD,OAAO,CAAC,oBAAoB,CAAqB;IAEjD,OAAO,CAAC,YAAY,CAA6C;IAEjE,OAAO,CAAC,WAAW,CAAQ;IAE3B,OAAO,CAAC,KAAK,CAA6D;IAE1E,OAAO,CAAC,cAAc,CAAQ;IAE9B,OAAO,CAAC,GAAG,CAAO;IAElB,OAAO,CAAC,eAAe,CAAC,CAAmC;IAE3D,OAAO,CAAC,UAAU,CAAK;gBAEX,OAAO,GAAE,OAAe,EAAE,aAAa,GAAE,aAAkB;IAWvE,OAAO,KAAK,UAAU,GAErB;IAEK,IAAI,CACR,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,aAAa,EACnB,WAAW,CAAC,EAAE,WAAW,GACxB,OAAO,CAAC,YAAY,CAAC;IASlB,SAAS,CACb,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,MAAM,EACb,GAAG,CAAC,EAAE,MAAM,EACZ,MAAM,CAAC,EAAE,eAAe,GACvB,OAAO,CAAC,iBAAiB,GAAG,SAAS,CAAC;IAqBzC,OAAO,CAAC,OAAO;IAqMf,gBAAgB;IAChB,cAAc,aAYZ;IAEI,KAAK,CAAC,WAAW,GAAE,OAAc;IAuCvC,SAAS,CAAC,KAAK,EAAE,yBAAyB,EAAE,OAAO,EAAE,MAAM;IAS3D,UAAU,CAAC,MAAM,EAAE,yBAAyB,EAAE,OAAO,EAAE,MAAM;IAQ7D,gBAAgB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,EAAE,YAAY;IAWrE,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;IAU9C,YAAY,CAAC,GAAG,EAAE,eAAe;IAO3B,uBAAuB,CAC3B,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,EACZ,UAAU,GAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAM;IAezC,uBAAuB,CAAC,QAAQ,EAAE,mBAAmB;IAOrD,sBAAsB,CAAC,GAAG,EAAE,kBAAkB;IAO9C,aAAa,CAAC,IAAI,EAAE,SAAS;IAO7B,qBAAqB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;IAU5D,iCAAiC,CAAC,eAAe,EAAE,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE;IAU/F,oBAAoB,CAAC,QAAQ,EAAE,gBAAgB;IAO/C,QAAQ;IAiBR,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE;IAOzE,SAAS;IAWH,WAAW,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,GAAE,OAAe;IA2CpE,OAAO,CAAC,oBAAoB;IAwG5B,cAAc;YASA,aAAa;IAU3B,OAAO,CAAC,aAAa;IAIrB;;;OAGG;IACH,OAAO,CAAC,gBAAgB;IAiBxB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAMxB,OAAO,CAAC,iBAAiB;IAazB,OAAO,CAAC,iBAAiB;CAO1B;AAoBD,wBAAgB,yBAAyB,CACvC,GAAG,EAAE,qBAAqB,GAAG,yBAAyB,EACtD,EAAE,CAAC,EAAE,MAAM,GACV,kBAAkB,CAOpB"}