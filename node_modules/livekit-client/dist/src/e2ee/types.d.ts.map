{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/e2ee/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;AAC1C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACxD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AACrD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AAErD,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB;AAED,MAAM,WAAW,WAAY,SAAQ,WAAW;IAC9C,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE;QACJ,kBAAkB,EAAE,kBAAkB,CAAC;QACvC,QAAQ,EAAE,QAAQ,CAAC;KACpB,CAAC;CACH;AAED,MAAM,WAAW,aAAc,SAAQ,WAAW;IAChD,IAAI,EAAE,QAAQ,CAAC;IACf,IAAI,EAAE;QACJ,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAC7B,WAAW,EAAE,OAAO,CAAC;QACrB,GAAG,EAAE,SAAS,CAAC;QACf,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC;CACH;AAED,MAAM,WAAW,kBAAmB,SAAQ,WAAW;IACrD,IAAI,EAAE,WAAW,CAAC;IAClB,IAAI,EAAE;QACJ,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC7B,mBAAmB,EAAE,MAAM,CAAC;KAC7B,CAAC;CACH;AAED,MAAM,WAAW,iBAAkB,SAAQ,WAAW;IACpD,IAAI,EAAE,eAAe,CAAC;IACtB,IAAI,EAAE;QACJ,OAAO,EAAE,UAAU,CAAC;KACrB,CAAC;CACH;AAED,MAAM,WAAW,aAAc,SAAQ,WAAW;IAChD,IAAI,EAAE,QAAQ,GAAG,QAAQ,CAAC;IAC1B,IAAI,EAAE;QACJ,mBAAmB,EAAE,MAAM,CAAC;QAC5B,cAAc,EAAE,cAAc,CAAC;QAC/B,cAAc,EAAE,cAAc,CAAC;QAC/B,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,UAAU,CAAC;QACnB,OAAO,EAAE,OAAO,CAAC;KAClB,CAAC;CACH;AAED,MAAM,WAAW,sBAAuB,SAAQ,WAAW;IACzD,IAAI,EAAE,iBAAiB,CAAC;IACxB,IAAI,EAAE;QACJ,mBAAmB,EAAE,MAAM,CAAC;QAC5B,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;CACH;AAED,MAAM,WAAW,kBAAmB,SAAQ,WAAW;IACrD,IAAI,EAAE,aAAa,CAAC;IACpB,IAAI,EAAE;QACJ,mBAAmB,EAAE,MAAM,CAAC;QAC5B,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,EAAE,UAAU,CAAC;KACnB,CAAC;CACH;AAED,MAAM,WAAW,qBAAsB,SAAQ,WAAW;IACxD,IAAI,EAAE,gBAAgB,CAAC;IACvB,IAAI,EAAE;QACJ,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAC7B,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC;CACH;AAED,MAAM,WAAW,cAAe,SAAQ,WAAW;IACjD,IAAI,EAAE,YAAY,CAAC;IACnB,IAAI,EAAE;QACJ,mBAAmB,EAAE,MAAM,CAAC;QAC5B,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,aAAa,EAAE,aAAa,CAAC;KAC9B,CAAC;CACH;AAED,MAAM,WAAW,YAAa,SAAQ,WAAW;IAC/C,IAAI,EAAE,OAAO,CAAC;IACd,IAAI,EAAE;QACJ,KAAK,EAAE,KAAK,CAAC;KACd,CAAC;CACH;AAED,MAAM,WAAW,aAAc,SAAQ,WAAW;IAChD,IAAI,EAAE,QAAQ,CAAC;IACf,IAAI,EAAE;QACJ,mBAAmB,EAAE,MAAM,CAAC;QAC5B,OAAO,EAAE,OAAO,CAAC;KAClB,CAAC;CACH;AAED,MAAM,WAAW,OAAQ,SAAQ,WAAW;IAC1C,IAAI,EAAE,SAAS,CAAC;IAChB,IAAI,EAAE;QACJ,OAAO,EAAE,OAAO,CAAC;KAClB,CAAC;CACH;AAED,MAAM,MAAM,iBAAiB,GACzB,WAAW,GACX,aAAa,GACb,aAAa,GACb,YAAY,GACZ,aAAa,GACb,sBAAsB,GACtB,kBAAkB,GAClB,kBAAkB,GAClB,qBAAqB,GACrB,cAAc,GACd,iBAAiB,GACjB,OAAO,CAAC;AAEZ,MAAM,MAAM,MAAM,GAAG;IAAE,QAAQ,EAAE,SAAS,CAAC;IAAC,aAAa,EAAE,SAAS,CAAA;CAAE,CAAC;AAEvE,MAAM,MAAM,aAAa,GAAG;IAG1B,QAAQ,EAAE,WAAW,CAAC;IACtB,SAAS,EAAE,SAAS,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B,SAAS,EAAE,OAAO,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,gBAAgB,EAAE,MAAM,CAAC;IACzB,WAAW,EAAE,MAAM,CAAC;CACrB,CAAC;AAEF,MAAM,MAAM,OAAO,GAAG;IACpB,GAAG,EAAE,SAAS,CAAC;IACf,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B,WAAW,EAAE,eAAe,CAAC;IAC7B,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AACF,MAAM,MAAM,WAAW,GACnB,kBAAkB,GAClB;IACE,8BAA8B;IAC9B,WAAW,EAAE,eAAe,CAAC;CAC9B,CAAC;AAEN,MAAM,MAAM,oBAAoB,GAAG;IACjC,gBAAgB;IAChB,YAAY,EAAE,MAAM,CAAC;IACrB,2BAA2B;IAC3B,aAAa,CAAC,EAAE,SAAS,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG;IACnC,IAAI,EAAE,QAAQ,GAAG,QAAQ,CAAC;IAC1B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,UAAU,CAAC;CACpB,CAAC"}