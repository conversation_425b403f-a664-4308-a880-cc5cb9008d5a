{"version": 3, "file": "livekit-client.e2ee.worker.js", "sources": ["../node_modules/.pnpm/loglevel@1.9.2/node_modules/loglevel/lib/loglevel.js", "../src/logger.ts", "../src/utils/AsyncQueue.ts", "../node_modules/.pnpm/@livekit+mutex@1.1.1/node_modules/@livekit/mutex/dist/index.mjs", "../src/e2ee/constants.ts", "../src/room/errors.ts", "../src/e2ee/errors.ts", "../src/e2ee/events.ts", "../node_modules/.pnpm/events@3.3.0/node_modules/events/events.js", "../src/e2ee/utils.ts", "../src/e2ee/worker/naluUtils.ts", "../src/e2ee/worker/sifPayload.ts", "../src/e2ee/worker/FrameCryptor.ts", "../src/e2ee/worker/ParticipantKeyHandler.ts", "../src/e2ee/worker/e2ee.worker.ts"], "sourcesContent": ["/*\n* loglevel - https://github.com/pimterry/loglevel\n*\n* Copyright (c) 2013 <PERSON>\n* Licensed under the MIT license.\n*/\n(function (root, definition) {\n    \"use strict\";\n    if (typeof define === 'function' && define.amd) {\n        define(definition);\n    } else if (typeof module === 'object' && module.exports) {\n        module.exports = definition();\n    } else {\n        root.log = definition();\n    }\n}(this, function () {\n    \"use strict\";\n\n    // Slightly dubious tricks to cut down minimized file size\n    var noop = function() {};\n    var undefinedType = \"undefined\";\n    var isIE = (typeof window !== undefinedType) && (typeof window.navigator !== undefinedType) && (\n        /Trident\\/|MSIE /.test(window.navigator.userAgent)\n    );\n\n    var logMethods = [\n        \"trace\",\n        \"debug\",\n        \"info\",\n        \"warn\",\n        \"error\"\n    ];\n\n    var _loggersByName = {};\n    var defaultLogger = null;\n\n    // Cross-browser bind equivalent that works at least back to IE6\n    function bindMethod(obj, methodName) {\n        var method = obj[methodName];\n        if (typeof method.bind === 'function') {\n            return method.bind(obj);\n        } else {\n            try {\n                return Function.prototype.bind.call(method, obj);\n            } catch (e) {\n                // Missing bind shim or IE8 + Modernizr, fallback to wrapping\n                return function() {\n                    return Function.prototype.apply.apply(method, [obj, arguments]);\n                };\n            }\n        }\n    }\n\n    // Trace() doesn't print the message in IE, so for that case we need to wrap it\n    function traceForIE() {\n        if (console.log) {\n            if (console.log.apply) {\n                console.log.apply(console, arguments);\n            } else {\n                // In old IE, native console methods themselves don't have apply().\n                Function.prototype.apply.apply(console.log, [console, arguments]);\n            }\n        }\n        if (console.trace) console.trace();\n    }\n\n    // Build the best logging method possible for this env\n    // Wherever possible we want to bind, not wrap, to preserve stack traces\n    function realMethod(methodName) {\n        if (methodName === 'debug') {\n            methodName = 'log';\n        }\n\n        if (typeof console === undefinedType) {\n            return false; // No method possible, for now - fixed later by enableLoggingWhenConsoleArrives\n        } else if (methodName === 'trace' && isIE) {\n            return traceForIE;\n        } else if (console[methodName] !== undefined) {\n            return bindMethod(console, methodName);\n        } else if (console.log !== undefined) {\n            return bindMethod(console, 'log');\n        } else {\n            return noop;\n        }\n    }\n\n    // These private functions always need `this` to be set properly\n\n    function replaceLoggingMethods() {\n        /*jshint validthis:true */\n        var level = this.getLevel();\n\n        // Replace the actual methods.\n        for (var i = 0; i < logMethods.length; i++) {\n            var methodName = logMethods[i];\n            this[methodName] = (i < level) ?\n                noop :\n                this.methodFactory(methodName, level, this.name);\n        }\n\n        // Define log.log as an alias for log.debug\n        this.log = this.debug;\n\n        // Return any important warnings.\n        if (typeof console === undefinedType && level < this.levels.SILENT) {\n            return \"No console available for logging\";\n        }\n    }\n\n    // In old IE versions, the console isn't present until you first open it.\n    // We build realMethod() replacements here that regenerate logging methods\n    function enableLoggingWhenConsoleArrives(methodName) {\n        return function () {\n            if (typeof console !== undefinedType) {\n                replaceLoggingMethods.call(this);\n                this[methodName].apply(this, arguments);\n            }\n        };\n    }\n\n    // By default, we use closely bound real methods wherever possible, and\n    // otherwise we wait for a console to appear, and then try again.\n    function defaultMethodFactory(methodName, _level, _loggerName) {\n        /*jshint validthis:true */\n        return realMethod(methodName) ||\n               enableLoggingWhenConsoleArrives.apply(this, arguments);\n    }\n\n    function Logger(name, factory) {\n      // Private instance variables.\n      var self = this;\n      /**\n       * The level inherited from a parent logger (or a global default). We\n       * cache this here rather than delegating to the parent so that it stays\n       * in sync with the actual logging methods that we have installed (the\n       * parent could change levels but we might not have rebuilt the loggers\n       * in this child yet).\n       * @type {number}\n       */\n      var inheritedLevel;\n      /**\n       * The default level for this logger, if any. If set, this overrides\n       * `inheritedLevel`.\n       * @type {number|null}\n       */\n      var defaultLevel;\n      /**\n       * A user-specific level for this logger. If set, this overrides\n       * `defaultLevel`.\n       * @type {number|null}\n       */\n      var userLevel;\n\n      var storageKey = \"loglevel\";\n      if (typeof name === \"string\") {\n        storageKey += \":\" + name;\n      } else if (typeof name === \"symbol\") {\n        storageKey = undefined;\n      }\n\n      function persistLevelIfPossible(levelNum) {\n          var levelName = (logMethods[levelNum] || 'silent').toUpperCase();\n\n          if (typeof window === undefinedType || !storageKey) return;\n\n          // Use localStorage if available\n          try {\n              window.localStorage[storageKey] = levelName;\n              return;\n          } catch (ignore) {}\n\n          // Use session cookie as fallback\n          try {\n              window.document.cookie =\n                encodeURIComponent(storageKey) + \"=\" + levelName + \";\";\n          } catch (ignore) {}\n      }\n\n      function getPersistedLevel() {\n          var storedLevel;\n\n          if (typeof window === undefinedType || !storageKey) return;\n\n          try {\n              storedLevel = window.localStorage[storageKey];\n          } catch (ignore) {}\n\n          // Fallback to cookies if local storage gives us nothing\n          if (typeof storedLevel === undefinedType) {\n              try {\n                  var cookie = window.document.cookie;\n                  var cookieName = encodeURIComponent(storageKey);\n                  var location = cookie.indexOf(cookieName + \"=\");\n                  if (location !== -1) {\n                      storedLevel = /^([^;]+)/.exec(\n                          cookie.slice(location + cookieName.length + 1)\n                      )[1];\n                  }\n              } catch (ignore) {}\n          }\n\n          // If the stored level is not valid, treat it as if nothing was stored.\n          if (self.levels[storedLevel] === undefined) {\n              storedLevel = undefined;\n          }\n\n          return storedLevel;\n      }\n\n      function clearPersistedLevel() {\n          if (typeof window === undefinedType || !storageKey) return;\n\n          // Use localStorage if available\n          try {\n              window.localStorage.removeItem(storageKey);\n          } catch (ignore) {}\n\n          // Use session cookie as fallback\n          try {\n              window.document.cookie =\n                encodeURIComponent(storageKey) + \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC\";\n          } catch (ignore) {}\n      }\n\n      function normalizeLevel(input) {\n          var level = input;\n          if (typeof level === \"string\" && self.levels[level.toUpperCase()] !== undefined) {\n              level = self.levels[level.toUpperCase()];\n          }\n          if (typeof level === \"number\" && level >= 0 && level <= self.levels.SILENT) {\n              return level;\n          } else {\n              throw new TypeError(\"log.setLevel() called with invalid level: \" + input);\n          }\n      }\n\n      /*\n       *\n       * Public logger API - see https://github.com/pimterry/loglevel for details\n       *\n       */\n\n      self.name = name;\n\n      self.levels = { \"TRACE\": 0, \"DEBUG\": 1, \"INFO\": 2, \"WARN\": 3,\n          \"ERROR\": 4, \"SILENT\": 5};\n\n      self.methodFactory = factory || defaultMethodFactory;\n\n      self.getLevel = function () {\n          if (userLevel != null) {\n            return userLevel;\n          } else if (defaultLevel != null) {\n            return defaultLevel;\n          } else {\n            return inheritedLevel;\n          }\n      };\n\n      self.setLevel = function (level, persist) {\n          userLevel = normalizeLevel(level);\n          if (persist !== false) {  // defaults to true\n              persistLevelIfPossible(userLevel);\n          }\n\n          // NOTE: in v2, this should call rebuild(), which updates children.\n          return replaceLoggingMethods.call(self);\n      };\n\n      self.setDefaultLevel = function (level) {\n          defaultLevel = normalizeLevel(level);\n          if (!getPersistedLevel()) {\n              self.setLevel(level, false);\n          }\n      };\n\n      self.resetLevel = function () {\n          userLevel = null;\n          clearPersistedLevel();\n          replaceLoggingMethods.call(self);\n      };\n\n      self.enableAll = function(persist) {\n          self.setLevel(self.levels.TRACE, persist);\n      };\n\n      self.disableAll = function(persist) {\n          self.setLevel(self.levels.SILENT, persist);\n      };\n\n      self.rebuild = function () {\n          if (defaultLogger !== self) {\n              inheritedLevel = normalizeLevel(defaultLogger.getLevel());\n          }\n          replaceLoggingMethods.call(self);\n\n          if (defaultLogger === self) {\n              for (var childName in _loggersByName) {\n                _loggersByName[childName].rebuild();\n              }\n          }\n      };\n\n      // Initialize all the internal levels.\n      inheritedLevel = normalizeLevel(\n          defaultLogger ? defaultLogger.getLevel() : \"WARN\"\n      );\n      var initialLevel = getPersistedLevel();\n      if (initialLevel != null) {\n          userLevel = normalizeLevel(initialLevel);\n      }\n      replaceLoggingMethods.call(self);\n    }\n\n    /*\n     *\n     * Top-level API\n     *\n     */\n\n    defaultLogger = new Logger();\n\n    defaultLogger.getLogger = function getLogger(name) {\n        if ((typeof name !== \"symbol\" && typeof name !== \"string\") || name === \"\") {\n            throw new TypeError(\"You must supply a name when creating a logger.\");\n        }\n\n        var logger = _loggersByName[name];\n        if (!logger) {\n            logger = _loggersByName[name] = new Logger(\n                name,\n                defaultLogger.methodFactory\n            );\n        }\n        return logger;\n    };\n\n    // Grab the current global log variable in case of overwrite\n    var _log = (typeof window !== undefinedType) ? window.log : undefined;\n    defaultLogger.noConflict = function() {\n        if (typeof window !== undefinedType &&\n               window.log === defaultLogger) {\n            window.log = _log;\n        }\n\n        return defaultLogger;\n    };\n\n    defaultLogger.getLoggers = function getLoggers() {\n        return _loggersByName;\n    };\n\n    // ES6 default export, for compatibility\n    defaultLogger['default'] = defaultLogger;\n\n    return defaultLogger;\n}));\n", "import * as log from 'loglevel';\n\nexport enum LogLevel {\n  trace = 0,\n  debug = 1,\n  info = 2,\n  warn = 3,\n  error = 4,\n  silent = 5,\n}\n\nexport enum LoggerNames {\n  Default = 'livekit',\n  Room = 'livekit-room',\n  Participant = 'livekit-participant',\n  Track = 'livekit-track',\n  Publication = 'livekit-track-publication',\n  Engine = 'livekit-engine',\n  Signal = 'livekit-signal',\n  PCManager = 'livekit-pc-manager',\n  PCTransport = 'livekit-pc-transport',\n  E2EE = 'lk-e2ee',\n}\n\ntype LogLevelString = keyof typeof LogLevel;\n\nexport type StructuredLogger = log.Logger & {\n  trace: (msg: string, context?: object) => void;\n  debug: (msg: string, context?: object) => void;\n  info: (msg: string, context?: object) => void;\n  warn: (msg: string, context?: object) => void;\n  error: (msg: string, context?: object) => void;\n  setDefaultLevel: (level: log.LogLevelDesc) => void;\n  setLevel: (level: log.LogLevelDesc) => void;\n  getLevel: () => number;\n};\n\nlet livekitLogger = log.getLogger('livekit');\nconst livekitLoggers = Object.values(LoggerNames).map((name) => log.getLogger(name));\n\nlivekitLogger.setDefaultLevel(LogLevel.info);\n\nexport default livekitLogger as StructuredLogger;\n\n/**\n * @internal\n */\nexport function getLogger(name: string) {\n  const logger = log.getLogger(name);\n  logger.setDefaultLevel(livekitLogger.getLevel());\n  return logger as StructuredLogger;\n}\n\nexport function setLogLevel(level: LogLevel | LogLevelString, loggerName?: LoggerNames) {\n  if (loggerName) {\n    log.getLogger(loggerName).setLevel(level);\n  } else {\n    for (const logger of livekitLoggers) {\n      logger.setLevel(level);\n    }\n  }\n}\n\nexport type LogExtension = (level: LogLevel, msg: string, context?: object) => void;\n\n/**\n * use this to hook into the logging function to allow sending internal livekit logs to third party services\n * if set, the browser logs will lose their stacktrace information (see https://github.com/pimterry/loglevel#writing-plugins)\n */\nexport function setLogExtension(extension: LogExtension, logger?: StructuredLogger) {\n  const loggers = logger ? [logger] : livekitLoggers;\n\n  loggers.forEach((logR) => {\n    const originalFactory = logR.methodFactory;\n\n    logR.methodFactory = (methodName, configLevel, loggerName) => {\n      const rawMethod = originalFactory(methodName, configLevel, loggerName);\n\n      const logLevel = LogLevel[methodName as LogLevelString];\n      const needLog = logLevel >= configLevel && logLevel < LogLevel.silent;\n\n      return (msg, context?: [msg: string, context: object]) => {\n        if (context) rawMethod(msg, context);\n        else rawMethod(msg);\n        if (needLog) {\n          extension(logLevel, msg, context);\n        }\n      };\n    };\n    logR.setLevel(logR.getLevel());\n  });\n}\n\nexport const workerLogger = log.getLogger('lk-e2ee') as StructuredLogger;\n", "import { Mutex } from '@livekit/mutex';\n\ntype QueueTask<T> = () => PromiseLike<T>;\n\nenum QueueTaskStatus {\n  'WAITING',\n  'RUNNING',\n  'COMPLETED',\n}\n\ntype QueueTaskInfo = {\n  id: number;\n  enqueuedAt: number;\n  executedAt?: number;\n  status: QueueTaskStatus;\n};\n\nexport class AsyncQueue {\n  private pendingTasks: Map<number, QueueTaskInfo>;\n\n  private taskMutex: Mutex;\n\n  private nextTaskIndex: number;\n\n  constructor() {\n    this.pendingTasks = new Map();\n    this.taskMutex = new Mutex();\n    this.nextTaskIndex = 0;\n  }\n\n  async run<T>(task: QueueTask<T>) {\n    const taskInfo: QueueTaskInfo = {\n      id: this.nextTaskIndex++,\n      enqueuedAt: Date.now(),\n      status: QueueTaskStatus.WAITING,\n    };\n    this.pendingTasks.set(taskInfo.id, taskInfo);\n    const unlock = await this.taskMutex.lock();\n    try {\n      taskInfo.executedAt = Date.now();\n      taskInfo.status = QueueTaskStatus.RUNNING;\n      return await task();\n    } finally {\n      taskInfo.status = QueueTaskStatus.COMPLETED;\n      this.pendingTasks.delete(taskInfo.id);\n      unlock();\n    }\n  }\n\n  async flush() {\n    return this.run(async () => {});\n  }\n\n  snapshot() {\n    return Array.from(this.pendingTasks.values());\n  }\n}\n", "var e = Object.defineProperty;\nvar h = (i, s, t) => s in i ? e(i, s, { enumerable: !0, configurable: !0, writable: !0, value: t }) : i[s] = t;\nvar o = (i, s, t) => h(i, typeof s != \"symbol\" ? s + \"\" : s, t);\nclass _ {\n  constructor() {\n    o(this, \"_locking\");\n    o(this, \"_locks\");\n    this._locking = Promise.resolve(), this._locks = 0;\n  }\n  isLocked() {\n    return this._locks > 0;\n  }\n  lock() {\n    this._locks += 1;\n    let s;\n    const t = new Promise(\n      (l) => s = () => {\n        this._locks -= 1, l();\n      }\n    ), c = this._locking.then(() => s);\n    return this._locking = this._locking.then(() => t), c;\n  }\n}\nclass n {\n  constructor(s) {\n    o(this, \"_queue\");\n    o(this, \"_limit\");\n    o(this, \"_locks\");\n    this._queue = [], this._limit = s, this._locks = 0;\n  }\n  isLocked() {\n    return this._locks >= this._limit;\n  }\n  async lock() {\n    return this.isLocked() ? new Promise((s) => {\n      this._queue.push(() => {\n        this._locks++, s(this._unlock.bind(this));\n      });\n    }) : (this._locks++, this._unlock.bind(this));\n  }\n  _unlock() {\n    if (this._locks--, this._queue.length && !this.isLocked()) {\n      const s = this._queue.shift();\n      s == null || s();\n    }\n  }\n}\nexport {\n  n as MultiMutex,\n  _ as Mutex\n};\n//# sourceMappingURL=index.mjs.map\n", "import type { KeyProviderOptions } from './types';\n\nexport const ENCRYPTION_ALGORITHM = 'AES-GCM';\n\n// How many consecutive frames can fail decrypting before a particular key gets marked as invalid\nexport const DECRYPTION_FAILURE_TOLERANCE = 10;\n\n// We copy the first bytes of the VP8 payload unencrypted.\n// For keyframes this is 10 bytes, for non-keyframes (delta) 3. See\n//   https://tools.ietf.org/html/rfc6386#section-9.1\n// This allows the bridge to continue detecting keyframes (only one byte needed in the JVB)\n// and is also a bit easier for the VP8 decoder (i.e. it generates funny garbage pictures\n// instead of being unable to decode).\n// This is a bit for show and we might want to reduce to 1 unconditionally in the final version.\n//\n// For audio (where frame.type is not set) we do not encrypt the opus TOC byte:\n//   https://tools.ietf.org/html/rfc6716#section-3.1\nexport const UNENCRYPTED_BYTES = {\n  key: 10,\n  delta: 3,\n  audio: 1, // frame.type is not set on audio, so this is set manually\n  empty: 0,\n} as const;\n\n/* We use a 12 byte bit IV. This is signalled in plain together with the\n packet. See https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/encrypt#parameters */\nexport const IV_LENGTH = 12;\n\n// flag set to indicate that e2ee has been setup for sender/receiver;\nexport const E2EE_FLAG = 'lk_e2ee';\n\nexport const SALT = 'LKFrameEncryptionKey';\n\nexport const KEY_PROVIDER_DEFAULTS: KeyProviderOptions = {\n  sharedKey: false,\n  ratchetSalt: SALT,\n  ratchetWindowSize: 8,\n  failureTolerance: DECRYPTION_FAILURE_TOLERANCE,\n  keyringSize: 16,\n} as const;\n\nexport const MAX_SIF_COUNT = 100;\nexport const MAX_SIF_DURATION = 2000;\n", "import { DisconnectReason, RequestResponse_Reason } from '@livekit/protocol';\n\nexport class LivekitError extends Error {\n  code: number;\n\n  constructor(code: number, message?: string) {\n    super(message || 'an error has occured');\n    this.name = 'LiveKitError';\n    this.code = code;\n  }\n}\n\nexport enum ConnectionErrorReason {\n  NotAllowed,\n  ServerUnreachable,\n  InternalError,\n  Cancelled,\n  LeaveRequest,\n  Timeout,\n}\n\nexport class ConnectionError extends LivekitError {\n  status?: number;\n\n  context?: unknown | DisconnectReason;\n\n  reason: ConnectionErrorReason;\n\n  reasonName: string;\n\n  constructor(\n    message: string,\n    reason: ConnectionErrorReason,\n    status?: number,\n    context?: unknown | DisconnectReason,\n  ) {\n    super(1, message);\n    this.name = 'ConnectionError';\n    this.status = status;\n    this.reason = reason;\n    this.context = context;\n    this.reasonName = ConnectionErrorReason[reason];\n  }\n}\n\nexport class DeviceUnsupportedError extends LivekitError {\n  constructor(message?: string) {\n    super(21, message ?? 'device is unsupported');\n    this.name = 'DeviceUnsupportedError';\n  }\n}\n\nexport class TrackInvalidError extends LivekitError {\n  constructor(message?: string) {\n    super(20, message ?? 'track is invalid');\n    this.name = 'TrackInvalidError';\n  }\n}\n\nexport class UnsupportedServer extends LivekitError {\n  constructor(message?: string) {\n    super(10, message ?? 'unsupported server');\n    this.name = 'UnsupportedServer';\n  }\n}\n\nexport class UnexpectedConnectionState extends LivekitError {\n  constructor(message?: string) {\n    super(12, message ?? 'unexpected connection state');\n    this.name = 'UnexpectedConnectionState';\n  }\n}\n\nexport class NegotiationError extends LivekitError {\n  constructor(message?: string) {\n    super(13, message ?? 'unable to negotiate');\n    this.name = 'NegotiationError';\n  }\n}\n\nexport class PublishDataError extends LivekitError {\n  constructor(message?: string) {\n    super(14, message ?? 'unable to publish data');\n    this.name = 'PublishDataError';\n  }\n}\n\nexport class PublishTrackError extends LivekitError {\n  status: number;\n\n  constructor(message: string, status: number) {\n    super(15, message);\n    this.name = 'PublishTrackError';\n    this.status = status;\n  }\n}\n\nexport type RequestErrorReason =\n  | Exclude<RequestResponse_Reason, RequestResponse_Reason.OK>\n  | 'TimeoutError';\n\nexport class SignalRequestError extends LivekitError {\n  reason: RequestErrorReason;\n\n  reasonName: string;\n\n  constructor(message: string, reason: RequestErrorReason) {\n    super(15, message);\n    this.reason = reason;\n    this.reasonName = typeof reason === 'string' ? reason : RequestResponse_Reason[reason];\n  }\n}\n\n// NOTE: matches with https://github.com/livekit/client-sdk-swift/blob/f37bbd260d61e165084962db822c79f995f1a113/Sources/LiveKit/DataStream/StreamError.swift#L17\nexport enum DataStreamErrorReason {\n  // Unable to open a stream with the same ID more than once.\n  AlreadyOpened = 0,\n\n  // Stream closed abnormally by remote participant.\n  AbnormalEnd = 1,\n\n  // Incoming chunk data could not be decoded.\n  DecodeFailed = 2,\n\n  // Read length exceeded total length specified in stream header.\n  LengthExceeded = 3,\n\n  // Read length less than total length specified in stream header.\n  Incomplete = 4,\n\n  // Unable to register a stream handler more than once.\n  HandlerAlreadyRegistered = 7,\n}\n\nexport class DataStreamError extends LivekitError {\n  reason: DataStreamErrorReason;\n\n  reasonName: string;\n\n  constructor(message: string, reason: DataStreamErrorReason) {\n    super(16, message);\n    this.name = 'DataStreamError';\n    this.reason = reason;\n    this.reasonName = DataStreamErrorReason[reason];\n  }\n}\n\nexport enum MediaDeviceFailure {\n  // user rejected permissions\n  PermissionDenied = 'PermissionDenied',\n  // device is not available\n  NotFound = 'NotFound',\n  // device is in use. On Windows, only a single tab may get access to a device at a time.\n  DeviceInUse = 'DeviceInUse',\n  Other = 'Other',\n}\n\nexport namespace MediaDeviceFailure {\n  export function getFailure(error: any): MediaDeviceFailure | undefined {\n    if (error && 'name' in error) {\n      if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {\n        return MediaDeviceFailure.NotFound;\n      }\n      if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {\n        return MediaDeviceFailure.PermissionDenied;\n      }\n      if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {\n        return MediaDeviceFailure.DeviceInUse;\n      }\n      return MediaDeviceFailure.Other;\n    }\n  }\n}\n", "import { LivekitError } from '../room/errors';\n\nexport enum CryptorErrorReason {\n  InvalidKey = 0,\n  MissingKey = 1,\n  InternalError = 2,\n}\n\nexport class CryptorError extends LivekitError {\n  reason: CryptorErrorReason;\n\n  participantIdentity?: string;\n\n  constructor(\n    message?: string,\n    reason: CryptorErrorReason = CryptorErrorReason.InternalError,\n    participantIdentity?: string,\n  ) {\n    super(40, message);\n    this.reason = reason;\n    this.participantIdentity = participantIdentity;\n  }\n}\n", "import type Participant from '../room/participant/Participant';\nimport type { CryptorError } from './errors';\nimport type { KeyInfo, RatchetResult } from './types';\n\nexport enum KeyProviderEvent {\n  SetKey = 'setKey',\n  /** Event for requesting to ratchet the key used to encrypt the stream */\n  RatchetRequest = 'ratchetRequest',\n  /** Emitted when a key is ratcheted. Could be after auto-ratcheting on decryption failure or\n   *  following a `RatchetRequest`, will contain the ratcheted key material */\n  KeyRatcheted = 'keyRatcheted',\n}\n\nexport type KeyProviderCallbacks = {\n  [KeyProviderEvent.SetKey]: (keyInfo: KeyInfo) => void;\n  [KeyProviderEvent.RatchetRequest]: (participantIdentity?: string, keyIndex?: number) => void;\n  [KeyProviderEvent.KeyRatcheted]: (\n    ratchetedResult: RatchetResult,\n    participantIdentity?: string,\n    keyIndex?: number,\n  ) => void;\n};\n\nexport enum KeyHandlerEvent {\n  /** Emitted when a key has been ratcheted. Is emitted when any key has been ratcheted\n   * i.e. when the FrameCryptor tried to ratchet when decryption is failing  */\n  KeyRatcheted = 'keyRatcheted',\n}\n\nexport type ParticipantKeyHandlerCallbacks = {\n  [KeyHandlerEvent.KeyRatcheted]: (\n    ratchetResult: RatchetResult,\n    participantIdentity: string,\n    keyIndex?: number,\n  ) => void;\n};\n\nexport enum EncryptionEvent {\n  ParticipantEncryptionStatusChanged = 'participantEncryptionStatusChanged',\n  EncryptionError = 'encryptionError',\n}\n\nexport type E2EEManagerCallbacks = {\n  [EncryptionEvent.ParticipantEncryptionStatusChanged]: (\n    enabled: boolean,\n    participant: Participant,\n  ) => void;\n  [EncryptionEvent.EncryptionError]: (error: Error) => void;\n};\n\nexport type CryptorCallbacks = {\n  [CryptorEvent.Error]: (error: CryptorError) => void;\n};\n\nexport enum CryptorEvent {\n  Error = 'cryptorError',\n}\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "import { ENCRYPTION_ALGORITHM } from './constants';\n\nexport function isE2EESupported() {\n  return isInsertableStreamSupported() || isScriptTransformSupported();\n}\n\nexport function isScriptTransformSupported() {\n  // @ts-ignore\n  return typeof window.RTCRtpScriptTransform !== 'undefined';\n}\n\nexport function isInsertableStreamSupported() {\n  return (\n    typeof window.RTCRtpSender !== 'undefined' &&\n    // @ts-ignore\n    typeof window.RTCRtpSender.prototype.createEncodedStreams !== 'undefined'\n  );\n}\n\nexport function isVideoFrame(\n  frame: RTCEncodedAudioFrame | RTCEncodedVideoFrame,\n): frame is RTCEncodedVideoFrame {\n  return 'type' in frame;\n}\n\nexport async function importKey(\n  keyBytes: Uint8Array | ArrayBuffer,\n  algorithm: string | { name: string } = { name: ENCRYPTION_ALGORITHM },\n  usage: 'derive' | 'encrypt' = 'encrypt',\n) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/importKey\n  return crypto.subtle.importKey(\n    'raw',\n    keyBytes,\n    algorithm,\n    false,\n    usage === 'derive' ? ['deriveBits', 'deriveKey'] : ['encrypt', 'decrypt'],\n  );\n}\n\nexport async function createKeyMaterialFromString(password: string) {\n  let enc = new TextEncoder();\n\n  const keyMaterial = await crypto.subtle.importKey(\n    'raw',\n    enc.encode(password),\n    {\n      name: 'PBKDF2',\n    },\n    false,\n    ['deriveBits', 'deriveKey'],\n  );\n\n  return keyMaterial;\n}\n\nexport async function createKeyMaterialFromBuffer(cryptoBuffer: ArrayBuffer) {\n  const keyMaterial = await crypto.subtle.importKey('raw', cryptoBuffer, 'HKDF', false, [\n    'deriveBits',\n    'deriveKey',\n  ]);\n\n  return keyMaterial;\n}\n\nfunction getAlgoOptions(algorithmName: string, salt: string) {\n  const textEncoder = new TextEncoder();\n  const encodedSalt = textEncoder.encode(salt);\n  switch (algorithmName) {\n    case 'HKDF':\n      return {\n        name: 'HKDF',\n        salt: encodedSalt,\n        hash: 'SHA-256',\n        info: new ArrayBuffer(128),\n      };\n    case 'PBKDF2': {\n      return {\n        name: 'PBKDF2',\n        salt: encodedSalt,\n        hash: 'SHA-256',\n        iterations: 100000,\n      };\n    }\n    default:\n      throw new Error(`algorithm ${algorithmName} is currently unsupported`);\n  }\n}\n\n/**\n * Derives a set of keys from the master key.\n * See https://tools.ietf.org/html/draft-omara-sframe-00#section-4.3.1\n */\nexport async function deriveKeys(material: CryptoKey, salt: string) {\n  const algorithmOptions = getAlgoOptions(material.algorithm.name, salt);\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/deriveKey#HKDF\n  // https://developer.mozilla.org/en-US/docs/Web/API/HkdfParams\n  const encryptionKey = await crypto.subtle.deriveKey(\n    algorithmOptions,\n    material,\n    {\n      name: ENCRYPTION_ALGORITHM,\n      length: 128,\n    },\n    false,\n    ['encrypt', 'decrypt'],\n  );\n\n  return { material, encryptionKey };\n}\n\nexport function createE2EEKey(): Uint8Array {\n  return window.crypto.getRandomValues(new Uint8Array(32));\n}\n\n/**\n * Ratchets a key. See\n * https://tools.ietf.org/html/draft-omara-sframe-00#section-4.3.5.1\n */\nexport async function ratchet(material: CryptoKey, salt: string): Promise<ArrayBuffer> {\n  const algorithmOptions = getAlgoOptions(material.algorithm.name, salt);\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/deriveBits\n  return crypto.subtle.deriveBits(algorithmOptions, material, 256);\n}\n\nexport function needsRbspUnescaping(frameData: Uint8Array) {\n  for (var i = 0; i < frameData.length - 3; i++) {\n    if (frameData[i] == 0 && frameData[i + 1] == 0 && frameData[i + 2] == 3) return true;\n  }\n  return false;\n}\n\nexport function parseRbsp(stream: Uint8Array): Uint8Array {\n  const dataOut: number[] = [];\n  var length = stream.length;\n  for (var i = 0; i < stream.length; ) {\n    // Be careful about over/underflow here. byte_length_ - 3 can underflow, and\n    // i + 3 can overflow, but byte_length_ - i can't, because i < byte_length_\n    // above, and that expression will produce the number of bytes left in\n    // the stream including the byte at i.\n    if (length - i >= 3 && !stream[i] && !stream[i + 1] && stream[i + 2] == 3) {\n      // Two rbsp bytes.\n      dataOut.push(stream[i++]);\n      dataOut.push(stream[i++]);\n      // Skip the emulation byte.\n      i++;\n    } else {\n      // Single rbsp byte.\n      dataOut.push(stream[i++]);\n    }\n  }\n  return new Uint8Array(dataOut);\n}\n\nconst kZerosInStartSequence = 2;\nconst kEmulationByte = 3;\n\nexport function writeRbsp(data_in: Uint8Array): Uint8Array {\n  const dataOut: number[] = [];\n  var numConsecutiveZeros = 0;\n  for (var i = 0; i < data_in.length; ++i) {\n    var byte = data_in[i];\n    if (byte <= kEmulationByte && numConsecutiveZeros >= kZerosInStartSequence) {\n      // Need to escape.\n      dataOut.push(kEmulationByte);\n      numConsecutiveZeros = 0;\n    }\n    dataOut.push(byte);\n    if (byte == 0) {\n      ++numConsecutiveZeros;\n    } else {\n      numConsecutiveZeros = 0;\n    }\n  }\n  return new Uint8Array(dataOut);\n}\n", "/**\n * NALU (Network Abstraction Layer Unit) utilities for H.264 and H.265 video processing\n * Contains functions for parsing and working with NALUs in video frames\n */\n\n/**\n * Mask for extracting NALU type from H.264 header byte\n */\nconst kH264NaluTypeMask = 0x1f;\n\n/**\n * H.264 NALU types according to RFC 6184\n */\nenum H264NALUType {\n  /** Coded slice of a non-IDR picture */\n  SLICE_NON_IDR = 1,\n  /** Coded slice data partition A */\n  SLICE_PARTITION_A = 2,\n  /** Coded slice data partition B */\n  SLICE_PARTITION_B = 3,\n  /** Coded slice data partition C */\n  SLICE_PARTITION_C = 4,\n  /** Coded slice of an IDR picture */\n  SLICE_IDR = 5,\n  /** Supplemental enhancement information */\n  SEI = 6,\n  /** Sequence parameter set */\n  SPS = 7,\n  /** Picture parameter set */\n  PPS = 8,\n  /** Access unit delimiter */\n  AUD = 9,\n  /** End of sequence */\n  END_SEQ = 10,\n  /** End of stream */\n  END_STREAM = 11,\n  /** Filler data */\n  FILLER_DATA = 12,\n  /** Sequence parameter set extension */\n  SPS_EXT = 13,\n  /** Prefix NAL unit */\n  PREFIX_NALU = 14,\n  /** Subset sequence parameter set */\n  SUBSET_SPS = 15,\n  /** Depth parameter set */\n  DPS = 16,\n\n  // 17, 18 reserved\n\n  /** Coded slice of an auxiliary coded picture without partitioning */\n  SLICE_AUX = 19,\n  /** Coded slice extension */\n  SLICE_EXT = 20,\n  /** Coded slice extension for a depth view component or a 3D-AVC texture view component */\n  SLICE_LAYER_EXT = 21,\n\n  // 22, 23 reserved\n}\n\n/**\n * H.265/HEVC NALU types according to ITU-T H.265\n */\nenum H265NALUType {\n  /** Coded slice segment of a non-TSA, non-STSA trailing picture */\n  TRAIL_N = 0,\n  /** Coded slice segment of a non-TSA, non-STSA trailing picture */\n  TRAIL_R = 1,\n  /** Coded slice segment of a TSA picture */\n  TSA_N = 2,\n  /** Coded slice segment of a TSA picture */\n  TSA_R = 3,\n  /** Coded slice segment of an STSA picture */\n  STSA_N = 4,\n  /** Coded slice segment of an STSA picture */\n  STSA_R = 5,\n  /** Coded slice segment of a RADL picture */\n  RADL_N = 6,\n  /** Coded slice segment of a RADL picture */\n  RADL_R = 7,\n  /** Coded slice segment of a RASL picture */\n  RASL_N = 8,\n  /** Coded slice segment of a RASL picture */\n  RASL_R = 9,\n\n  // 10-15 reserved\n\n  /** Coded slice segment of a BLA picture */\n  BLA_W_LP = 16,\n  /** Coded slice segment of a BLA picture */\n  BLA_W_RADL = 17,\n  /** Coded slice segment of a BLA picture */\n  BLA_N_LP = 18,\n  /** Coded slice segment of an IDR picture */\n  IDR_W_RADL = 19,\n  /** Coded slice segment of an IDR picture */\n  IDR_N_LP = 20,\n  /** Coded slice segment of a CRA picture */\n  CRA_NUT = 21,\n\n  // 22-31 reserved\n\n  /** Video parameter set */\n  VPS_NUT = 32,\n  /** Sequence parameter set */\n  SPS_NUT = 33,\n  /** Picture parameter set */\n  PPS_NUT = 34,\n  /** Access unit delimiter */\n  AUD_NUT = 35,\n  /** End of sequence */\n  EOS_NUT = 36,\n  /** End of bitstream */\n  EOB_NUT = 37,\n  /** Filler data */\n  FD_NUT = 38,\n  /** Supplemental enhancement information */\n  PREFIX_SEI_NUT = 39,\n  /** Supplemental enhancement information */\n  SUFFIX_SEI_NUT = 40,\n\n  // 41-47 reserved\n  // 48-63 unspecified\n}\n\n/**\n * Parse H.264 NALU type from the first byte of a NALU\n * @param startByte First byte of the NALU\n * @returns H.264 NALU type\n */\nfunction parseH264NALUType(startByte: number): H264NALUType {\n  return startByte & kH264NaluTypeMask;\n}\n\n/**\n * Parse H.265 NALU type from the first byte of a NALU\n * @param firstByte First byte of the NALU\n * @returns H.265 NALU type\n */\nfunction parseH265NALUType(firstByte: number): H265NALUType {\n  // In H.265, NALU type is in bits 1-6 (shifted right by 1)\n  return (firstByte >> 1) & 0x3f;\n}\n\n/**\n * Check if H.264 NALU type is a slice (IDR or non-IDR)\n * @param naluType H.264 NALU type\n * @returns True if the NALU is a slice\n */\nfunction isH264SliceNALU(naluType: H264NALUType): boolean {\n  return naluType === H264NALUType.SLICE_IDR || naluType === H264NALUType.SLICE_NON_IDR;\n}\n\n/**\n * Check if H.265 NALU type is a slice\n * @param naluType H.265 NALU type\n * @returns True if the NALU is a slice\n */\nfunction isH265SliceNALU(naluType: H265NALUType): boolean {\n  return (\n    // VCL NALUs (Video Coding Layer) - slice segments\n    naluType === H265NALUType.TRAIL_N ||\n    naluType === H265NALUType.TRAIL_R ||\n    naluType === H265NALUType.TSA_N ||\n    naluType === H265NALUType.TSA_R ||\n    naluType === H265NALUType.STSA_N ||\n    naluType === H265NALUType.STSA_R ||\n    naluType === H265NALUType.RADL_N ||\n    naluType === H265NALUType.RADL_R ||\n    naluType === H265NALUType.RASL_N ||\n    naluType === H265NALUType.RASL_R ||\n    naluType === H265NALUType.BLA_W_LP ||\n    naluType === H265NALUType.BLA_W_RADL ||\n    naluType === H265NALUType.BLA_N_LP ||\n    naluType === H265NALUType.IDR_W_RADL ||\n    naluType === H265NALUType.IDR_N_LP ||\n    naluType === H265NALUType.CRA_NUT\n  );\n}\n\n/**\n * Detected codec type from NALU analysis\n */\nexport type DetectedCodec = 'h264' | 'h265' | 'unknown';\n\n/**\n * Result of NALU processing for frame encryption\n */\nexport interface NALUProcessingResult {\n  /** Number of unencrypted bytes at the start of the frame */\n  unencryptedBytes: number;\n  /** Detected codec type */\n  detectedCodec: DetectedCodec;\n  /** Whether this frame requires NALU processing */\n  requiresNALUProcessing: boolean;\n}\n\n/**\n * Detect codec type by examining NALU types in the data\n * @param data Frame data\n * @param naluIndices Indices where NALUs start\n * @returns Detected codec type\n */\nfunction detectCodecFromNALUs(data: Uint8Array, naluIndices: number[]): DetectedCodec {\n  for (const naluIndex of naluIndices) {\n    if (isH264SliceNALU(parseH264NALUType(data[naluIndex]))) return 'h264';\n    if (isH265SliceNALU(parseH265NALUType(data[naluIndex]))) return 'h265';\n  }\n  return 'unknown';\n}\n\n/**\n * Find the first slice NALU and return the number of unencrypted bytes\n * @param data Frame data\n * @param naluIndices Indices where NALUs start\n * @param codec Codec type to use for parsing\n * @returns Number of unencrypted bytes (index + 2) or null if no slice found\n */\nfunction findSliceNALUUnencryptedBytes(\n  data: Uint8Array,\n  naluIndices: number[],\n  codec: 'h264' | 'h265',\n): number | null {\n  for (const index of naluIndices) {\n    if (codec === 'h265') {\n      const type = parseH265NALUType(data[index]);\n      if (isH265SliceNALU(type)) {\n        return index + 2;\n      }\n    } else {\n      const type = parseH264NALUType(data[index]);\n      if (isH264SliceNALU(type)) {\n        return index + 2;\n      }\n    }\n  }\n  return null;\n}\n\n/**\n * Find all NALU start indices in a byte stream\n * Supports both H.264 and H.265 with 3-byte and 4-byte start codes\n *\n * This function slices the NALUs present in the supplied buffer, assuming it is already byte-aligned.\n * Code adapted from https://github.com/medooze/h264-frame-parser/blob/main/lib/NalUnits.ts to return indices only\n *\n * @param stream Byte stream containing NALUs\n * @returns Array of indices where NALUs start (after the start code)\n */\nfunction findNALUIndices(stream: Uint8Array): number[] {\n  const result: number[] = [];\n  let start = 0,\n    pos = 0,\n    searchLength = stream.length - 3; // Changed to -3 to handle 4-byte start codes\n\n  while (pos < searchLength) {\n    // skip until end of current NALU - check for both 3-byte and 4-byte start codes\n    while (pos < searchLength) {\n      // Check for 4-byte start code: 0x00 0x00 0x00 0x01\n      if (\n        pos < searchLength - 1 &&\n        stream[pos] === 0 &&\n        stream[pos + 1] === 0 &&\n        stream[pos + 2] === 0 &&\n        stream[pos + 3] === 1\n      ) {\n        break;\n      }\n      // Check for 3-byte start code: 0x00 0x00 0x01\n      if (stream[pos] === 0 && stream[pos + 1] === 0 && stream[pos + 2] === 1) {\n        break;\n      }\n      pos++;\n    }\n\n    if (pos >= searchLength) pos = stream.length;\n\n    // remove trailing zeros from current NALU\n    let end = pos;\n    while (end > start && stream[end - 1] === 0) end--;\n\n    // save current NALU\n    if (start === 0) {\n      if (end !== start) throw TypeError('byte stream contains leading data');\n    } else {\n      result.push(start);\n    }\n\n    // begin new NALU - determine start code length\n    let startCodeLength = 3;\n    if (\n      pos < stream.length - 3 &&\n      stream[pos] === 0 &&\n      stream[pos + 1] === 0 &&\n      stream[pos + 2] === 0 &&\n      stream[pos + 3] === 1\n    ) {\n      startCodeLength = 4;\n    }\n\n    start = pos = pos + startCodeLength;\n  }\n  return result;\n}\n\n/**\n * Process NALU data for frame encryption, detecting codec and finding unencrypted bytes\n * @param data Frame data\n * @param knownCodec Known codec from other sources (optional)\n * @returns NALU processing result\n */\nexport function processNALUsForEncryption(\n  data: Uint8Array,\n  knownCodec?: 'h264' | 'h265',\n): NALUProcessingResult {\n  const naluIndices = findNALUIndices(data);\n  const detectedCodec = knownCodec ?? detectCodecFromNALUs(data, naluIndices);\n\n  if (detectedCodec === 'unknown') {\n    return { unencryptedBytes: 0, detectedCodec, requiresNALUProcessing: false };\n  }\n\n  const unencryptedBytes = findSliceNALUUnencryptedBytes(data, naluIndices, detectedCodec);\n  if (unencryptedBytes === null) {\n    throw new TypeError('Could not find NALU');\n  }\n\n  return { unencryptedBytes, detectedCodec, requiresNALUProcessing: true };\n}\n", "import type { VideoCodec } from '../..';\n\n//  Payload definitions taken from https://github.com/livekit/livekit/blob/master/pkg/sfu/downtrack.go#L104\n\nexport const VP8KeyFrame8x8 = new Uint8Array([\n  0x10, 0x02, 0x00, 0x9d, 0x01, 0x2a, 0x08, 0x00, 0x08, 0x00, 0x00, 0x47, 0x08, 0x85, 0x85, 0x88,\n  0x85, 0x84, 0x88, 0x02, 0x02, 0x00, 0x0c, 0x0d, 0x60, 0x00, 0xfe, 0xff, 0xab, 0x50, 0x80,\n]);\n\nexport const H264KeyFrame2x2SPS = new Uint8Array([\n  0x67, 0x42, 0xc0, 0x1f, 0x0f, 0xd9, 0x1f, 0x88, 0x88, 0x84, 0x00, 0x00, 0x03, 0x00, 0x04, 0x00,\n  0x00, 0x03, 0x00, 0xc8, 0x3c, 0x60, 0xc9, 0x20,\n]);\n\nexport const H264KeyFrame2x2PPS = new Uint8Array([0x68, 0x87, 0xcb, 0x83, 0xcb, 0x20]);\n\nexport const H264KeyFrame2x2IDR = new Uint8Array([\n  0x65, 0x88, 0x84, 0x0a, 0xf2, 0x62, 0x80, 0x00, 0xa7, 0xbe,\n]);\n\nexport const H264KeyFrame2x2 = [H264KeyFrame2x2SPS, H264KeyFrame2x2PPS, H264KeyFrame2x2IDR];\n\nexport const OpusSilenceFrame = new Uint8Array([\n  0xf8, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n]);\n\n/**\n * Create a crypto hash using Web Crypto API for secure comparison operations\n */\nasync function cryptoHash(data: Uint8Array | ArrayBuffer): Promise<string> {\n  const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n  const hashArray = new Uint8Array(hashBuffer);\n  return Array.from(hashArray)\n    .map((b) => b.toString(16).padStart(2, '0'))\n    .join('');\n}\n\n/**\n * Pre-computed SHA-256 hashes for secure comparison operations\n */\nexport const CryptoHashes = {\n  VP8KeyFrame8x8: 'ef0161653d8b2b23aad46624b420af1d03ce48950e9fc85718028f91b50f9219',\n  H264KeyFrame2x2SPS: 'f0a0e09647d891d6d50aa898bce7108090375d0d55e50a2bb21147afee558e44',\n  H264KeyFrame2x2PPS: '61d9665eed71b6d424ae9539330a3bdd5cb386d4d781c808219a6e36750493a7',\n  H264KeyFrame2x2IDR: 'faffc26b68a2fc09096fa20f3351e706398b6f838a7500c8063472c2e476e90d',\n  OpusSilenceFrame: 'aad8d31fc56b2802ca500e58c2fb9d0b29ad71bb7cb52cd6530251eade188988',\n} as const;\n\n/**\n * Check if a byte array matches any of the known SIF payload frame types using secure crypto hashes\n */\nexport async function identifySifPayload(\n  data: Uint8Array | ArrayBuffer,\n): Promise<VideoCodec | 'opus' | null> {\n  const hash = await cryptoHash(data);\n\n  switch (hash) {\n    case CryptoHashes.VP8KeyFrame8x8:\n      return 'vp8';\n    case CryptoHashes.H264KeyFrame2x2SPS:\n      return 'h264';\n    case CryptoHashes.H264KeyFrame2x2PPS:\n      return 'h264';\n    case CryptoHashes.H264KeyFrame2x2IDR:\n      return 'h264';\n    case CryptoHashes.OpusSilenceFrame:\n      return 'opus';\n    default:\n      return null;\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-unused-vars */\n// TODO code inspired by https://github.com/webrtc/samples/blob/gh-pages/src/content/insertable-streams/endtoend-encryption/js/worker.js\nimport { EventEmitter } from 'events';\nimport type TypedEventEmitter from 'typed-emitter';\nimport { workerLogger } from '../../logger';\nimport type { VideoCodec } from '../../room/track/options';\nimport { ENCRYPTION_ALGORITHM, IV_LENGTH, UNENCRYPTED_BYTES } from '../constants';\nimport { CryptorError, CryptorErrorReason } from '../errors';\nimport { type CryptorCallbacks, CryptorEvent } from '../events';\nimport type { DecodeRatchetOptions, KeyProviderOptions, KeySet, RatchetResult } from '../types';\nimport { deriveKeys, isVideoFrame, needsRbspUnescaping, parseRbsp, writeRbsp } from '../utils';\nimport type { ParticipantKeyHandler } from './ParticipantKeyHandler';\nimport { processNALUsForEncryption } from './naluUtils';\nimport { identifySifPayload } from './sifPayload';\n\nexport const encryptionEnabledMap: Map<string, boolean> = new Map();\n\nexport interface FrameCryptorConstructor {\n  new (opts?: unknown): BaseFrameCryptor;\n}\n\nexport interface TransformerInfo {\n  readable: ReadableStream;\n  writable: WritableStream;\n  transformer: TransformStream;\n  abortController: AbortController;\n}\n\nexport class BaseFrameCryptor extends (EventEmitter as new () => TypedEventEmitter<CryptorCallbacks>) {\n  protected encodeFunction(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    controller: TransformStreamDefaultController,\n  ): Promise<any> {\n    throw Error('not implemented for subclass');\n  }\n\n  protected decodeFunction(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    controller: TransformStreamDefaultController,\n  ): Promise<any> {\n    throw Error('not implemented for subclass');\n  }\n}\n\n/**\n * Cryptor is responsible for en-/decrypting media frames.\n * Each Cryptor instance is responsible for en-/decrypting a single mediaStreamTrack.\n */\nexport class FrameCryptor extends BaseFrameCryptor {\n  private sendCounts: Map<number, number>;\n\n  private participantIdentity: string | undefined;\n\n  private trackId: string | undefined;\n\n  private keys: ParticipantKeyHandler;\n\n  private videoCodec?: VideoCodec;\n\n  private rtpMap: Map<number, VideoCodec>;\n\n  private keyProviderOptions: KeyProviderOptions;\n\n  /**\n   * used for detecting server injected unencrypted frames\n   */\n  private sifTrailer: Uint8Array;\n\n  private detectedCodec?: VideoCodec;\n\n  private isTransformActive: boolean = false;\n\n  constructor(opts: {\n    keys: ParticipantKeyHandler;\n    participantIdentity: string;\n    keyProviderOptions: KeyProviderOptions;\n    sifTrailer?: Uint8Array;\n  }) {\n    super();\n    this.sendCounts = new Map();\n    this.keys = opts.keys;\n    this.participantIdentity = opts.participantIdentity;\n    this.rtpMap = new Map();\n    this.keyProviderOptions = opts.keyProviderOptions;\n    this.sifTrailer = opts.sifTrailer ?? Uint8Array.from([]);\n  }\n\n  private get logContext() {\n    return {\n      participant: this.participantIdentity,\n      mediaTrackId: this.trackId,\n      fallbackCodec: this.videoCodec,\n    };\n  }\n\n  /**\n   * Assign a different participant to the cryptor.\n   * useful for transceiver re-use\n   * @param id\n   * @param keys\n   */\n  setParticipant(id: string, keys: ParticipantKeyHandler) {\n    workerLogger.debug('setting new participant on cryptor', {\n      ...this.logContext,\n      participant: id,\n    });\n    if (this.participantIdentity) {\n      workerLogger.error(\n        'cryptor has already a participant set, participant should have been unset before',\n        {\n          ...this.logContext,\n        },\n      );\n    }\n    this.participantIdentity = id;\n    this.keys = keys;\n  }\n\n  unsetParticipant() {\n    workerLogger.debug('unsetting participant', this.logContext);\n    this.participantIdentity = undefined;\n  }\n\n  isEnabled() {\n    if (this.participantIdentity) {\n      return encryptionEnabledMap.get(this.participantIdentity);\n    } else {\n      return undefined;\n    }\n  }\n\n  getParticipantIdentity() {\n    return this.participantIdentity;\n  }\n\n  getTrackId() {\n    return this.trackId;\n  }\n\n  /**\n   * Update the video codec used by the mediaStreamTrack\n   * @param codec\n   */\n  setVideoCodec(codec: VideoCodec) {\n    this.videoCodec = codec;\n  }\n\n  /**\n   * rtp payload type map used for figuring out codec of payload type when encoding\n   * @param map\n   */\n  setRtpMap(map: Map<number, VideoCodec>) {\n    this.rtpMap = map;\n  }\n\n  setupTransform(\n    operation: 'encode' | 'decode',\n    readable: ReadableStream<RTCEncodedVideoFrame | RTCEncodedAudioFrame>,\n    writable: WritableStream<RTCEncodedVideoFrame | RTCEncodedAudioFrame>,\n    trackId: string,\n    isReuse: boolean,\n    codec?: VideoCodec,\n  ) {\n    if (codec) {\n      workerLogger.info('setting codec on cryptor to', { codec });\n      this.videoCodec = codec;\n    }\n\n    workerLogger.debug('Setting up frame cryptor transform', {\n      operation,\n      passedTrackId: trackId,\n      codec,\n      ...this.logContext,\n    });\n\n    if (isReuse && this.isTransformActive) {\n      workerLogger.debug('reuse transform', {\n        ...this.logContext,\n      });\n      return;\n    }\n\n    const transformFn = operation === 'encode' ? this.encodeFunction : this.decodeFunction;\n    const transformStream = new TransformStream({\n      transform: transformFn.bind(this),\n    });\n\n    this.isTransformActive = true;\n\n    readable\n      .pipeThrough(transformStream)\n      .pipeTo(writable)\n      .catch((e) => {\n        workerLogger.warn(e);\n        this.emit(\n          CryptorEvent.Error,\n          e instanceof CryptorError\n            ? e\n            : new CryptorError(e.message, undefined, this.participantIdentity),\n        );\n      })\n      .finally(() => {\n        this.isTransformActive = false;\n      });\n    this.trackId = trackId;\n  }\n\n  setSifTrailer(trailer: Uint8Array) {\n    workerLogger.debug('setting SIF trailer', { ...this.logContext, trailer });\n    this.sifTrailer = trailer;\n  }\n\n  /**\n   * Function that will be injected in a stream and will encrypt the given encoded frames.\n   *\n   * @param {RTCEncodedVideoFrame|RTCEncodedAudioFrame} encodedFrame - Encoded video frame.\n   * @param {TransformStreamDefaultController} controller - TransportStreamController.\n   *\n   * The VP8 payload descriptor described in\n   * https://tools.ietf.org/html/rfc7741#section-4.2\n   * is part of the RTP packet and not part of the frame and is not controllable by us.\n   * This is fine as the SFU keeps having access to it for routing.\n   *\n   * The encrypted frame is formed as follows:\n   * 1) Find unencrypted byte length, depending on the codec, frame type and kind.\n   * 2) Form the GCM IV for the frame as described above.\n   * 3) Encrypt the rest of the frame using AES-GCM.\n   * 4) Allocate space for the encrypted frame.\n   * 5) Copy the unencrypted bytes to the start of the encrypted frame.\n   * 6) Append the ciphertext to the encrypted frame.\n   * 7) Append the IV.\n   * 8) Append a single byte for the key identifier.\n   * 9) Enqueue the encrypted frame for sending.\n   */\n  protected async encodeFunction(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    controller: TransformStreamDefaultController,\n  ) {\n    if (\n      !this.isEnabled() ||\n      // skip for encryption for empty dtx frames\n      encodedFrame.data.byteLength === 0\n    ) {\n      return controller.enqueue(encodedFrame);\n    }\n    const keySet = this.keys.getKeySet();\n    if (!keySet) {\n      this.emit(\n        CryptorEvent.Error,\n        new CryptorError(\n          `key set not found for ${\n            this.participantIdentity\n          } at index ${this.keys.getCurrentKeyIndex()}`,\n          CryptorErrorReason.MissingKey,\n          this.participantIdentity,\n        ),\n      );\n      return;\n    }\n    const { encryptionKey } = keySet;\n    const keyIndex = this.keys.getCurrentKeyIndex();\n\n    if (encryptionKey) {\n      const iv = this.makeIV(\n        encodedFrame.getMetadata().synchronizationSource ?? -1,\n        encodedFrame.timestamp,\n      );\n      let frameInfo = this.getUnencryptedBytes(encodedFrame);\n\n      // Thіs is not encrypted and contains the VP8 payload descriptor or the Opus TOC byte.\n      const frameHeader = new Uint8Array(encodedFrame.data, 0, frameInfo.unencryptedBytes);\n\n      // Frame trailer contains the R|IV_LENGTH and key index\n      const frameTrailer = new Uint8Array(2);\n\n      frameTrailer[0] = IV_LENGTH;\n      frameTrailer[1] = keyIndex;\n\n      // Construct frame trailer. Similar to the frame header described in\n      // https://tools.ietf.org/html/draft-omara-sframe-00#section-4.2\n      // but we put it at the end.\n      //\n      // ---------+-------------------------+-+---------+----\n      // payload  |IV...(length = IV_LENGTH)|R|IV_LENGTH|KID |\n      // ---------+-------------------------+-+---------+----\n      try {\n        const cipherText = await crypto.subtle.encrypt(\n          {\n            name: ENCRYPTION_ALGORITHM,\n            iv,\n            additionalData: new Uint8Array(encodedFrame.data, 0, frameHeader.byteLength),\n          },\n          encryptionKey,\n          new Uint8Array(encodedFrame.data, frameInfo.unencryptedBytes),\n        );\n\n        let newDataWithoutHeader = new Uint8Array(\n          cipherText.byteLength + iv.byteLength + frameTrailer.byteLength,\n        );\n        newDataWithoutHeader.set(new Uint8Array(cipherText)); // add ciphertext.\n        newDataWithoutHeader.set(new Uint8Array(iv), cipherText.byteLength); // append IV.\n        newDataWithoutHeader.set(frameTrailer, cipherText.byteLength + iv.byteLength); // append frame trailer.\n\n        if (frameInfo.requiresNALUProcessing) {\n          newDataWithoutHeader = writeRbsp(newDataWithoutHeader);\n        }\n\n        var newData = new Uint8Array(frameHeader.byteLength + newDataWithoutHeader.byteLength);\n        newData.set(frameHeader);\n        newData.set(newDataWithoutHeader, frameHeader.byteLength);\n\n        encodedFrame.data = newData.buffer;\n\n        return controller.enqueue(encodedFrame);\n      } catch (e: any) {\n        // TODO: surface this to the app.\n        workerLogger.error(e);\n      }\n    } else {\n      workerLogger.debug('failed to encrypt, emitting error', this.logContext);\n      this.emit(\n        CryptorEvent.Error,\n        new CryptorError(\n          `encryption key missing for encoding`,\n          CryptorErrorReason.MissingKey,\n          this.participantIdentity,\n        ),\n      );\n    }\n  }\n\n  /**\n   * Function that will be injected in a stream and will decrypt the given encoded frames.\n   *\n   * @param {RTCEncodedVideoFrame|RTCEncodedAudioFrame} encodedFrame - Encoded video frame.\n   * @param {TransformStreamDefaultController} controller - TransportStreamController.\n   */\n  protected async decodeFunction(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    controller: TransformStreamDefaultController,\n  ) {\n    if (\n      !this.isEnabled() ||\n      // skip for decryption for empty dtx frames\n      encodedFrame.data.byteLength === 0\n    ) {\n      return controller.enqueue(encodedFrame);\n    }\n\n    if (isFrameServerInjected(encodedFrame.data, this.sifTrailer)) {\n      encodedFrame.data = encodedFrame.data.slice(\n        0,\n        encodedFrame.data.byteLength - this.sifTrailer.byteLength,\n      );\n      if (await identifySifPayload(encodedFrame.data)) {\n        workerLogger.debug('enqueue SIF', this.logContext);\n        return controller.enqueue(encodedFrame);\n      } else {\n        workerLogger.warn('Unexpected SIF frame payload, dropping frame', this.logContext);\n        return;\n      }\n    }\n    const data = new Uint8Array(encodedFrame.data);\n    const keyIndex = data[encodedFrame.data.byteLength - 1];\n\n    if (this.keys.hasInvalidKeyAtIndex(keyIndex)) {\n      // drop frame\n      return;\n    }\n\n    if (this.keys.getKeySet(keyIndex)) {\n      try {\n        const decodedFrame = await this.decryptFrame(encodedFrame, keyIndex);\n        this.keys.decryptionSuccess(keyIndex);\n        if (decodedFrame) {\n          return controller.enqueue(decodedFrame);\n        }\n      } catch (error) {\n        if (error instanceof CryptorError && error.reason === CryptorErrorReason.InvalidKey) {\n          // emit an error if the key handler thinks we have a valid key\n          if (this.keys.hasValidKey) {\n            this.emit(CryptorEvent.Error, error);\n            this.keys.decryptionFailure(keyIndex);\n          }\n        } else {\n          workerLogger.warn('decoding frame failed', { error });\n        }\n      }\n    } else {\n      // emit an error if the key index is out of bounds but the key handler thinks we still have a valid key\n      workerLogger.warn(`skipping decryption due to missing key at index ${keyIndex}`);\n      this.emit(\n        CryptorEvent.Error,\n        new CryptorError(\n          `missing key at index ${keyIndex} for participant ${this.participantIdentity}`,\n          CryptorErrorReason.MissingKey,\n          this.participantIdentity,\n        ),\n      );\n      this.keys.decryptionFailure(keyIndex);\n    }\n  }\n\n  /**\n   * Function that will decrypt the given encoded frame. If the decryption fails, it will\n   * ratchet the key for up to RATCHET_WINDOW_SIZE times.\n   */\n  private async decryptFrame(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    keyIndex: number,\n    initialMaterial: KeySet | undefined = undefined,\n    ratchetOpts: DecodeRatchetOptions = { ratchetCount: 0 },\n  ): Promise<RTCEncodedVideoFrame | RTCEncodedAudioFrame | undefined> {\n    const keySet = this.keys.getKeySet(keyIndex);\n    if (!ratchetOpts.encryptionKey && !keySet) {\n      throw new TypeError(`no encryption key found for decryption of ${this.participantIdentity}`);\n    }\n    let frameInfo = this.getUnencryptedBytes(encodedFrame);\n\n    // Construct frame trailer. Similar to the frame header described in\n    // https://tools.ietf.org/html/draft-omara-sframe-00#section-4.2\n    // but we put it at the end.\n    //\n    // ---------+-------------------------+-+---------+----\n    // payload  |IV...(length = IV_LENGTH)|R|IV_LENGTH|KID |\n    // ---------+-------------------------+-+---------+----\n\n    try {\n      const frameHeader = new Uint8Array(encodedFrame.data, 0, frameInfo.unencryptedBytes);\n      var encryptedData = new Uint8Array(\n        encodedFrame.data,\n        frameHeader.length,\n        encodedFrame.data.byteLength - frameHeader.length,\n      );\n      if (frameInfo.requiresNALUProcessing && needsRbspUnescaping(encryptedData)) {\n        encryptedData = parseRbsp(encryptedData);\n        const newUint8 = new Uint8Array(frameHeader.byteLength + encryptedData.byteLength);\n        newUint8.set(frameHeader);\n        newUint8.set(encryptedData, frameHeader.byteLength);\n        encodedFrame.data = newUint8.buffer;\n      }\n\n      const frameTrailer = new Uint8Array(encodedFrame.data, encodedFrame.data.byteLength - 2, 2);\n\n      const ivLength = frameTrailer[0];\n      const iv = new Uint8Array(\n        encodedFrame.data,\n        encodedFrame.data.byteLength - ivLength - frameTrailer.byteLength,\n        ivLength,\n      );\n\n      const cipherTextStart = frameHeader.byteLength;\n      const cipherTextLength =\n        encodedFrame.data.byteLength -\n        (frameHeader.byteLength + ivLength + frameTrailer.byteLength);\n\n      const plainText = await crypto.subtle.decrypt(\n        {\n          name: ENCRYPTION_ALGORITHM,\n          iv,\n          additionalData: new Uint8Array(encodedFrame.data, 0, frameHeader.byteLength),\n        },\n        ratchetOpts.encryptionKey ?? keySet!.encryptionKey,\n        new Uint8Array(encodedFrame.data, cipherTextStart, cipherTextLength),\n      );\n\n      const newData = new ArrayBuffer(frameHeader.byteLength + plainText.byteLength);\n      const newUint8 = new Uint8Array(newData);\n\n      newUint8.set(new Uint8Array(encodedFrame.data, 0, frameHeader.byteLength));\n      newUint8.set(new Uint8Array(plainText), frameHeader.byteLength);\n\n      encodedFrame.data = newData;\n\n      return encodedFrame;\n    } catch (error: any) {\n      if (this.keyProviderOptions.ratchetWindowSize > 0) {\n        if (ratchetOpts.ratchetCount < this.keyProviderOptions.ratchetWindowSize) {\n          workerLogger.debug(\n            `ratcheting key attempt ${ratchetOpts.ratchetCount} of ${\n              this.keyProviderOptions.ratchetWindowSize\n            }, for kind ${encodedFrame instanceof RTCEncodedAudioFrame ? 'audio' : 'video'}`,\n          );\n\n          let ratchetedKeySet: KeySet | undefined;\n          let ratchetResult: RatchetResult | undefined;\n          if ((initialMaterial ?? keySet) === this.keys.getKeySet(keyIndex)) {\n            // only ratchet if the currently set key is still the same as the one used to decrypt this frame\n            // if not, it might be that a different frame has already ratcheted and we try with that one first\n            ratchetResult = await this.keys.ratchetKey(keyIndex, false);\n\n            ratchetedKeySet = await deriveKeys(\n              ratchetResult.cryptoKey,\n              this.keyProviderOptions.ratchetSalt,\n            );\n          }\n\n          const frame = await this.decryptFrame(encodedFrame, keyIndex, initialMaterial || keySet, {\n            ratchetCount: ratchetOpts.ratchetCount + 1,\n            encryptionKey: ratchetedKeySet?.encryptionKey,\n          });\n          if (frame && ratchetedKeySet) {\n            // before updating the keys, make sure that the keySet used for this frame is still the same as the currently set key\n            // if it's not, a new key might have been set already, which we don't want to override\n            if ((initialMaterial ?? keySet) === this.keys.getKeySet(keyIndex)) {\n              this.keys.setKeySet(ratchetedKeySet, keyIndex, ratchetResult);\n              // decryption was successful, set the new key index to reflect the ratcheted key set\n              this.keys.setCurrentKeyIndex(keyIndex);\n            }\n          }\n          return frame;\n        } else {\n          /**\n           * Because we only set a new key once decryption has been successful,\n           * we can be sure that we don't need to reset the key to the initial material at this point\n           * as the key has not been updated on the keyHandler instance\n           */\n\n          workerLogger.warn('maximum ratchet attempts exceeded');\n          throw new CryptorError(\n            `valid key missing for participant ${this.participantIdentity}`,\n            CryptorErrorReason.InvalidKey,\n            this.participantIdentity,\n          );\n        }\n      } else {\n        throw new CryptorError(\n          `Decryption failed: ${error.message}`,\n          CryptorErrorReason.InvalidKey,\n          this.participantIdentity,\n        );\n      }\n    }\n  }\n\n  /**\n   * Construct the IV used for AES-GCM and sent (in plain) with the packet similar to\n   * https://tools.ietf.org/html/rfc7714#section-8.1\n   * It concatenates\n   * - the 32 bit synchronization source (SSRC) given on the encoded frame,\n   * - the 32 bit rtp timestamp given on the encoded frame,\n   * - a send counter that is specific to the SSRC. Starts at a random number.\n   * The send counter is essentially the pictureId but we currently have to implement this ourselves.\n   * There is no XOR with a salt. Note that this IV leaks the SSRC to the receiver but since this is\n   * randomly generated and SFUs may not rewrite this is considered acceptable.\n   * The SSRC is used to allow demultiplexing multiple streams with the same key, as described in\n   *   https://tools.ietf.org/html/rfc3711#section-4.1.1\n   * The RTP timestamp is 32 bits and advances by the codec clock rate (90khz for video, 48khz for\n   * opus audio) every second. For video it rolls over roughly every 13 hours.\n   * The send counter will advance at the frame rate (30fps for video, 50fps for 20ms opus audio)\n   * every second. It will take a long time to roll over.\n   *\n   * See also https://developer.mozilla.org/en-US/docs/Web/API/AesGcmParams\n   */\n  private makeIV(synchronizationSource: number, timestamp: number) {\n    const iv = new ArrayBuffer(IV_LENGTH);\n    const ivView = new DataView(iv);\n\n    // having to keep our own send count (similar to a picture id) is not ideal.\n    if (!this.sendCounts.has(synchronizationSource)) {\n      // Initialize with a random offset, similar to the RTP sequence number.\n      this.sendCounts.set(synchronizationSource, Math.floor(Math.random() * 0xffff));\n    }\n\n    const sendCount = this.sendCounts.get(synchronizationSource) ?? 0;\n\n    ivView.setUint32(0, synchronizationSource);\n    ivView.setUint32(4, timestamp);\n    ivView.setUint32(8, timestamp - (sendCount % 0xffff));\n\n    this.sendCounts.set(synchronizationSource, sendCount + 1);\n\n    return iv;\n  }\n\n  private getUnencryptedBytes(frame: RTCEncodedVideoFrame | RTCEncodedAudioFrame): {\n    unencryptedBytes: number;\n    requiresNALUProcessing: boolean;\n  } {\n    // Handle audio frames\n    if (!isVideoFrame(frame)) {\n      return { unencryptedBytes: UNENCRYPTED_BYTES.audio, requiresNALUProcessing: false };\n    }\n\n    // Detect and track codec changes\n    const detectedCodec = this.getVideoCodec(frame) ?? this.videoCodec;\n    if (detectedCodec !== this.detectedCodec) {\n      workerLogger.debug('detected different codec', {\n        detectedCodec,\n        oldCodec: this.detectedCodec,\n        ...this.logContext,\n      });\n      this.detectedCodec = detectedCodec;\n    }\n\n    // Check for unsupported codecs\n    if (detectedCodec === 'av1') {\n      throw new Error(`${detectedCodec} is not yet supported for end to end encryption`);\n    }\n\n    // Handle VP8/VP9 codecs (no NALU processing needed)\n    if (detectedCodec === 'vp8') {\n      return { unencryptedBytes: UNENCRYPTED_BYTES[frame.type], requiresNALUProcessing: false };\n    }\n    if (detectedCodec === 'vp9') {\n      return { unencryptedBytes: 0, requiresNALUProcessing: false };\n    }\n\n    // Try NALU processing for H.264/H.265 codecs\n    try {\n      const knownCodec =\n        detectedCodec === 'h264' || detectedCodec === 'h265' ? detectedCodec : undefined;\n      const naluResult = processNALUsForEncryption(new Uint8Array(frame.data), knownCodec);\n\n      if (naluResult.requiresNALUProcessing) {\n        return {\n          unencryptedBytes: naluResult.unencryptedBytes,\n          requiresNALUProcessing: true,\n        };\n      }\n    } catch (e) {\n      workerLogger.debug('NALU processing failed, falling back to VP8 handling', {\n        error: e,\n        ...this.logContext,\n      });\n    }\n\n    // Fallback to VP8 handling\n    return { unencryptedBytes: UNENCRYPTED_BYTES[frame.type], requiresNALUProcessing: false };\n  }\n\n  /**\n   * inspects frame payloadtype if available and maps it to the codec specified in rtpMap\n   */\n  private getVideoCodec(frame: RTCEncodedVideoFrame): VideoCodec | undefined {\n    if (this.rtpMap.size === 0) {\n      return undefined;\n    }\n    const payloadType = frame.getMetadata().payloadType;\n    const codec = payloadType ? this.rtpMap.get(payloadType) : undefined;\n    return codec;\n  }\n}\n\n/**\n * we use a magic frame trailer to detect whether a frame is injected\n * by the livekit server and thus to be treated as unencrypted\n * @internal\n */\nexport function isFrameServerInjected(frameData: ArrayBuffer, trailerBytes: Uint8Array): boolean {\n  if (trailerBytes.byteLength === 0) {\n    return false;\n  }\n  const frameTrailer = new Uint8Array(\n    frameData.slice(frameData.byteLength - trailerBytes.byteLength),\n  );\n  return trailerBytes.every((value, index) => value === frameTrailer[index]);\n}\n", "import { EventEmitter } from 'events';\nimport type TypedEventEmitter from 'typed-emitter';\nimport { workerLogger } from '../../logger';\nimport { KeyHandlerEvent, type ParticipantKeyHandlerCallbacks } from '../events';\nimport type { KeyProviderOptions, KeySet, RatchetResult } from '../types';\nimport { deriveKeys, importKey, ratchet } from '../utils';\n\n// TODO ParticipantKeyHandlers currently don't get destroyed on participant disconnect\n// we could do this by having a separate worker message on participant disconnected.\n\n/**\n * ParticipantKeyHandler is responsible for providing a cryptor instance with the\n * en-/decryption key of a participant. It assumes that all tracks of a specific participant\n * are encrypted with the same key.\n * Additionally it exposes a method to ratchet a key which can be used by the cryptor either automatically\n * if decryption fails or can be triggered manually on both sender and receiver side.\n *\n */\nexport class ParticipantKeyHandler extends (EventEmitter as new () => TypedEventEmitter<ParticipantKeyHandlerCallbacks>) {\n  private currentKeyIndex: number;\n\n  private cryptoKeyRing: Array<KeySet | undefined>;\n\n  private decryptionFailureCounts: Array<number>;\n\n  private keyProviderOptions: KeyProviderOptions;\n\n  private ratchetPromiseMap: Map<number, Promise<RatchetResult>>;\n\n  private participantIdentity: string;\n\n  /**\n   * true if the current key has not been marked as invalid\n   */\n  get hasValidKey(): boolean {\n    return !this.hasInvalidKeyAtIndex(this.currentKeyIndex);\n  }\n\n  constructor(participantIdentity: string, keyProviderOptions: KeyProviderOptions) {\n    super();\n    this.currentKeyIndex = 0;\n    if (keyProviderOptions.keyringSize < 1 || keyProviderOptions.keyringSize > 256) {\n      throw new TypeError('Keyring size needs to be between 1 and 256');\n    }\n    this.cryptoKeyRing = new Array(keyProviderOptions.keyringSize).fill(undefined);\n    this.decryptionFailureCounts = new Array(keyProviderOptions.keyringSize).fill(0);\n    this.keyProviderOptions = keyProviderOptions;\n    this.ratchetPromiseMap = new Map();\n    this.participantIdentity = participantIdentity;\n  }\n\n  /**\n   * Returns true if the key at the given index is marked as invalid.\n   *\n   * @param keyIndex the index of the key\n   */\n  hasInvalidKeyAtIndex(keyIndex: number): boolean {\n    return (\n      this.keyProviderOptions.failureTolerance >= 0 &&\n      this.decryptionFailureCounts[keyIndex] > this.keyProviderOptions.failureTolerance\n    );\n  }\n\n  /**\n   * Informs the key handler that a decryption failure occurred for an encryption key.\n   * @internal\n   * @param keyIndex the key index for which the failure occurred. Defaults to the current key index.\n   */\n  decryptionFailure(keyIndex: number = this.currentKeyIndex): void {\n    if (this.keyProviderOptions.failureTolerance < 0) {\n      return;\n    }\n\n    this.decryptionFailureCounts[keyIndex] += 1;\n\n    if (this.decryptionFailureCounts[keyIndex] > this.keyProviderOptions.failureTolerance) {\n      workerLogger.warn(\n        `key for ${this.participantIdentity} at index ${keyIndex} is being marked as invalid`,\n      );\n    }\n  }\n\n  /**\n   * Informs the key handler that a frame was successfully decrypted using an encryption key.\n   * @internal\n   * @param keyIndex the key index for which the success occurred. Defaults to the current key index.\n   */\n  decryptionSuccess(keyIndex: number = this.currentKeyIndex): void {\n    this.resetKeyStatus(keyIndex);\n  }\n\n  /**\n   * Call this after user initiated ratchet or a new key has been set in order to make sure to mark potentially\n   * invalid keys as valid again\n   *\n   * @param keyIndex the index of the key. Defaults to the current key index.\n   */\n  resetKeyStatus(keyIndex?: number): void {\n    if (keyIndex === undefined) {\n      this.decryptionFailureCounts.fill(0);\n    } else {\n      this.decryptionFailureCounts[keyIndex] = 0;\n    }\n  }\n\n  /**\n   * Ratchets the current key (or the one at keyIndex if provided) and\n   * returns the ratcheted material\n   * if `setKey` is true (default), it will also set the ratcheted key directly on the crypto key ring\n   * @param keyIndex\n   * @param setKey\n   */\n  ratchetKey(keyIndex?: number, setKey = true): Promise<RatchetResult> {\n    const currentKeyIndex = keyIndex ?? this.getCurrentKeyIndex();\n\n    const existingPromise = this.ratchetPromiseMap.get(currentKeyIndex);\n    if (typeof existingPromise !== 'undefined') {\n      return existingPromise;\n    }\n    const ratchetPromise = new Promise<RatchetResult>(async (resolve, reject) => {\n      try {\n        const keySet = this.getKeySet(currentKeyIndex);\n        if (!keySet) {\n          throw new TypeError(\n            `Cannot ratchet key without a valid keyset of participant ${this.participantIdentity}`,\n          );\n        }\n        const currentMaterial = keySet.material;\n        const chainKey = await ratchet(currentMaterial, this.keyProviderOptions.ratchetSalt);\n        const newMaterial = await importKey(chainKey, currentMaterial.algorithm.name, 'derive');\n        const ratchetResult: RatchetResult = {\n          chainKey,\n          cryptoKey: newMaterial,\n        };\n        if (setKey) {\n          // Set the new key and emit a ratchet event with the ratcheted chain key\n          await this.setKeyFromMaterial(newMaterial, currentKeyIndex, ratchetResult);\n        }\n        resolve(ratchetResult);\n      } catch (e) {\n        reject(e);\n      } finally {\n        this.ratchetPromiseMap.delete(currentKeyIndex);\n      }\n    });\n    this.ratchetPromiseMap.set(currentKeyIndex, ratchetPromise);\n    return ratchetPromise;\n  }\n\n  /**\n   * takes in a key material with `deriveBits` and `deriveKey` set as key usages\n   * and derives encryption keys from the material and sets it on the key ring buffer\n   * together with the material\n   * also resets the valid key property and updates the currentKeyIndex\n   */\n  async setKey(material: CryptoKey, keyIndex = 0) {\n    await this.setKeyFromMaterial(material, keyIndex);\n    this.resetKeyStatus(keyIndex);\n  }\n\n  /**\n   * takes in a key material with `deriveBits` and `deriveKey` set as key usages\n   * and derives encryption keys from the material and sets it on the key ring buffers\n   * together with the material\n   * also updates the currentKeyIndex\n   */\n  async setKeyFromMaterial(\n    material: CryptoKey,\n    keyIndex: number,\n    ratchetedResult: RatchetResult | null = null,\n  ) {\n    const keySet = await deriveKeys(material, this.keyProviderOptions.ratchetSalt);\n    const newIndex = keyIndex >= 0 ? keyIndex % this.cryptoKeyRing.length : this.currentKeyIndex;\n    workerLogger.debug(`setting new key with index ${keyIndex}`, {\n      usage: material.usages,\n      algorithm: material.algorithm,\n      ratchetSalt: this.keyProviderOptions.ratchetSalt,\n    });\n    this.setKeySet(keySet, newIndex, ratchetedResult);\n    if (newIndex >= 0) this.currentKeyIndex = newIndex;\n  }\n\n  setKeySet(keySet: KeySet, keyIndex: number, ratchetedResult: RatchetResult | null = null) {\n    this.cryptoKeyRing[keyIndex % this.cryptoKeyRing.length] = keySet;\n\n    if (ratchetedResult) {\n      this.emit(KeyHandlerEvent.KeyRatcheted, ratchetedResult, this.participantIdentity, keyIndex);\n    }\n  }\n\n  async setCurrentKeyIndex(index: number) {\n    this.currentKeyIndex = index % this.cryptoKeyRing.length;\n    this.resetKeyStatus(index);\n  }\n\n  getCurrentKeyIndex() {\n    return this.currentKeyIndex;\n  }\n\n  /**\n   * returns currently used KeySet or the one at `keyIndex` if provided\n   * @param keyIndex\n   * @returns\n   */\n  getKeySet(keyIndex?: number) {\n    return this.cryptoKeyRing[keyIndex ?? this.currentKeyIndex];\n  }\n}\n", "import { workerLogger } from '../../logger';\nimport type { VideoCodec } from '../../room/track/options';\nimport { AsyncQueue } from '../../utils/AsyncQueue';\nimport { KEY_PROVIDER_DEFAULTS } from '../constants';\nimport { CryptorErrorReason } from '../errors';\nimport { CryptorEvent, KeyHandlerEvent } from '../events';\nimport type {\n  E2EEWorkerMessage,\n  ErrorMessage,\n  InitAck,\n  KeyProviderOptions,\n  RatchetMessage,\n  RatchetRequestMessage,\n  RatchetResult,\n  ScriptTransformOptions,\n} from '../types';\nimport { FrameCryptor, encryptionEnabledMap } from './FrameCryptor';\nimport { ParticipantKeyHandler } from './ParticipantKeyHandler';\n\nconst participantCryptors: FrameCryptor[] = [];\nconst participantKeys: Map<string, ParticipantKeyHandler> = new Map();\nlet sharedKeyHandler: ParticipantKeyHandler | undefined;\nlet messageQueue = new AsyncQueue();\n\nlet isEncryptionEnabled: boolean = false;\n\nlet useSharedKey: boolean = false;\n\nlet sifTrailer: Uint8Array | undefined;\n\nlet keyProviderOptions: KeyProviderOptions = KEY_PROVIDER_DEFAULTS;\n\nlet rtpMap: Map<number, VideoCodec> = new Map();\n\nworkerLogger.setDefaultLevel('info');\n\nonmessage = (ev) => {\n  messageQueue.run(async () => {\n    const { kind, data }: E2EEWorkerMessage = ev.data;\n\n    switch (kind) {\n      case 'init':\n        workerLogger.setLevel(data.loglevel);\n        workerLogger.info('worker initialized');\n        keyProviderOptions = data.keyProviderOptions;\n        useSharedKey = !!data.keyProviderOptions.sharedKey;\n        // acknowledge init successful\n        const ackMsg: InitAck = {\n          kind: 'initAck',\n          data: { enabled: isEncryptionEnabled },\n        };\n        postMessage(ackMsg);\n        break;\n      case 'enable':\n        setEncryptionEnabled(data.enabled, data.participantIdentity);\n        workerLogger.info(\n          `updated e2ee enabled status for ${data.participantIdentity} to ${data.enabled}`,\n        );\n        // acknowledge enable call successful\n        postMessage(ev.data);\n        break;\n      case 'decode':\n        let cryptor = getTrackCryptor(data.participantIdentity, data.trackId);\n        cryptor.setupTransform(\n          kind,\n          data.readableStream,\n          data.writableStream,\n          data.trackId,\n          data.isReuse,\n          data.codec,\n        );\n        break;\n      case 'encode':\n        let pubCryptor = getTrackCryptor(data.participantIdentity, data.trackId);\n        pubCryptor.setupTransform(\n          kind,\n          data.readableStream,\n          data.writableStream,\n          data.trackId,\n          data.isReuse,\n          data.codec,\n        );\n        break;\n      case 'setKey':\n        if (useSharedKey) {\n          await setSharedKey(data.key, data.keyIndex);\n        } else if (data.participantIdentity) {\n          workerLogger.info(\n            `set participant sender key ${data.participantIdentity} index ${data.keyIndex}`,\n          );\n          await getParticipantKeyHandler(data.participantIdentity).setKey(data.key, data.keyIndex);\n        } else {\n          workerLogger.error('no participant Id was provided and shared key usage is disabled');\n        }\n        break;\n      case 'removeTransform':\n        unsetCryptorParticipant(data.trackId, data.participantIdentity);\n        break;\n      case 'updateCodec':\n        getTrackCryptor(data.participantIdentity, data.trackId).setVideoCodec(data.codec);\n        workerLogger.info('updated codec', {\n          participantIdentity: data.participantIdentity,\n          trackId: data.trackId,\n          codec: data.codec,\n        });\n        break;\n      case 'setRTPMap':\n        // this is only used for the local participant\n        rtpMap = data.map;\n        participantCryptors.forEach((cr) => {\n          if (cr.getParticipantIdentity() === data.participantIdentity) {\n            cr.setRtpMap(data.map);\n          }\n        });\n        break;\n      case 'ratchetRequest':\n        handleRatchetRequest(data);\n        break;\n      case 'setSifTrailer':\n        handleSifTrailer(data.trailer);\n        break;\n      default:\n        break;\n    }\n  });\n};\n\nasync function handleRatchetRequest(data: RatchetRequestMessage['data']) {\n  if (useSharedKey) {\n    const keyHandler = getSharedKeyHandler();\n    await keyHandler.ratchetKey(data.keyIndex);\n    keyHandler.resetKeyStatus();\n  } else if (data.participantIdentity) {\n    const keyHandler = getParticipantKeyHandler(data.participantIdentity);\n    await keyHandler.ratchetKey(data.keyIndex);\n    keyHandler.resetKeyStatus();\n  } else {\n    workerLogger.error(\n      'no participant Id was provided for ratchet request and shared key usage is disabled',\n    );\n  }\n}\n\nfunction getTrackCryptor(participantIdentity: string, trackId: string) {\n  let cryptors = participantCryptors.filter((c) => c.getTrackId() === trackId);\n  if (cryptors.length > 1) {\n    const debugInfo = cryptors\n      .map((c) => {\n        return { participant: c.getParticipantIdentity() };\n      })\n      .join(',');\n    workerLogger.error(\n      `Found multiple cryptors for the same trackID ${trackId}. target participant: ${participantIdentity} `,\n      { participants: debugInfo },\n    );\n  }\n  let cryptor = cryptors[0];\n  if (!cryptor) {\n    workerLogger.info('creating new cryptor for', { participantIdentity, trackId });\n    if (!keyProviderOptions) {\n      throw Error('Missing keyProvider options');\n    }\n    cryptor = new FrameCryptor({\n      participantIdentity,\n      keys: getParticipantKeyHandler(participantIdentity),\n      keyProviderOptions,\n      sifTrailer,\n    });\n    cryptor.setRtpMap(rtpMap);\n    setupCryptorErrorEvents(cryptor);\n    participantCryptors.push(cryptor);\n  } else if (participantIdentity !== cryptor.getParticipantIdentity()) {\n    // assign new participant id to track cryptor and pass in correct key handler\n    cryptor.setParticipant(participantIdentity, getParticipantKeyHandler(participantIdentity));\n  }\n\n  return cryptor;\n}\n\nfunction getParticipantKeyHandler(participantIdentity: string) {\n  if (useSharedKey) {\n    return getSharedKeyHandler();\n  }\n  let keys = participantKeys.get(participantIdentity);\n  if (!keys) {\n    keys = new ParticipantKeyHandler(participantIdentity, keyProviderOptions);\n    keys.on(KeyHandlerEvent.KeyRatcheted, emitRatchetedKeys);\n    participantKeys.set(participantIdentity, keys);\n  }\n  return keys;\n}\n\nfunction getSharedKeyHandler() {\n  if (!sharedKeyHandler) {\n    workerLogger.debug('creating new shared key handler');\n    sharedKeyHandler = new ParticipantKeyHandler('shared-key', keyProviderOptions);\n  }\n  return sharedKeyHandler;\n}\n\nfunction unsetCryptorParticipant(trackId: string, participantIdentity: string) {\n  const cryptors = participantCryptors.filter(\n    (c) => c.getParticipantIdentity() === participantIdentity && c.getTrackId() === trackId,\n  );\n  if (cryptors.length > 1) {\n    workerLogger.error('Found multiple cryptors for the same participant and trackID combination', {\n      trackId,\n      participantIdentity,\n    });\n  }\n  const cryptor = cryptors[0];\n  if (!cryptor) {\n    workerLogger.warn('Could not unset participant on cryptor', { trackId, participantIdentity });\n  } else {\n    cryptor.unsetParticipant();\n  }\n}\n\nfunction setEncryptionEnabled(enable: boolean, participantIdentity: string) {\n  workerLogger.debug(`setting encryption enabled for all tracks of ${participantIdentity}`, {\n    enable,\n  });\n  encryptionEnabledMap.set(participantIdentity, enable);\n}\n\nasync function setSharedKey(key: CryptoKey, index?: number) {\n  workerLogger.info('set shared key', { index });\n  await getSharedKeyHandler().setKey(key, index);\n}\n\nfunction setupCryptorErrorEvents(cryptor: FrameCryptor) {\n  cryptor.on(CryptorEvent.Error, (error) => {\n    const msg: ErrorMessage = {\n      kind: 'error',\n      data: { error: new Error(`${CryptorErrorReason[error.reason]}: ${error.message}`) },\n    };\n    postMessage(msg);\n  });\n}\n\nfunction emitRatchetedKeys(\n  ratchetResult: RatchetResult,\n  participantIdentity: string,\n  keyIndex?: number,\n) {\n  const msg: RatchetMessage = {\n    kind: `ratchetKey`,\n    data: {\n      participantIdentity,\n      keyIndex,\n      ratchetResult,\n    },\n  };\n  postMessage(msg);\n}\n\nfunction handleSifTrailer(trailer: Uint8Array) {\n  sifTrailer = trailer;\n  participantCryptors.forEach((c) => {\n    c.setSifTrailer(trailer);\n  });\n}\n\n// Operations using RTCRtpScriptTransform.\n// @ts-ignore\nif (self.RTCTransformEvent) {\n  workerLogger.debug('setup transform event');\n  // @ts-ignore\n  self.onrtctransform = (event: RTCTransformEvent) => {\n    // @ts-ignore\n    const transformer = event.transformer;\n    workerLogger.debug('transformer', transformer);\n\n    const { kind, participantIdentity, trackId, codec } =\n      transformer.options as ScriptTransformOptions;\n    const cryptor = getTrackCryptor(participantIdentity, trackId);\n    workerLogger.debug('transform', { codec });\n    cryptor.setupTransform(kind, transformer.readable, transformer.writable, trackId, false, codec);\n  };\n}\n"], "names": ["root", "definition", "LogLevel", "LoggerNames", "noop", "undefinedType", "isIE", "window", "navigator", "test", "userAgent", "logMethods", "_loggersByName", "defaultLogger", "bindMethod", "obj", "methodName", "method", "bind", "Function", "prototype", "call", "e", "apply", "arguments", "traceForIE", "console", "log", "trace", "replaceLoggingMethods", "level", "this", "getLevel", "i", "length", "methodFactory", "name", "debug", "levels", "SILENT", "enableLoggingWhenConsoleArrives", "defaultMethodFactory", "_level", "_loggerName", "undefined", "realMethod", "<PERSON><PERSON>", "factory", "inheritedLevel", "defaultLevel", "userLevel", "self", "storageKey", "getPersistedLevel", "storedLevel", "localStorage", "ignore", "cookie", "document", "cookieName", "encodeURIComponent", "location", "indexOf", "exec", "slice", "normalizeLevel", "input", "toUpperCase", "TypeError", "TRACE", "DEBUG", "INFO", "WARN", "ERROR", "setLevel", "persist", "levelNum", "levelName", "persistLevelIfPossible", "setDefaultLevel", "resetLevel", "removeItem", "clearPersistedLevel", "enableAll", "disableAll", "rebuild", "<PERSON><PERSON><PERSON>", "initialLevel", "<PERSON><PERSON><PERSON><PERSON>", "logger", "_log", "noConflict", "getLoggers", "exports", "module", "livekitLogger", "Object", "values", "map", "info", "worker<PERSON>ogger", "QueueTaskStatus", "_", "constructor", "o", "_locking", "Promise", "resolve", "_locks", "isLocked", "lock", "s", "t", "l", "unlockNext", "c", "then", "ENCRYPTION_ALGORITHM", "UNENCRYPTED_BYTES", "key", "delta", "audio", "empty", "KEY_PROVIDER_DEFAULTS", "sharedKey", "ratchetSalt", "ratchetWindowSize", "failureTolerance", "keyringSize", "LivekitError", "Error", "code", "message", "super", "ConnectionErrorReason", "DataStreamErrorReason", "MediaDeviceFailure", "CryptorErrorReason", "KeyProviderEvent", "KeyHandlerEvent", "EncryptionEvent", "CryptorEvent", "getFailure", "error", "NotFound", "PermissionDenied", "DeviceInUse", "Other", "CryptorError", "reason", "InternalError", "participantIdentity", "ReflectOwnKeys", "R", "Reflect", "ReflectApply", "target", "receiver", "args", "ownKeys", "getOwnPropertySymbols", "getOwnPropertyNames", "concat", "NumberIsNaN", "Number", "isNaN", "value", "EventEmitter", "init", "eventsModule", "once", "emitter", "reject", "errorListener", "err", "removeListener", "resolver", "eventTargetAgnosticAddListener", "handler", "flags", "on", "addErrorHandlerIfEventEmitter", "_events", "_eventsCount", "_maxListeners", "defaultMaxListeners", "checkListener", "listener", "_getMaxListeners", "that", "_addListener", "type", "prepend", "m", "events", "existing", "warning", "create", "newListener", "emit", "unshift", "push", "warned", "w", "String", "count", "warn", "onceWrapper", "fired", "wrapFn", "_onceWrap", "state", "wrapped", "_listeners", "unwrap", "evlistener", "arr", "ret", "Array", "unwrapListeners", "arrayClone", "listenerCount", "n", "copy", "addEventListener", "wrapListener", "arg", "removeEventListener", "defineProperty", "enumerable", "get", "set", "RangeError", "getPrototypeOf", "setMaxListeners", "getMaxListeners", "do<PERSON><PERSON><PERSON>", "er", "context", "len", "listeners", "addListener", "prependListener", "prependOnceListener", "list", "position", "originalListener", "shift", "index", "pop", "spliceOne", "off", "removeAllListeners", "keys", "rawListeners", "eventNames", "getAlgoOptions", "algorithmName", "salt", "encodedSalt", "TextEncoder", "encode", "hash", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iterations", "<PERSON><PERSON><PERSON><PERSON>", "material", "algorithmOptions", "algorithm", "<PERSON><PERSON><PERSON>", "crypto", "subtle", "<PERSON><PERSON><PERSON>", "kH264NaluTypeMask", "H264NALUType", "H265NALUType", "parseH264NALUType", "startByte", "parseH265NALUType", "firstByte", "isH264SliceNALU", "naluType", "SLICE_IDR", "SLICE_NON_IDR", "isH265SliceNALU", "TRAIL_N", "TRAIL_R", "TSA_N", "TSA_R", "STSA_N", "STSA_R", "RADL_N", "RADL_R", "RASL_N", "RASL_R", "BLA_W_LP", "BLA_W_RADL", "BLA_N_LP", "IDR_W_RADL", "IDR_N_LP", "CRA_NUT", "processNALUsForEncryption", "data", "knownCodec", "naluIndices", "stream", "result", "start", "pos", "searchLength", "end", "startCodeLength", "findNALUIndices", "detectedCodec", "naluIndex", "detectCodecFromNALUs", "unencryptedBytes", "requiresNALUProcessing", "codec", "findSliceNALUUnencryptedBytes", "CryptoHashes", "identifySifPayload", "hash<PERSON><PERSON><PERSON>", "digest", "hashArray", "Uint8Array", "from", "b", "toString", "padStart", "join", "cryptoHash", "encryptionEnabledMap", "Map", "BaseFrameCryptor", "encodeFunction", "encodedFrame", "controller", "decodeFunction", "FrameCryptor", "opts", "isTransformActive", "sendCounts", "rtpMap", "keyProviderOptions", "sifTrailer", "_a", "logContext", "participant", "mediaTrackId", "trackId", "fallbackCodec", "videoCodec", "setParticipant", "id", "assign", "unsetParticipant", "isEnabled", "getParticipantIdentity", "getTrackId", "setVideoCodec", "setRtpMap", "setupTransform", "operation", "readable", "writable", "isReuse", "passedTrackId", "transformFn", "transformStream", "TransformStream", "transform", "pipeThrough", "pipeTo", "catch", "finally", "setSifTrailer", "trailer", "byteLength", "enqueue", "keySet", "getKeySet", "getCurrentKeyIndex", "<PERSON><PERSON><PERSON>", "keyIndex", "iv", "makeIV", "getMetadata", "synchronizationSource", "timestamp", "frameInfo", "getUnencryptedBytes", "frameHeader", "frameTrailer", "cipherText", "encrypt", "additionalData", "newDataWithoutHeader", "data_in", "dataOut", "numConsecutiveZeros", "byte", "writeRbsp", "newData", "buffer", "frameData", "trailerBytes", "every", "isFrameServerInjected", "hasInvalidKeyAtIndex", "decodedFrame", "decryptFrame", "decryptionSuccess", "Invalid<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decryptionFailure", "encodedFrame_1", "keyIndex_1", "_this", "initialMaterial", "ratchetOpts", "ratchetCount", "encryptedData", "needsRbspUnescaping", "parseRbsp", "newUint8", "iv<PERSON><PERSON><PERSON>", "cipherTextStart", "cipherTextLength", "plainText", "decrypt", "ratchetedKeySet", "ratchetResult", "RTCEncodedAudioFrame", "ratchetKey", "cryptoKey", "frame", "setKeySet", "setCurrentKeyIndex", "iv<PERSON><PERSON><PERSON>", "DataView", "has", "Math", "floor", "random", "sendCount", "setUint32", "isVideoFrame", "getVideoCodec", "oldCodec", "naluResult", "size", "payloadType", "ParticipantKeyHandler", "currentKeyIndex", "cryptoKeyRing", "fill", "decryptionFailureCounts", "ratchetPromiseMap", "resetKeyStatus", "<PERSON><PERSON><PERSON>", "existingPromise", "ratchetPromise", "__awaiter", "currentMaterial", "chainKey", "deriveBits", "ratchet", "newMaterial", "keyBytes_1", "keyBytes", "usage", "importKey", "setKeyFromMaterial", "delete", "material_1", "_this2", "ratchetedResult", "newIndex", "usages", "KeyRatcheted", "participantCryptors", "participant<PERSON><PERSON><PERSON>", "shared<PERSON>eyHandler", "messageQueue", "pendingTasks", "taskMutex", "Mutex", "nextTaskIndex", "run", "task", "taskInfo", "enqueuedAt", "Date", "now", "status", "WAITING", "unlock", "executedAt", "RUNNING", "COMPLETED", "flush", "snapshot", "useSharedKey", "getTrackCryptor", "cryptors", "filter", "debugInfo", "participants", "cryptor", "getParticipantKeyHandler", "msg", "kind", "postMessage", "setupCryptorErrorEvents", "getSharedKeyHandler", "emitRatchetedKeys", "onmessage", "ev", "loglevel", "enabled", "enable", "readableStream", "writableStream", "setSharedKey", "unsetCryptorParticipant", "for<PERSON>ach", "cr", "<PERSON><PERSON><PERSON><PERSON>", "handleRatchetRequest", "RTCTransformEvent", "onrtctransform", "event", "transformer", "options"], "mappings": "+cAMWA,EAAMC,ECJLC,EASAC,aDLDH,YAAMC,EAST,WAIJ,IAAIG,EAAO,WAAW,EAClBC,EAAgB,YAChBC,SAAeC,SAAWF,UAA0BE,OAAOC,YAAcH,GACzE,kBAAkBI,KAAKF,OAAOC,UAAUE,WAGxCC,EAAa,CACb,QACA,QACA,OACA,OACA,SAGAC,EAAiB,CAAA,EACjBC,EAAgB,KAGpB,SAASC,EAAWC,EAAKC,GACrB,IAAIC,EAASF,EAAIC,GACjB,GAA2B,mBAAhBC,EAAOC,KACd,OAAOD,EAAOC,KAAKH,GAEnB,IACI,OAAOI,SAASC,UAAUF,KAAKG,KAAKJ,EAAQF,EAC5D,CAAc,MAAOO,GAEL,OAAO,WACH,OAAOH,SAASC,UAAUG,MAAMA,MAAMN,EAAQ,CAACF,EAAKS,WACxE,CACA,CAEA,CAGI,SAASC,IACDC,QAAQC,MACJD,QAAQC,IAAIJ,MACZG,QAAQC,IAAIJ,MAAMG,QAASF,WAG3BL,SAASC,UAAUG,MAAMA,MAAMG,QAAQC,IAAK,CAACD,QAASF,aAG1DE,QAAQE,OAAOF,QAAQE,OACnC,CAwBI,SAASC,IAKL,IAHA,IAAIC,EAAQC,KAAKC,WAGRC,EAAI,EAAGA,EAAItB,EAAWuB,OAAQD,IAAK,CACxC,IAAIjB,EAAaL,EAAWsB,GAC5BF,KAAKf,GAAeiB,EAAIH,EACpB1B,EACA2B,KAAKI,cAAcnB,EAAYc,EAAOC,KAAKK,KAC3D,CAMQ,GAHAL,KAAKJ,IAAMI,KAAKM,aAGLX,UAAYrB,GAAiByB,EAAQC,KAAKO,OAAOC,OACxD,MAAO,kCAEnB,CAII,SAASC,EAAgCxB,GACrC,OAAO,kBACQU,UAAYrB,IACnBwB,EAAsBR,KAAKU,MAC3BA,KAAKf,GAAYO,MAAMQ,KAAMP,WAE7C,CACA,CAII,SAASiB,EAAqBzB,EAAY0B,EAAQC,GAE9C,OAxDJ,SAAoB3B,GAKhB,MAJmB,UAAfA,IACAA,EAAa,cAGNU,UAAYrB,IAEG,UAAfW,GAA0BV,EAC1BmB,OACwBmB,IAAxBlB,QAAQV,GACRF,EAAWY,QAASV,QACJ4B,IAAhBlB,QAAQC,IACRb,EAAWY,QAAS,OAEpBtB,EAEnB,CAwCeyC,CAAW7B,IACXwB,EAAgCjB,MAAMQ,KAAMP,UAC3D,CAEI,SAASsB,EAAOV,EAAMW,GAEpB,IASIC,EAMAC,EAMAC,EArBAC,EAAOpB,KAuBPqB,EAAa,WAyBjB,SAASC,IACL,IAAIC,EAEJ,UAAW/C,SAAWF,GAAkB+C,EAAxC,CAEA,IACIE,EAAc/C,OAAOgD,aAAaH,EAChD,CAAY,MAAOI,GAAQ,CAGjB,UAAWF,IAAgBjD,EACvB,IACI,IAAIoD,EAASlD,OAAOmD,SAASD,OACzBE,EAAaC,mBAAmBR,GAChCS,EAAWJ,EAAOK,QAAQH,EAAa,MACzB,IAAdE,IACAP,EAAc,WAAWS,KACrBN,EAAOO,MAAMH,EAAWF,EAAWzB,OAAS,IAC9C,GAExB,CAAgB,MAAOsB,GAAQ,CAQrB,YAJiCZ,IAA7BO,EAAKb,OAAOgB,KACZA,OAAcV,GAGXU,CAzB6C,CA0B9D,CAiBM,SAASW,EAAeC,GACpB,IAAIpC,EAAQoC,EAIZ,GAHqB,iBAAVpC,QAA2Dc,IAArCO,EAAKb,OAAOR,EAAMqC,iBAC/CrC,EAAQqB,EAAKb,OAAOR,EAAMqC,gBAET,iBAAVrC,GAAsBA,GAAS,GAAKA,GAASqB,EAAKb,OAAOC,OAChE,OAAOT,EAEP,MAAM,IAAIsC,UAAU,6CAA+CF,EAEjF,CAhF0B,iBAAT9B,EACTgB,GAAc,IAAMhB,EACK,iBAATA,IAChBgB,OAAaR,GAqFfO,EAAKf,KAAOA,EAEZe,EAAKb,OAAS,CAAE+B,MAAS,EAAGC,MAAS,EAAGC,KAAQ,EAAGC,KAAQ,EACvDC,MAAS,EAAGlC,OAAU,GAE1BY,EAAKhB,cAAgBY,GAAWN,EAEhCU,EAAKnB,SAAW,WACZ,OAAiB,MAAbkB,EACKA,EACkB,MAAhBD,EACFA,EAEAD,CAEnB,EAEMG,EAAKuB,SAAW,SAAU5C,EAAO6C,GAO7B,OANAzB,EAAYe,EAAenC,IACX,IAAZ6C,GArGR,SAAgCC,GAC5B,IAAIC,GAAalE,EAAWiE,IAAa,UAAUT,cAEnD,UAAW5D,SAAWF,GAAkB+C,EAAxC,CAGA,IAEI,YADA7C,OAAOgD,aAAaH,GAAcyB,EAEhD,CAAY,MAAOrB,GAAQ,CAGjB,IACIjD,OAAOmD,SAASD,OACdG,mBAAmBR,GAAc,IAAMyB,EAAY,GACnE,CAAY,MAAOrB,GAAQ,CAZmC,CAa9D,CAsFcsB,CAAuB5B,GAIpBrB,EAAsBR,KAAK8B,EAC5C,EAEMA,EAAK4B,gBAAkB,SAAUjD,GAC7BmB,EAAegB,EAAenC,GACzBuB,KACDF,EAAKuB,SAAS5C,GAAO,EAEnC,EAEMqB,EAAK6B,WAAa,WACd9B,EAAY,KApEhB,WACI,UAAW3C,SAAWF,GAAkB+C,EAAxC,CAGA,IACI7C,OAAOgD,aAAa0B,WAAW7B,EAC7C,CAAY,MAAOI,GAAQ,CAGjB,IACIjD,OAAOmD,SAASD,OACdG,mBAAmBR,GAAc,0CACjD,CAAY,MAAOI,GAAQ,CAXmC,CAY9D,CAwDU0B,GACArD,EAAsBR,KAAK8B,EACrC,EAEMA,EAAKgC,UAAY,SAASR,GACtBxB,EAAKuB,SAASvB,EAAKb,OAAO+B,MAAOM,EAC3C,EAEMxB,EAAKiC,WAAa,SAAST,GACvBxB,EAAKuB,SAASvB,EAAKb,OAAOC,OAAQoC,EAC5C,EAEMxB,EAAKkC,QAAU,WAMX,GALIxE,IAAkBsC,IAClBH,EAAiBiB,EAAepD,EAAcmB,aAElDH,EAAsBR,KAAK8B,GAEvBtC,IAAkBsC,EAClB,IAAK,IAAImC,KAAa1E,EACpBA,EAAe0E,GAAWD,SAG1C,EAGMrC,EAAiBiB,EACbpD,EAAgBA,EAAcmB,WAAa,QAE/C,IAAIuD,EAAelC,IACC,MAAhBkC,IACArC,EAAYe,EAAesB,IAE/B1D,EAAsBR,KAAK8B,EACjC,EAQItC,EAAgB,IAAIiC,GAEN0C,UAAY,SAAmBpD,GACzC,GAAqB,iBAATA,GAAqC,iBAATA,GAA+B,KAATA,EAC1D,MAAM,IAAIgC,UAAU,kDAGxB,IAAIqB,EAAS7E,EAAewB,GAO5B,OANKqD,IACDA,EAAS7E,EAAewB,GAAQ,IAAIU,EAChCV,EACAvB,EAAcsB,gBAGfsD,CACf,EAGI,IAAIC,SAAenF,SAAWF,EAAiBE,OAAOoB,SAAMiB,EAiB5D,OAhBA/B,EAAc8E,WAAa,WAMvB,cALWpF,SAAWF,GACfE,OAAOoB,MAAQd,IAClBN,OAAOoB,IAAM+D,GAGV7E,CACf,EAEIA,EAAc+E,WAAa,WACvB,OAAOhF,CACf,EAGIC,EAAuB,QAAIA,EAEpBA,CACX,QA1VoDgF,QAC5CC,EAAAD,QAAiB5F,IAEjBD,EAAK2B,IAAM1B,iBCXnB,SAAYC,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAPD,CAAYA,IAAAA,EAAQ,CAAA,IASpB,SAAYC,GACVA,EAAA,QAAA,UACAA,EAAA,KAAA,eACAA,EAAA,YAAA,sBACAA,EAAA,MAAA,gBACAA,EAAA,YAAA,4BACAA,EAAA,OAAA,iBACAA,EAAA,OAAA,iBACAA,EAAA,UAAA,qBACAA,EAAA,YAAA,uBACAA,EAAA,KAAA,SACD,CAXD,CAAYA,IAAAA,EAAW,CAAA,IA0BvB,IAAI4F,EAAgBpE,EAAAA,UAAc,WACXqE,OAAOC,OAAO9F,GAAa+F,KAAK9D,GAAST,EAAAA,UAAcS,KAE9E2D,EAAchB,gBAAgB7E,EAASiG,MAqDhC,MAAMC,EAAezE,EAAAA,UAAc,eCzFrC0E,wJCJE,MAAMC,EAKXC,WAAAA,GAJQC,EAAAzE,KAAA,YAEAyE,EAAAzE,KAAA,UAGDA,KAAA0E,SAAWC,QAAQC,UACxB5E,KAAK6E,OAAS,CAChB,CAEAC,QAAAA,GACE,OAAO9E,KAAK6E,OAAS,CACvB,CAEAE,IAAAA,GAGM,IAAAC,EAFJhF,KAAK6E,QAAU,EAIf,MAAMI,EAAW,IAAIN,SAClBO,GACEF,EAAaG,KACZnF,KAAK6E,QAAU,EACPK,GAAA,IAIRE,EAAapF,KAAK0E,SAASW,MAAK,IAAML,IAE5C,OAAAhF,KAAK0E,SAAW1E,KAAK0E,SAASW,MAAK,IAAMJ,IAElCG,CACT,GD5BF,SAAKd,GACHA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,UAAA,GAAA,WACD,CAJD,CAAKA,IAAAA,EAAe,CAAA,IEFb,MAAMgB,EAAuB,UAevBC,EAAoB,CAC/BC,IAAK,GACLC,MAAO,EACPC,MAAO,EACPC,MAAO,GAYIC,EAA4C,CACvDC,WAAW,EACXC,YAJkB,uBAKlBC,kBAAmB,EACnBC,iBAhC0C,GAiC1CC,YAAa,ICpCT,MAAOC,UAAqBC,MAGhC3B,WAAAA,CAAY4B,EAAcC,GACxBC,MAAMD,GAAW,wBACjBrG,KAAKK,KAAO,eACZL,KAAKoG,KAAOA,CACd,EAGF,IAAYG,EAsGAC,EAiCAC,ECjJAC,ECEAC,EAmBAC,EAcAC,EAiBAC,GF1CZ,SAAYP,GACVA,EAAAA,EAAA,WAAA,GAAA,aACAA,EAAAA,EAAA,kBAAA,GAAA,oBACAA,EAAAA,EAAA,cAAA,GAAA,gBACAA,EAAAA,EAAA,UAAA,GAAA,YACAA,EAAAA,EAAA,aAAA,GAAA,eACAA,EAAAA,EAAA,QAAA,GAAA,SACD,CAPD,CAAYA,IAAAA,EAAqB,CAAA,IAsGjC,SAAYC,GAEVA,EAAAA,EAAA,cAAA,GAAA,gBAGAA,EAAAA,EAAA,YAAA,GAAA,cAGAA,EAAAA,EAAA,aAAA,GAAA,eAGAA,EAAAA,EAAA,eAAA,GAAA,iBAGAA,EAAAA,EAAA,WAAA,GAAA,aAGAA,EAAAA,EAAA,yBAAA,GAAA,0BACD,CAlBD,CAAYA,IAAAA,EAAqB,CAAA,IAiCjC,SAAYC,GAEVA,EAAA,iBAAA,mBAEAA,EAAA,SAAA,WAEAA,EAAA,YAAA,cACAA,EAAA,MAAA,OACD,CARD,CAAYA,IAAAA,EAAkB,CAAA,IAU9B,SAAiBA,GACCA,EAAAM,WAAhB,SAA2BC,GACzB,GAAIA,GAAS,SAAUA,EACrB,MAAmB,kBAAfA,EAAM3G,MAA2C,yBAAf2G,EAAM3G,KACnCoG,EAAmBQ,SAET,oBAAfD,EAAM3G,MAA6C,0BAAf2G,EAAM3G,KACrCoG,EAAmBS,iBAET,qBAAfF,EAAM3G,MAA8C,oBAAf2G,EAAM3G,KACtCoG,EAAmBU,YAErBV,EAAmBW,KAE9B,CACD,CAfD,CAAiBX,IAAAA,EAAkB,CAAA,IC3JnC,SAAYC,GACVA,EAAAA,EAAA,WAAA,GAAA,aACAA,EAAAA,EAAA,WAAA,GAAA,aACAA,EAAAA,EAAA,cAAA,GAAA,eACD,CAJD,CAAYA,IAAAA,EAAkB,CAAA,IAMxB,MAAOW,UAAqBnB,EAKhC1B,WAAAA,CACE6B,GAE4B,IAD5BiB,EAAA7H,UAAAU,OAAA,QAAAU,IAAApB,UAAA,GAAAA,UAAA,GAA6BiH,EAAmBa,cAChDC,EAA4B/H,UAAAU,OAAA,EAAAV,kBAAAoB,EAE5ByF,MAAM,GAAID,GACVrG,KAAKsH,OAASA,EACdtH,KAAKwH,oBAAsBA,CAC7B,GCjBF,SAAYb,GACVA,EAAA,OAAA,SAEAA,EAAA,eAAA,iBAGAA,EAAA,aAAA,cACD,CAPD,CAAYA,IAAAA,EAAgB,CAAA,IAmB5B,SAAYC,GAGVA,EAAA,aAAA,cACD,CAJD,CAAYA,IAAAA,EAAe,CAAA,IAc3B,SAAYC,GACVA,EAAA,mCAAA,qCACAA,EAAA,gBAAA,iBACD,CAHD,CAAYA,IAAAA,EAAe,CAAA,IAiB3B,SAAYC,GACVA,EAAA,MAAA,cACD,CAFD,CAAYA,IAAAA,EAAY,CAAA,oEC/BxB,IAOIW,EAPAC,EAAuB,iBAAZC,QAAuBA,QAAU,KAC5CC,EAAeF,GAAwB,mBAAZA,EAAElI,MAC7BkI,EAAElI,MACF,SAAsBqI,EAAQC,EAAUC,GACxC,OAAO3I,SAASC,UAAUG,MAAMF,KAAKuI,EAAQC,EAAUC,EAC3D,EAIEN,EADEC,GAA0B,mBAAdA,EAAEM,QACCN,EAAEM,QACV/D,OAAOgE,sBACC,SAAwBJ,GACvC,OAAO5D,OAAOiE,oBAAoBL,GAC/BM,OAAOlE,OAAOgE,sBAAsBJ,GAC3C,EAEmB,SAAwBA,GACvC,OAAO5D,OAAOiE,oBAAoBL,EACtC,EAOA,IAAIO,EAAcC,OAAOC,OAAS,SAAqBC,GACrD,OAAOA,GAAUA,CACnB,EAEA,SAASC,IACPA,EAAaC,KAAKnJ,KAAKU,KACzB,CACA0I,EAAA5E,QAAiB0E,EACjBE,EAAA5E,QAAA6E,KAwYA,SAAcC,EAASvI,GACrB,OAAO,IAAIsE,SAAQ,SAAUC,EAASiE,GACpC,SAASC,EAAcC,GACrBH,EAAQI,eAAe3I,EAAM4I,GAC7BJ,EAAOE,EACb,CAEI,SAASE,IAC+B,mBAA3BL,EAAQI,gBACjBJ,EAAQI,eAAe,QAASF,GAElClE,EAAQ,GAAG3C,MAAM3C,KAAKG,WAC5B,CAEIyJ,EAA+BN,EAASvI,EAAM4I,EAAU,CAAEN,MAAM,IACnD,UAATtI,GAMR,SAAuCuI,EAASO,EAASC,GAC7B,mBAAfR,EAAQS,IACjBH,EAA+BN,EAAS,QAASO,EAASC,EAE9D,CATME,CAA8BV,EAASE,EAAe,CAAEH,MAAM,GAEpE,GACA,EAxZAH,EAAaA,aAAeA,EAE5BA,EAAanJ,UAAUkK,aAAU1I,EACjC2H,EAAanJ,UAAUmK,aAAe,EACtChB,EAAanJ,UAAUoK,mBAAgB5I,EAIvC,IAAI6I,EAAsB,GAE1B,SAASC,EAAcC,GACrB,GAAwB,mBAAbA,EACT,MAAM,IAAIvH,UAAU,0EAA4EuH,EAEpG,CAoCA,SAASC,EAAiBC,GACxB,YAA2BjJ,IAAvBiJ,EAAKL,cACAjB,EAAakB,oBACfI,EAAKL,aACd,CAkDA,SAASM,EAAalC,EAAQmC,EAAMJ,EAAUK,GAC5C,IAAIC,EACAC,EACAC,EA1HsBC,EAgJ1B,GApBAV,EAAcC,QAGC/I,KADfsJ,EAAStC,EAAO0B,UAEdY,EAAStC,EAAO0B,QAAUtF,OAAOqG,OAAO,MACxCzC,EAAO2B,aAAe,SAIK3I,IAAvBsJ,EAAOI,cACT1C,EAAO2C,KAAK,cAAeR,EACfJ,EAASA,SAAWA,EAASA,SAAWA,GAIpDO,EAAStC,EAAO0B,SAElBa,EAAWD,EAAOH,SAGHnJ,IAAbuJ,EAEFA,EAAWD,EAAOH,GAAQJ,IACxB/B,EAAO2B,kBAeT,GAbwB,mBAAbY,EAETA,EAAWD,EAAOH,GAChBC,EAAU,CAACL,EAAUQ,GAAY,CAACA,EAAUR,GAErCK,EACTG,EAASK,QAAQb,GAEjBQ,EAASM,KAAKd,IAIhBM,EAAIL,EAAiBhC,IACb,GAAKuC,EAASjK,OAAS+J,IAAME,EAASO,OAAQ,CACpDP,EAASO,QAAS,EAGlB,IAAIC,EAAI,IAAIzE,MAAM,+CACEiE,EAASjK,OAAS,IAAM0K,OAAOb,GADjC,qEAIlBY,EAAEvK,KAAO,8BACTuK,EAAEhC,QAAUf,EACZ+C,EAAEZ,KAAOA,EACTY,EAAEE,MAAQV,EAASjK,OA7KGkK,EA8KHO,EA7KnBjL,SAAWA,QAAQoL,MAAMpL,QAAQoL,KAAKV,EA8K5C,CAGE,OAAOxC,CACT,CAaA,SAASmD,IACP,IAAKhL,KAAKiL,MAGR,OAFAjL,KAAK6H,OAAOmB,eAAehJ,KAAKgK,KAAMhK,KAAKkL,QAC3ClL,KAAKiL,OAAQ,EACY,IAArBxL,UAAUU,OACLH,KAAK4J,SAAStK,KAAKU,KAAK6H,QAC1B7H,KAAK4J,SAASpK,MAAMQ,KAAK6H,OAAQpI,UAE5C,CAEA,SAAS0L,EAAUtD,EAAQmC,EAAMJ,GAC/B,IAAIwB,EAAQ,CAAEH,OAAO,EAAOC,YAAQrK,EAAWgH,OAAQA,EAAQmC,KAAMA,EAAMJ,SAAUA,GACjFyB,EAAUL,EAAY7L,KAAKiM,GAG/B,OAFAC,EAAQzB,SAAWA,EACnBwB,EAAMF,OAASG,EACRA,CACT,CAyHA,SAASC,EAAWzD,EAAQmC,EAAMuB,GAChC,IAAIpB,EAAStC,EAAO0B,QAEpB,QAAe1I,IAAXsJ,EACF,MAAO,GAET,IAAIqB,EAAarB,EAAOH,GACxB,YAAmBnJ,IAAf2K,EACK,GAEiB,mBAAfA,EACFD,EAAS,CAACC,EAAW5B,UAAY4B,GAAc,CAACA,GAElDD,EAsDT,SAAyBE,GAEvB,IADA,IAAIC,EAAM,IAAIC,MAAMF,EAAItL,QACfD,EAAI,EAAGA,EAAIwL,EAAIvL,SAAUD,EAChCwL,EAAIxL,GAAKuL,EAAIvL,GAAG0J,UAAY6B,EAAIvL,GAElC,OAAOwL,CACT,CA3DIE,CAAgBJ,GAAcK,EAAWL,EAAYA,EAAWrL,OACpE,CAmBA,SAAS2L,EAAc9B,GACrB,IAAIG,EAASnK,KAAKuJ,QAElB,QAAe1I,IAAXsJ,EAAsB,CACxB,IAAIqB,EAAarB,EAAOH,GAExB,GAA0B,mBAAfwB,EACT,OAAO,EACF,QAAmB3K,IAAf2K,EACT,OAAOA,EAAWrL,MAExB,CAEE,OAAO,CACT,CAMA,SAAS0L,EAAWJ,EAAKM,GAEvB,IADA,IAAIC,EAAO,IAAIL,MAAMI,GACZ7L,EAAI,EAAGA,EAAI6L,IAAK7L,EACvB8L,EAAK9L,GAAKuL,EAAIvL,GAChB,OAAO8L,CACT,CA2CA,SAAS9C,EAA+BN,EAASvI,EAAMuJ,EAAUR,GAC/D,GAA0B,mBAAfR,EAAQS,GACbD,EAAMT,KACRC,EAAQD,KAAKtI,EAAMuJ,GAEnBhB,EAAQS,GAAGhJ,EAAMuJ,OAEd,IAAwC,mBAA7BhB,EAAQqD,iBAYxB,MAAM,IAAI5J,UAAU,6EAA+EuG,GATnGA,EAAQqD,iBAAiB5L,GAAM,SAAS6L,EAAaC,GAG/C/C,EAAMT,MACRC,EAAQwD,oBAAoB/L,EAAM6L,GAEpCtC,EAASuC,EACf,GAGA,CACA,QAraAlI,OAAOoI,eAAe7D,EAAc,sBAAuB,CACzD8D,YAAY,EACZC,IAAK,WACH,OAAO7C,CACX,EACE8C,IAAK,SAASL,GACZ,GAAmB,iBAARA,GAAoBA,EAAM,GAAK/D,EAAY+D,GACpD,MAAM,IAAIM,WAAW,kGAAoGN,EAAM,KAEjIzC,EAAsByC,CAC1B,IAGA3D,EAAaC,KAAO,gBAEG5H,IAAjBb,KAAKuJ,SACLvJ,KAAKuJ,UAAYtF,OAAOyI,eAAe1M,MAAMuJ,UAC/CvJ,KAAKuJ,QAAUtF,OAAOqG,OAAO,MAC7BtK,KAAKwJ,aAAe,GAGtBxJ,KAAKyJ,cAAgBzJ,KAAKyJ,oBAAiB5I,CAC7C,EAIA2H,EAAanJ,UAAUsN,gBAAkB,SAAyBZ,GAChE,GAAiB,iBAANA,GAAkBA,EAAI,GAAK3D,EAAY2D,GAChD,MAAM,IAAIU,WAAW,gFAAkFV,EAAI,KAG7G,OADA/L,KAAKyJ,cAAgBsC,EACd/L,IACT,EAQAwI,EAAanJ,UAAUuN,gBAAkB,WACvC,OAAO/C,EAAiB7J,KAC1B,EAEAwI,EAAanJ,UAAUmL,KAAO,SAAcR,GAE1C,IADA,IAAIjC,EAAO,GACF7H,EAAI,EAAGA,EAAIT,UAAUU,OAAQD,IAAK6H,EAAK2C,KAAKjL,UAAUS,IAC/D,IAAI2M,EAAoB,UAAT7C,EAEXG,EAASnK,KAAKuJ,QAClB,QAAe1I,IAAXsJ,EACF0C,EAAWA,QAA4BhM,IAAjBsJ,EAAOnD,WAC1B,IAAK6F,EACR,OAAO,EAGT,GAAIA,EAAS,CACX,IAAIC,EAGJ,GAFI/E,EAAK5H,OAAS,IAChB2M,EAAK/E,EAAK,IACR+E,aAAc3G,MAGhB,MAAM2G,EAGR,IAAI/D,EAAM,IAAI5C,MAAM,oBAAsB2G,EAAK,KAAOA,EAAGzG,QAAU,IAAM,KAEzE,MADA0C,EAAIgE,QAAUD,EACR/D,CACV,CAEE,IAAII,EAAUgB,EAAOH,GAErB,QAAgBnJ,IAAZsI,EACF,OAAO,EAET,GAAuB,mBAAZA,EACTvB,EAAauB,EAASnJ,KAAM+H,OAE5B,KAAIiF,EAAM7D,EAAQhJ,OACd8M,EAAYpB,EAAW1C,EAAS6D,GACpC,IAAS9M,EAAI,EAAGA,EAAI8M,IAAO9M,EACzB0H,EAAaqF,EAAU/M,GAAIF,KAAM+H,EAHX,CAM1B,OAAO,CACT,EAgEAS,EAAanJ,UAAU6N,YAAc,SAAqBlD,EAAMJ,GAC9D,OAAOG,EAAa/J,KAAMgK,EAAMJ,GAAU,EAC5C,EAEApB,EAAanJ,UAAUgK,GAAKb,EAAanJ,UAAU6N,YAEnD1E,EAAanJ,UAAU8N,gBACnB,SAAyBnD,EAAMJ,GAC7B,OAAOG,EAAa/J,KAAMgK,EAAMJ,GAAU,EAChD,EAoBApB,EAAanJ,UAAUsJ,KAAO,SAAcqB,EAAMJ,GAGhD,OAFAD,EAAcC,GACd5J,KAAKqJ,GAAGW,EAAMmB,EAAUnL,KAAMgK,EAAMJ,IAC7B5J,IACT,EAEAwI,EAAanJ,UAAU+N,oBACnB,SAA6BpD,EAAMJ,GAGjC,OAFAD,EAAcC,GACd5J,KAAKmN,gBAAgBnD,EAAMmB,EAAUnL,KAAMgK,EAAMJ,IAC1C5J,IACb,EAGAwI,EAAanJ,UAAU2J,eACnB,SAAwBgB,EAAMJ,GAC5B,IAAIyD,EAAMlD,EAAQmD,EAAUpN,EAAGqN,EAK/B,GAHA5D,EAAcC,QAGC/I,KADfsJ,EAASnK,KAAKuJ,SAEZ,OAAOvJ,KAGT,QAAaa,KADbwM,EAAOlD,EAAOH,IAEZ,OAAOhK,KAET,GAAIqN,IAASzD,GAAYyD,EAAKzD,WAAaA,EACb,KAAtB5J,KAAKwJ,aACTxJ,KAAKuJ,QAAUtF,OAAOqG,OAAO,cAEtBH,EAAOH,GACVG,EAAOnB,gBACThJ,KAAKwK,KAAK,iBAAkBR,EAAMqD,EAAKzD,UAAYA,SAElD,GAAoB,mBAATyD,EAAqB,CAGrC,IAFAC,GAAW,EAENpN,EAAImN,EAAKlN,OAAS,EAAGD,GAAK,EAAGA,IAChC,GAAImN,EAAKnN,KAAO0J,GAAYyD,EAAKnN,GAAG0J,WAAaA,EAAU,CACzD2D,EAAmBF,EAAKnN,GAAG0J,SAC3B0D,EAAWpN,EACX,KACZ,CAGQ,GAAIoN,EAAW,EACb,OAAOtN,KAEQ,IAAbsN,EACFD,EAAKG,QAiIf,SAAmBH,EAAMI,GACvB,KAAOA,EAAQ,EAAIJ,EAAKlN,OAAQsN,IAC9BJ,EAAKI,GAASJ,EAAKI,EAAQ,GAC7BJ,EAAKK,KACP,CAnIUC,CAAUN,EAAMC,GAGE,IAAhBD,EAAKlN,SACPgK,EAAOH,GAAQqD,EAAK,SAEQxM,IAA1BsJ,EAAOnB,gBACThJ,KAAKwK,KAAK,iBAAkBR,EAAMuD,GAAoB3D,EAChE,CAEM,OAAO5J,IACb,EAEAwI,EAAanJ,UAAUuO,IAAMpF,EAAanJ,UAAU2J,eAEpDR,EAAanJ,UAAUwO,mBACnB,SAA4B7D,GAC1B,IAAIiD,EAAW9C,EAAQjK,EAGvB,QAAeW,KADfsJ,EAASnK,KAAKuJ,SAEZ,OAAOvJ,KAGT,QAA8Ba,IAA1BsJ,EAAOnB,eAUT,OATyB,IAArBvJ,UAAUU,QACZH,KAAKuJ,QAAUtF,OAAOqG,OAAO,MAC7BtK,KAAKwJ,aAAe,QACM3I,IAAjBsJ,EAAOH,KACY,KAAtBhK,KAAKwJ,aACTxJ,KAAKuJ,QAAUtF,OAAOqG,OAAO,aAEtBH,EAAOH,IAEXhK,KAIT,GAAyB,IAArBP,UAAUU,OAAc,CAC1B,IACIqF,EADAsI,EAAO7J,OAAO6J,KAAK3D,GAEvB,IAAKjK,EAAI,EAAGA,EAAI4N,EAAK3N,SAAUD,EAEjB,oBADZsF,EAAMsI,EAAK5N,KAEXF,KAAK6N,mBAAmBrI,GAK1B,OAHAxF,KAAK6N,mBAAmB,kBACxB7N,KAAKuJ,QAAUtF,OAAOqG,OAAO,MAC7BtK,KAAKwJ,aAAe,EACbxJ,IACf,CAIM,GAAyB,mBAFzBiN,EAAY9C,EAAOH,IAGjBhK,KAAKgJ,eAAegB,EAAMiD,QACrB,QAAkBpM,IAAdoM,EAET,IAAK/M,EAAI+M,EAAU9M,OAAS,EAAGD,GAAK,EAAGA,IACrCF,KAAKgJ,eAAegB,EAAMiD,EAAU/M,IAIxC,OAAOF,IACb,EAmBAwI,EAAanJ,UAAU4N,UAAY,SAAmBjD,GACpD,OAAOsB,EAAWtL,KAAMgK,GAAM,EAChC,EAEAxB,EAAanJ,UAAU0O,aAAe,SAAsB/D,GAC1D,OAAOsB,EAAWtL,KAAMgK,GAAM,EAChC,EAEAxB,EAAasD,cAAgB,SAASlD,EAASoB,GAC7C,MAAqC,mBAA1BpB,EAAQkD,cACVlD,EAAQkD,cAAc9B,GAEtB8B,EAAcxM,KAAKsJ,EAASoB,EAEvC,EAEAxB,EAAanJ,UAAUyM,cAAgBA,EAiBvCtD,EAAanJ,UAAU2O,WAAa,WAClC,OAAOhO,KAAKwJ,aAAe,EAAI/B,EAAezH,KAAKuJ,SAAW,EAChE,eCxWA,SAAS0E,EAAeC,EAAuBC,GAC7C,MACMC,GADc,IAAIC,aACQC,OAAOH,GACvC,OAAQD,GACN,IAAK,OACH,MAAO,CACL7N,KAAM,OACN8N,KAAMC,EACNG,KAAM,UACNnK,KAAM,IAAIoK,YAAY,MAE1B,IAAK,SACH,MAAO,CACLnO,KAAM,SACN8N,KAAMC,EACNG,KAAM,UACNE,WAAY,KAGhB,QACE,MAAM,IAAItI,MAAK,aAAAgC,OAAc+F,gCAEnC,CAMM,SAAgBQ,EAAWC,EAAqBR,4CACpD,MAAMS,EAAmBX,EAAeU,EAASE,UAAUxO,KAAM8N,GAI3DW,QAAsBC,OAAOC,OAAOC,UACxCL,EACAD,EACA,CACEtO,KAAMiF,EACNnF,OAAQ,MAEV,EACA,CAAC,UAAW,YAGd,MAAO,CAAEwO,WAAUG,gBACrB,GAAC,CCtGD,MAAMI,EAAoB,GAK1B,IAAKC,EAiDAC,EAmEL,SAASC,EAAkBC,GACzB,OAAOA,EAAYJ,CACrB,CAOA,SAASK,EAAkBC,GAEzB,OAAQA,GAAa,EAAK,EAC5B,CAOA,SAASC,EAAgBC,GACvB,OAAOA,IAAaP,EAAaQ,WAAaD,IAAaP,EAAaS,aAC1E,CAOA,SAASC,EAAgBH,GACvB,OAEEA,IAAaN,EAAaU,SAC1BJ,IAAaN,EAAaW,SAC1BL,IAAaN,EAAaY,OAC1BN,IAAaN,EAAaa,OAC1BP,IAAaN,EAAac,QAC1BR,IAAaN,EAAae,QAC1BT,IAAaN,EAAagB,QAC1BV,IAAaN,EAAaiB,QAC1BX,IAAaN,EAAakB,QAC1BZ,IAAaN,EAAamB,QAC1Bb,IAAaN,EAAaoB,UAC1Bd,IAAaN,EAAaqB,YAC1Bf,IAAaN,EAAasB,UAC1BhB,IAAaN,EAAauB,YAC1BjB,IAAaN,EAAawB,UAC1BlB,IAAaN,EAAayB,OAE9B,CAqIM,SAAUC,EACdC,EACAC,GAEA,MAAMC,EAlER,SAAyBC,GACvB,MAAMC,EAAmB,GACzB,IAAIC,EAAQ,EACVC,EAAM,EACNC,EAAeJ,EAAO/Q,OAAS,EAEjC,KAAOkR,EAAMC,GAAc,CAEzB,KAAOD,EAAMC,KAGTD,EAAMC,EAAe,GACL,IAAhBJ,EAAOG,IACa,IAApBH,EAAOG,EAAM,IACO,IAApBH,EAAOG,EAAM,IACO,IAApBH,EAAOG,EAAM,MAKK,IAAhBH,EAAOG,IAAkC,IAApBH,EAAOG,EAAM,IAAgC,IAApBH,EAAOG,EAAM,KAG/DA,IAGEA,GAAOC,IAAcD,EAAMH,EAAO/Q,QAGtC,IAAIoR,EAAMF,EACV,KAAOE,EAAMH,GAA6B,IAApBF,EAAOK,EAAM,IAAUA,IAG7C,GAAc,IAAVH,GACF,GAAIG,IAAQH,EAAO,MAAM/O,UAAU,0CAEnC8O,EAAOzG,KAAK0G,GAId,IAAII,EAAkB,EAEpBH,EAAMH,EAAO/Q,OAAS,GACN,IAAhB+Q,EAAOG,IACa,IAApBH,EAAOG,EAAM,IACO,IAApBH,EAAOG,EAAM,IACO,IAApBH,EAAOG,EAAM,KAEbG,EAAkB,GAGpBJ,EAAQC,GAAYG,CACtB,CACA,OAAOL,CACT,CAYsBM,CAAgBV,GAC9BW,EAAgBV,QAAAA,EAjHxB,SAA8BD,EAAkBE,GAC9C,IAAK,MAAMU,KAAaV,EAAa,CACnC,GAAIxB,EAAgBJ,EAAkB0B,EAAKY,KAAc,MAAO,OAChE,GAAI9B,EAAgBN,EAAkBwB,EAAKY,KAAc,MAAO,MAClE,CACA,MAAO,SACT,CA2GsCC,CAAqBb,EAAME,GAE/D,GAAsB,YAAlBS,EACF,MAAO,CAAEG,iBAAkB,EAAGH,gBAAeI,wBAAwB,GAGvE,MAAMD,EAxGR,SACEd,EACAE,EACAc,GAEA,IAAK,MAAMtE,KAASwD,EAClB,GAAc,SAAVc,GAEF,GAAIlC,EADSN,EAAkBwB,EAAKtD,KAElC,OAAOA,EAAQ,OAIjB,GAAIgC,EADSJ,EAAkB0B,EAAKtD,KAElC,OAAOA,EAAQ,EAIrB,OAAO,IACT,CAqF2BuE,CAA8BjB,EAAME,EAAaS,GAC1E,GAAyB,OAArBG,EACF,MAAM,IAAIxP,UAAU,uBAGtB,MAAO,CAAEwP,mBAAkBH,gBAAeI,wBAAwB,EACpE,EA1TA,SAAK3C,GAEHA,EAAAA,EAAA,cAAA,GAAA,gBAEAA,EAAAA,EAAA,kBAAA,GAAA,oBAEAA,EAAAA,EAAA,kBAAA,GAAA,oBAEAA,EAAAA,EAAA,kBAAA,GAAA,oBAEAA,EAAAA,EAAA,UAAA,GAAA,YAEAA,EAAAA,EAAA,IAAA,GAAA,MAEAA,EAAAA,EAAA,IAAA,GAAA,MAEAA,EAAAA,EAAA,IAAA,GAAA,MAEAA,EAAAA,EAAA,IAAA,GAAA,MAEAA,EAAAA,EAAA,QAAA,IAAA,UAEAA,EAAAA,EAAA,WAAA,IAAA,aAEAA,EAAAA,EAAA,YAAA,IAAA,cAEAA,EAAAA,EAAA,QAAA,IAAA,UAEAA,EAAAA,EAAA,YAAA,IAAA,cAEAA,EAAAA,EAAA,WAAA,IAAA,aAEAA,EAAAA,EAAA,IAAA,IAAA,MAKAA,EAAAA,EAAA,UAAA,IAAA,YAEAA,EAAAA,EAAA,UAAA,IAAA,YAEAA,EAAAA,EAAA,gBAAA,IAAA,iBAGD,CA5CD,CAAKA,IAAAA,EAAY,CAAA,IAiDjB,SAAKC,GAEHA,EAAAA,EAAA,QAAA,GAAA,UAEAA,EAAAA,EAAA,QAAA,GAAA,UAEAA,EAAAA,EAAA,MAAA,GAAA,QAEAA,EAAAA,EAAA,MAAA,GAAA,QAEAA,EAAAA,EAAA,OAAA,GAAA,SAEAA,EAAAA,EAAA,OAAA,GAAA,SAEAA,EAAAA,EAAA,OAAA,GAAA,SAEAA,EAAAA,EAAA,OAAA,GAAA,SAEAA,EAAAA,EAAA,OAAA,GAAA,SAEAA,EAAAA,EAAA,OAAA,GAAA,SAKAA,EAAAA,EAAA,SAAA,IAAA,WAEAA,EAAAA,EAAA,WAAA,IAAA,aAEAA,EAAAA,EAAA,SAAA,IAAA,WAEAA,EAAAA,EAAA,WAAA,IAAA,aAEAA,EAAAA,EAAA,SAAA,IAAA,WAEAA,EAAAA,EAAA,QAAA,IAAA,UAKAA,EAAAA,EAAA,QAAA,IAAA,UAEAA,EAAAA,EAAA,QAAA,IAAA,UAEAA,EAAAA,EAAA,QAAA,IAAA,UAEAA,EAAAA,EAAA,QAAA,IAAA,UAEAA,EAAAA,EAAA,QAAA,IAAA,UAEAA,EAAAA,EAAA,QAAA,IAAA,UAEAA,EAAAA,EAAA,OAAA,IAAA,SAEAA,EAAAA,EAAA,eAAA,IAAA,iBAEAA,EAAAA,EAAA,eAAA,IAAA,gBAID,CA5DD,CAAKA,IAAAA,EAAY,CAAA,IClBV,MAAM6C,EACK,mEADLA,EAES,mEAFTA,EAGS,mEAHTA,EAIS,mEAJTA,EAKO,mEAMd,SAAgBC,EACpBnB,4CAEA,MAAMxC,QAzBR,SAA0BwC,4CACxB,MAAMoB,QAAmBpD,OAAOC,OAAOoD,OAAO,UAAWrB,GACnDsB,EAAY,IAAIC,WAAWH,GACjC,OAAOxG,MAAM4G,KAAKF,GACflO,KAAKqO,GAAMA,EAAEC,SAAS,IAAIC,SAAS,EAAG,OACtCC,KAAK,GACV,GAAC,CAmBoBC,CAAW7B,GAE9B,OAAQxC,GACN,KAAK0D,EACH,MAAO,MACT,KAAKA,EAEL,KAAKA,EAEL,KAAKA,EACH,MAAO,OACT,KAAKA,EACH,MAAO,OACT,QACE,OAAO,KAEb,GAAC,CC3DM,MAAMY,EAA6C,IAAIC,IAaxD,MAAOC,UAA0BvK,EAAAA,aAC3BwK,cAAAA,CACRC,EACAC,GAEA,MAAM/M,MAAM,+BACd,CAEUgN,cAAAA,CACRF,EACAC,GAEA,MAAM/M,MAAM,+BACd,EAOI,MAAOiN,UAAqBL,EAwBhCvO,WAAAA,CAAY6O,SAMV/M,QARMtG,KAAAsT,mBAA6B,EASnCtT,KAAKuT,WAAa,IAAIT,IACtB9S,KAAK8N,KAAOuF,EAAKvF,KACjB9N,KAAKwH,oBAAsB6L,EAAK7L,oBAChCxH,KAAKwT,OAAS,IAAIV,IAClB9S,KAAKyT,mBAAqBJ,EAAKI,mBAC/BzT,KAAK0T,WAA4B,QAAfC,EAAAN,EAAKK,kBAAU,IAAAC,EAAAA,EAAIrB,WAAWC,KAAK,GACvD,CAEA,cAAYqB,GACV,MAAO,CACLC,YAAa7T,KAAKwH,oBAClBsM,aAAc9T,KAAK+T,QACnBC,cAAehU,KAAKiU,WAExB,CAQAC,cAAAA,CAAeC,EAAYrG,GACzBzJ,EAAa/D,MAAM,qCAAoC2D,OAAAmQ,OAAAnQ,OAAAmQ,OAAA,CAAA,EAClDpU,KAAK4T,YAAU,CAClBC,YAAaM,KAEXnU,KAAKwH,qBACPnD,EAAa2C,MACX,oGAEKhH,KAAK4T,aAId5T,KAAKwH,oBAAsB2M,EAC3BnU,KAAK8N,KAAOA,CACd,CAEAuG,gBAAAA,GACEhQ,EAAa/D,MAAM,wBAAyBN,KAAK4T,YACjD5T,KAAKwH,yBAAsB3G,CAC7B,CAEAyT,SAAAA,GACE,OAAItU,KAAKwH,oBACAqL,EAAqBtG,IAAIvM,KAAKwH,0BAErC,CAEJ,CAEA+M,sBAAAA,GACE,OAAOvU,KAAKwH,mBACd,CAEAgN,UAAAA,GACE,OAAOxU,KAAK+T,OACd,CAMAU,aAAAA,CAAc1C,GACZ/R,KAAKiU,WAAalC,CACpB,CAMA2C,SAAAA,CAAUvQ,GACRnE,KAAKwT,OAASrP,CAChB,CAEAwQ,cAAAA,CACEC,EACAC,EACAC,EACAf,EACAgB,EACAhD,GAcA,GAZIA,IACF1N,EAAaD,KAAK,8BAA+B,CAAE2N,UACnD/R,KAAKiU,WAAalC,GAGpB1N,EAAa/D,MAAM,qCAAoC2D,OAAAmQ,OAAA,CACrDQ,YACAI,cAAejB,EACfhC,SACG/R,KAAK4T,aAGNmB,GAAW/U,KAAKsT,kBAIlB,YAHAjP,EAAa/D,MAAM,mCACdN,KAAK4T,aAKZ,MAAMqB,EAA4B,WAAdL,EAAyB5U,KAAKgT,eAAiBhT,KAAKmT,eAClE+B,EAAkB,IAAIC,gBAAgB,CAC1CC,UAAWH,EAAY9V,KAAKa,QAG9BA,KAAKsT,mBAAoB,EAEzBuB,EACGQ,YAAYH,GACZI,OAAOR,GACPS,OAAOhW,IACN8E,EAAa0G,KAAKxL,GAClBS,KAAKwK,KACH1D,EAAaX,MACb5G,aAAa8H,EACT9H,EACA,IAAI8H,EAAa9H,EAAE8G,aAASxF,EAAWb,KAAKwH,qBACjD,IAEFgO,SAAQ,KACPxV,KAAKsT,mBAAoB,CAAK,IAElCtT,KAAK+T,QAAUA,CACjB,CAEA0B,aAAAA,CAAcC,GACZrR,EAAa/D,MAAM,sBAAqB2D,OAAAmQ,OAAAnQ,OAAAmQ,OAAA,CAAA,EAAOpU,KAAK4T,YAAU,CAAE8B,aAChE1V,KAAK0T,WAAagC,CACpB,CAwBgB1C,cAAAA,CACdC,EACAC,kDAEA,IACGlT,KAAKsU,aAE2B,IAAjCrB,EAAalC,KAAK4E,WAElB,OAAOzC,EAAW0C,QAAQ3C,GAE5B,MAAM4C,EAAS7V,KAAK8N,KAAKgI,YACzB,IAAKD,EAWH,YAVA7V,KAAKwK,KACH1D,EAAaX,MACb,IAAIkB,2BAAYc,OAEZnI,KAAKwH,oBACP,cAAAW,OAAanI,KAAK8N,KAAKiI,sBACvBrP,EAAmBsP,WACnBhW,KAAKwH,sBAKX,MAAMsH,cAAEA,GAAkB+G,EACpBI,EAAWjW,KAAK8N,KAAKiI,qBAE3B,GAAIjH,EAAe,CACjB,MAAMoH,EAAKlW,KAAKmW,eACdxC,EAAAV,EAAamD,cAAcC,sCAAyB,EACpDpD,EAAaqD,WAEf,IAAIC,EAAYvW,KAAKwW,oBAAoBvD,GAGzC,MAAMwD,EAAc,IAAInE,WAAWW,EAAalC,KAAM,EAAGwF,EAAU1E,kBAG7D6E,EAAe,IAAIpE,WAAW,GAEpCoE,EAAa,GRzPM,GQ0PnBA,EAAa,GAAKT,EASlB,IACE,MAAMU,QAAmB5H,OAAOC,OAAO4H,QACrC,CACEvW,KAAMiF,EACN4Q,KACAW,eAAgB,IAAIvE,WAAWW,EAAalC,KAAM,EAAG0F,EAAYd,aAEnE7G,EACA,IAAIwD,WAAWW,EAAalC,KAAMwF,EAAU1E,mBAG9C,IAAIiF,EAAuB,IAAIxE,WAC7BqE,EAAWhB,WAAaO,EAAGP,WAAae,EAAaf,YAEvDmB,EAAqBtK,IAAI,IAAI8F,WAAWqE,IACxCG,EAAqBtK,IAAI,IAAI8F,WAAW4D,GAAKS,EAAWhB,YACxDmB,EAAqBtK,IAAIkK,EAAcC,EAAWhB,WAAaO,EAAGP,YAE9DY,EAAUzE,yBACZgF,EHjJJ,SAAoBC,GACxB,MAAMC,EAAoB,GAE1B,IADA,IAAIC,EAAsB,EACjB/W,EAAI,EAAGA,EAAI6W,EAAQ5W,SAAUD,EAAG,CACvC,IAAIgX,EAAOH,EAAQ7W,GACfgX,GAPe,GAOWD,GARJ,IAUxBD,EAAQtM,KATS,GAUjBuM,EAAsB,GAExBD,EAAQtM,KAAKwM,GACD,GAARA,IACAD,EAEFA,EAAsB,CAE1B,CACA,OAAO,IAAI3E,WAAW0E,EACxB,CG+HiCG,CAAUL,IAGnC,IAAIM,EAAU,IAAI9E,WAAWmE,EAAYd,WAAamB,EAAqBnB,YAM3E,OALAyB,EAAQ5K,IAAIiK,GACZW,EAAQ5K,IAAIsK,EAAsBL,EAAYd,YAE9C1C,EAAalC,KAAOqG,EAAQC,OAErBnE,EAAW0C,QAAQ3C,EAC5B,CAAE,MAAO1T,GAEP8E,EAAa2C,MAAMzH,EACrB,CACF,MACE8E,EAAa/D,MAAM,oCAAqCN,KAAK4T,YAC7D5T,KAAKwK,KACH1D,EAAaX,MACb,IAAIkB,EAAY,sCAEdX,EAAmBsP,WACnBhW,KAAKwH,qBAIb,GAAC,CAQe2L,cAAAA,CACdF,EACAC,4CAEA,IACGlT,KAAKsU,aAE2B,IAAjCrB,EAAalC,KAAK4E,WAElB,OAAOzC,EAAW0C,QAAQ3C,GAG5B,GA4SE,SAAgCqE,EAAwBC,GAC5D,GAAgC,IAA5BA,EAAa5B,WACf,OAAO,EAET,MAAMe,EAAe,IAAIpE,WACvBgF,EAAUrV,MAAMqV,EAAU3B,WAAa4B,EAAa5B,aAEtD,OAAO4B,EAAaC,OAAM,CAACjP,EAAOkF,IAAUlF,IAAUmO,EAAajJ,IACrE,CApTQgK,CAAsBxE,EAAalC,KAAM/Q,KAAK0T,YAKhD,OAJAT,EAAalC,KAAOkC,EAAalC,KAAK9O,MACpC,EACAgR,EAAalC,KAAK4E,WAAa3V,KAAK0T,WAAWiC,mBAEvCzD,EAAmBe,EAAalC,QACxC1M,EAAa/D,MAAM,cAAeN,KAAK4T,YAChCV,EAAW0C,QAAQ3C,SAE1B5O,EAAa0G,KAAK,+CAAgD/K,KAAK4T,YAI3E,MACMqC,EADO,IAAI3D,WAAWW,EAAalC,MACnBkC,EAAalC,KAAK4E,WAAa,GAErD,IAAI3V,KAAK8N,KAAK4J,qBAAqBzB,GAKnC,GAAIjW,KAAK8N,KAAKgI,UAAUG,GACtB,IACE,MAAM0B,QAAqB3X,KAAK4X,aAAa3E,EAAcgD,GAE3D,GADAjW,KAAK8N,KAAK+J,kBAAkB5B,GACxB0B,EACF,OAAOzE,EAAW0C,QAAQ+B,EAE9B,CAAE,MAAO3Q,GACHA,aAAiBK,GAAgBL,EAAMM,SAAWZ,EAAmBoR,WAEnE9X,KAAK8N,KAAKiK,cACZ/X,KAAKwK,KAAK1D,EAAaX,MAAOa,GAC9BhH,KAAK8N,KAAKkK,kBAAkB/B,IAG9B5R,EAAa0G,KAAK,wBAAyB,CAAE/D,SAEjD,MAGA3C,EAAa0G,KAAI,mDAAA5C,OAAoD8N,IACrEjW,KAAKwK,KACH1D,EAAaX,MACb,IAAIkB,EAAY,wBAAAc,OACU8N,EAAQ,qBAAA9N,OAAoBnI,KAAKwH,qBACzDd,EAAmBsP,WACnBhW,KAAKwH,sBAGTxH,KAAK8N,KAAKkK,kBAAkB/B,EAEhC,GAAC,CAMa2B,YAAAA,CAAYK,EAAAC,4CACxBjF,EACAgD,GAAgB,IAAAkC,EAAAnY,KAAA,IAChBoY,EAAA3Y,UAAAU,OAAA,QAAAU,IAAApB,UAAA,GAAAA,UAAA,QAAsCoB,EACtCwX,EAAA5Y,UAAAU,OAAA,QAAAU,IAAApB,UAAA,GAAAA,UAAA,GAAoC,CAAE6Y,aAAc,GAAG,OAAA,kBAEvD,MAAMzC,EAASsC,EAAKrK,KAAKgI,UAAUG,GACnC,IAAKoC,EAAYvJ,gBAAkB+G,EACjC,MAAM,IAAIxT,UAAS,6CAAA8F,OAA8CgQ,EAAK3Q,sBAExE,IAAI+O,EAAY4B,EAAK3B,oBAAoBvD,GAUzC,IACE,MAAMwD,EAAc,IAAInE,WAAWW,EAAalC,KAAM,EAAGwF,EAAU1E,kBACnE,IAAI0G,EAAgB,IAAIjG,WACtBW,EAAalC,KACb0F,EAAYtW,OACZ8S,EAAalC,KAAK4E,WAAac,EAAYtW,QAE7C,GAAIoW,EAAUzE,wBHnTd,SAA8BwF,GAClC,IAAK,IAAIpX,EAAI,EAAGA,EAAIoX,EAAUnX,OAAS,EAAGD,IACxC,GAAoB,GAAhBoX,EAAUpX,IAA+B,GAApBoX,EAAUpX,EAAI,IAA+B,GAApBoX,EAAUpX,EAAI,GAAS,OAAO,EAElF,OAAO,CACT,CG8S8CsY,CAAoBD,GAAgB,CAC1EA,EH7SF,SAAoBrH,GACxB,MAAM8F,EAAoB,GAE1B,IADA,IAAI7W,EAAS+Q,EAAO/Q,OACXD,EAAI,EAAGA,EAAIgR,EAAO/Q,QAKrBA,EAASD,GAAK,IAAMgR,EAAOhR,KAAOgR,EAAOhR,EAAI,IAAuB,GAAjBgR,EAAOhR,EAAI,IAEhE8W,EAAQtM,KAAKwG,EAAOhR,MACpB8W,EAAQtM,KAAKwG,EAAOhR,MAEpBA,KAGA8W,EAAQtM,KAAKwG,EAAOhR,MAGxB,OAAO,IAAIoS,WAAW0E,EACxB,CGyRwByB,CAAUF,GAC1B,MAAMG,EAAW,IAAIpG,WAAWmE,EAAYd,WAAa4C,EAAc5C,YACvE+C,EAASlM,IAAIiK,GACbiC,EAASlM,IAAI+L,EAAe9B,EAAYd,YACxC1C,EAAalC,KAAO2H,EAASrB,MAC/B,CAEA,MAAMX,EAAe,IAAIpE,WAAWW,EAAalC,KAAMkC,EAAalC,KAAK4E,WAAa,EAAG,GAEnFgD,EAAWjC,EAAa,GACxBR,EAAK,IAAI5D,WACbW,EAAalC,KACbkC,EAAalC,KAAK4E,WAAagD,EAAWjC,EAAaf,WACvDgD,GAGIC,EAAkBnC,EAAYd,WAC9BkD,EACJ5F,EAAalC,KAAK4E,YACjBc,EAAYd,WAAagD,EAAWjC,EAAaf,YAE9CmD,QAAkB/J,OAAOC,OAAO+J,QACpC,CACE1Y,KAAMiF,EACN4Q,KACAW,eAAgB,IAAIvE,WAAWW,EAAalC,KAAM,EAAG0F,EAAYd,qBAEnEhC,EAAA0E,EAAYvJ,6BAAiB+G,EAAQ/G,cACrC,IAAIwD,WAAWW,EAAalC,KAAM6H,EAAiBC,IAG/CzB,EAAU,IAAI5I,YAAYiI,EAAYd,WAAamD,EAAUnD,YAC7D+C,EAAW,IAAIpG,WAAW8E,GAOhC,OALAsB,EAASlM,IAAI,IAAI8F,WAAWW,EAAalC,KAAM,EAAG0F,EAAYd,aAC9D+C,EAASlM,IAAI,IAAI8F,WAAWwG,GAAYrC,EAAYd,YAEpD1C,EAAalC,KAAOqG,EAEbnE,CACT,CAAE,MAAOjM,GACP,GAAImR,EAAK1E,mBAAmB1N,kBAAoB,EAAG,CACjD,GAAIsS,EAAYC,aAAeH,EAAK1E,mBAAmB1N,kBAAmB,CAOxE,IAAIiT,EACAC,EAPJ5U,EAAa/D,MAAK,0BAAA6H,OACUkQ,EAAYC,aAAY,QAAAnQ,OAChDgQ,EAAK1E,mBAAmB1N,kBAC1B,eAAAoC,OAAc8K,aAAwBiG,qBAAuB,QAAU,WAKpEd,QAAAA,EAAmBvC,KAAYsC,EAAKrK,KAAKgI,UAAUG,KAGtDgD,QAAsBd,EAAKrK,KAAKqL,WAAWlD,GAAU,GAErD+C,QAAwBtK,EACtBuK,EAAcG,UACdjB,EAAK1E,mBAAmB3N,cAI5B,MAAMuT,QAAclB,EAAKP,aAAa3E,EAAcgD,EAAUmC,GAAmBvC,EAAQ,CACvFyC,aAAcD,EAAYC,aAAe,EACzCxJ,cAAekK,aAAe,EAAfA,EAAiBlK,gBAWlC,OATIuK,GAASL,IAGNZ,QAAAA,EAAmBvC,KAAYsC,EAAKrK,KAAKgI,UAAUG,KACtDkC,EAAKrK,KAAKwL,UAAUN,EAAiB/C,EAAUgD,GAE/Cd,EAAKrK,KAAKyL,mBAAmBtD,IAG1BoD,CACT,CAQE,MADAhV,EAAa0G,KAAK,qCACZ,IAAI1D,EAAY,qCAAAc,OACiBgQ,EAAK3Q,qBAC1Cd,EAAmBoR,WACnBK,EAAK3Q,oBAGX,CACE,MAAM,IAAIH,EAAY,sBAAAc,OACEnB,EAAMX,SAC5BK,EAAmBoR,WACnBK,EAAK3Q,oBAGX,CACF,CA1HyD,EA0HxD,GAAA,CAqBO2O,MAAAA,CAAOE,EAA+BC,SAC5C,MAAMJ,EAAK,IAAI1H,YRjhBM,IQkhBfgL,EAAS,IAAIC,SAASvD,GAGvBlW,KAAKuT,WAAWmG,IAAIrD,IAEvBrW,KAAKuT,WAAW/G,IAAI6J,EAAuBsD,KAAKC,MAAsB,MAAhBD,KAAKE,WAG7D,MAAMC,EAAsD,QAA1CnG,EAAA3T,KAAKuT,WAAWhH,IAAI8J,UAAsB,IAAA1C,EAAAA,EAAI,EAQhE,OANA6F,EAAOO,UAAU,EAAG1D,GACpBmD,EAAOO,UAAU,EAAGzD,GACpBkD,EAAOO,UAAU,EAAGzD,EAAawD,EAAY,OAE7C9Z,KAAKuT,WAAW/G,IAAI6J,EAAuByD,EAAY,GAEhD5D,CACT,CAEQM,mBAAAA,CAAoB6C,SAK1B,IHjjBE,SACJA,GAEA,MAAO,SAAUA,CACnB,CG6iBSW,CAAaX,GAChB,MAAO,CAAExH,iBAAkBtM,EAAkBG,MAAOoM,wBAAwB,GAI9E,MAAMJ,EAAyC,QAAzBiC,EAAA3T,KAAKia,cAAcZ,UAAM,IAAA1F,EAAAA,EAAI3T,KAAKiU,WAWxD,GAVIvC,IAAkB1R,KAAK0R,gBACzBrN,EAAa/D,MAAM,2BAA0B2D,OAAAmQ,OAAA,CAC3C1C,gBACAwI,SAAUla,KAAK0R,eACZ1R,KAAK4T,aAEV5T,KAAK0R,cAAgBA,GAID,QAAlBA,EACF,MAAM,IAAIvL,MAAK,GAAAgC,OAAIuJ,sDAIrB,GAAsB,QAAlBA,EACF,MAAO,CAAEG,iBAAkBtM,EAAkB8T,EAAMrP,MAAO8H,wBAAwB,GAEpF,GAAsB,QAAlBJ,EACF,MAAO,CAAEG,iBAAkB,EAAGC,wBAAwB,GAIxD,IACE,MAAMd,EACc,SAAlBU,GAA8C,SAAlBA,EAA2BA,OAAgB7Q,EACnEsZ,EAAarJ,EAA0B,IAAIwB,WAAW+G,EAAMtI,MAAOC,GAEzE,GAAImJ,EAAWrI,uBACb,MAAO,CACLD,iBAAkBsI,EAAWtI,iBAC7BC,wBAAwB,EAG9B,CAAE,MAAOvS,GACP8E,EAAa/D,MAAM,uDAAsD2D,OAAAmQ,OAAA,CACvEpN,MAAOzH,GACJS,KAAK4T,YAEZ,CAGA,MAAO,CAAE/B,iBAAkBtM,EAAkB8T,EAAMrP,MAAO8H,wBAAwB,EACpF,CAKQmI,aAAAA,CAAcZ,GACpB,GAAyB,IAArBrZ,KAAKwT,OAAO4G,KACd,OAEF,MAAMC,EAAchB,EAAMjD,cAAciE,YAExC,OADcA,EAAcra,KAAKwT,OAAOjH,IAAI8N,QAAexZ,CAE7D,EC/mBI,MAAOyZ,UAA+B9R,EAAAA,aAgB1C,eAAIuP,GACF,OAAQ/X,KAAK0X,qBAAqB1X,KAAKua,gBACzC,CAEA/V,WAAAA,CAAYgD,EAA6BiM,GAGvC,GAFAnN,QACAtG,KAAKua,gBAAkB,EACnB9G,EAAmBxN,YAAc,GAAKwN,EAAmBxN,YAAc,IACzE,MAAM,IAAI5D,UAAU,8CAEtBrC,KAAKwa,cAAgB,IAAI7O,MAAM8H,EAAmBxN,aAAawU,UAAK5Z,GACpEb,KAAK0a,wBAA0B,IAAI/O,MAAM8H,EAAmBxN,aAAawU,KAAK,GAC9Eza,KAAKyT,mBAAqBA,EAC1BzT,KAAK2a,kBAAoB,IAAI7H,IAC7B9S,KAAKwH,oBAAsBA,CAC7B,CAOAkQ,oBAAAA,CAAqBzB,GACnB,OACEjW,KAAKyT,mBAAmBzN,kBAAoB,GAC5ChG,KAAK0a,wBAAwBzE,GAAYjW,KAAKyT,mBAAmBzN,gBAErE,CAOAgS,iBAAAA,GAAyD,IAAvC/B,EAAAxW,UAAAU,OAAA,QAAAU,IAAApB,UAAA,GAAAA,UAAA,GAAmBO,KAAKua,gBACpCva,KAAKyT,mBAAmBzN,iBAAmB,IAI/ChG,KAAK0a,wBAAwBzE,IAAa,EAEtCjW,KAAK0a,wBAAwBzE,GAAYjW,KAAKyT,mBAAmBzN,kBACnE3B,EAAa0G,KAAI,WAAA5C,OACJnI,KAAKwH,oBAAmB,cAAAW,OAAa8N,kCAGtD,CAOA4B,iBAAAA,GAAyD,IAAvC5B,EAAAxW,UAAAU,OAAA,QAAAU,IAAApB,UAAA,GAAAA,UAAA,GAAmBO,KAAKua,gBACxCva,KAAK4a,eAAe3E,EACtB,CAQA2E,cAAAA,CAAe3E,QACIpV,IAAboV,EACFjW,KAAK0a,wBAAwBD,KAAK,GAElCza,KAAK0a,wBAAwBzE,GAAY,CAE7C,CASAkD,UAAAA,CAAWlD,GAAgC,IAAb4E,IAAMpb,UAAAU,OAAA,QAAAU,IAAApB,UAAA,KAAAA,UAAA,GAClC,MAAM8a,EAAkBtE,QAAAA,EAAYjW,KAAK+V,qBAEnC+E,EAAkB9a,KAAK2a,kBAAkBpO,IAAIgO,GACnD,QAA+B,IAApBO,EACT,OAAOA,EAET,MAAMC,EAAiB,IAAIpW,SAAuB,CAAOC,EAASiE,IAAUmS,EAAAhb,UAAA,OAAA,GAAA,YAC1E,IACE,MAAM6V,EAAS7V,KAAK8V,UAAUyE,GAC9B,IAAK1E,EACH,MAAM,IAAIxT,UAAS,4DAAA8F,OAC2CnI,KAAKwH,sBAGrE,MAAMyT,EAAkBpF,EAAOlH,SACzBuM,QJRR,SAAwBvM,EAAqBR,4CACjD,MAAMS,EAAmBX,EAAeU,EAASE,UAAUxO,KAAM8N,GAGjE,OAAOY,OAAOC,OAAOmM,WAAWvM,EAAkBD,EAAU,IAC9D,GAAC,CIG8ByM,CAAQH,EAAiBjb,KAAKyT,mBAAmB3N,aAClEuV,iBJxGiBC,4CAC7BC,GAAkC,IAClC1M,yDAAuC,CAAExO,KAAMiF,GAC/CkW,EAAA/b,UAAAU,OAAA,QAAAU,IAAApB,UAAA,GAAAA,UAAA,GAA8B,UAAS,OAAA,YAGvC,OAAOsP,OAAOC,OAAOyM,UACnB,MACAF,EACA1M,GACA,EACU,WAAV2M,EAAqB,CAAC,aAAc,aAAe,CAAC,UAAW,WAEnE,CAVyC,EAUxC,GAAA,CI2FiCC,CAAUP,EAAUD,EAAgBpM,UAAUxO,KAAM,UACxE4Y,EAA+B,CACnCiC,WACA9B,UAAWiC,GAETR,UAEI7a,KAAK0b,mBAAmBL,EAAad,EAAiBtB,IAE9DrU,EAAQqU,EACV,CAAE,MAAO1Z,GACPsJ,EAAOtJ,EACT,CAAC,QACCS,KAAK2a,kBAAkBgB,OAAOpB,EAChC,CACF,MAEA,OADAva,KAAK2a,kBAAkBnO,IAAI+N,EAAiBQ,GACrCA,CACT,CAQMF,MAAAA,CAAMe,4CAACjN,GAAmB,IAAAwJ,EAAAnY,KAAA,IAAEiW,EAAQxW,UAAAU,OAAA,QAAAU,IAAApB,UAAA,GAAAA,UAAA,GAAG,EAAC,OAAA,kBACtC0Y,EAAKuD,mBAAmB/M,EAAUsH,GACxCkC,EAAKyC,eAAe3E,EACtB,CAH8C,EAG7C,GAAA,CAQKyF,kBAAAA,CAAkBE,EAAA1D,GACtB,OAAA8C,EAAAhb,KAAAP,eAAA,GAAA,SAAAkP,EACAsH,GAAgB,IAAA4F,EAAA7b,KAAA,IAChB8b,EAAArc,UAAAU,OAAA,QAAAU,IAAApB,UAAA,GAAAA,UAAA,GAAwC,KAAI,OAAA,YAE5C,MAAMoW,QAAenH,EAAWC,EAAUkN,EAAKpI,mBAAmB3N,aAC5DiW,EAAW9F,GAAY,EAAIA,EAAW4F,EAAKrB,cAAcra,OAAS0b,EAAKtB,gBAC7ElW,EAAa/D,MAAK,8BAAA6H,OAA+B8N,GAAY,CAC3DuF,MAAO7M,EAASqN,OAChBnN,UAAWF,EAASE,UACpB/I,YAAa+V,EAAKpI,mBAAmB3N,cAEvC+V,EAAKvC,UAAUzD,EAAQkG,EAAUD,GAC7BC,GAAY,IAAGF,EAAKtB,gBAAkBwB,EAC5C,CAX8C,EAW7C,GAAA,CAEDzC,SAAAA,CAAUzD,EAAgBI,GAA8D,IAA5C6F,yDAAwC,KAClF9b,KAAKwa,cAAcvE,EAAWjW,KAAKwa,cAAcra,QAAU0V,EAEvDiG,GACF9b,KAAKwK,KAAK5D,EAAgBqV,aAAcH,EAAiB9b,KAAKwH,oBAAqByO,EAEvF,CAEMsD,kBAAAA,CAAmB9L,4CACvBzN,KAAKua,gBAAkB9M,EAAQzN,KAAKwa,cAAcra,OAClDH,KAAK4a,eAAenN,EACtB,GAAC,CAEDsI,kBAAAA,GACE,OAAO/V,KAAKua,eACd,CAOAzE,SAAAA,CAAUG,GACR,OAAOjW,KAAKwa,cAAcvE,QAAAA,EAAYjW,KAAKua,gBAC7C,EC3LF,MAAM2B,EAAsC,GACtCC,EAAsD,IAAIrJ,IAChE,IAAIsJ,EAOA1I,GANA2I,GAAe,UZEjB7X,WAAAA,GACExE,KAAKsc,aAAe,IAAIxJ,IACxB9S,KAAKuc,UAAY,IAAIC,EACrBxc,KAAKyc,cAAgB,CACvB,CAEMC,GAAAA,CAAOC,4CACX,MAAMC,EAA0B,CAC9BzI,GAAInU,KAAKyc,gBACTI,WAAYC,KAAKC,MACjBC,OAAQ1Y,EAAgB2Y,SAE1Bjd,KAAKsc,aAAa9P,IAAIoQ,EAASzI,GAAIyI,GACnC,MAAMM,QAAeld,KAAKuc,UAAUxX,OACpC,IAGE,OAFA6X,EAASO,WAAaL,KAAKC,MAC3BH,EAASI,OAAS1Y,EAAgB8Y,cACrBT,GACf,CAAC,QACCC,EAASI,OAAS1Y,EAAgB+Y,UAClCrd,KAAKsc,aAAaX,OAAOiB,EAASzI,IAClC+I,GACF,CACF,GAAC,CAEKI,KAAAA,4CACJ,OAAOtd,KAAK0c,KAAI,IAAW1B,EAAAhb,UAAA,OAAA,GAAA,YAAE,KAC/B,GAAC,CAEDud,QAAAA,GACE,OAAO5R,MAAM4G,KAAKvS,KAAKsc,aAAapY,SACtC,GY7BEsZ,IAAwB,EAIxB/J,GAAyC7N,EAEzC4N,GAAkC,IAAIV,IA+G1C,SAAS2K,GAAgBjW,EAA6BuM,GACpD,IAAI2J,EAAWxB,EAAoByB,QAAQvY,GAAMA,EAAEoP,eAAiBT,IACpE,GAAI2J,EAASvd,OAAS,EAAG,CACvB,MAAMyd,EAAYF,EACfvZ,KAAKiB,IACG,CAAEyO,YAAazO,EAAEmP,6BAEzB5B,KAAK,KACRtO,EAAa2C,MAAK,gDAAAmB,OACgC4L,EAAO,0BAAA5L,OAAyBX,EAAmB,KACnG,CAAEqW,aAAcD,GAEpB,CACA,IAAIE,EAAUJ,EAAS,GACvB,GAAKI,EAcMtW,IAAwBsW,EAAQvJ,0BAEzCuJ,EAAQ5J,eAAe1M,EAAqBuW,GAAyBvW,QAhBzD,CAEZ,GADAnD,EAAaD,KAAK,2BAA4B,CAAEoD,sBAAqBuM,aAChEN,GACH,MAAMtN,MAAM,+BAEd2X,EAAU,IAAI1K,EAAa,CACzB5L,sBACAsG,KAAMiQ,GAAyBvW,GAC/BiM,sBACAC,gBAEFoK,EAAQpJ,UAAUlB,IA8DtB,SAAiCsK,GAC/BA,EAAQzU,GAAGvC,EAAaX,OAAQa,IAC9B,MAAMgX,EAAoB,CACxBC,KAAM,QACNlN,KAAM,CAAE/J,MAAO,IAAIb,SAAKgC,OAAIzB,EAAmBM,EAAMM,QAAO,MAAAa,OAAKnB,EAAMX,YAEzE6X,YAAYF,EAAI,GAEpB,CArEIG,CAAwBL,GACxB5B,EAAoBxR,KAAKoT,EAC3B,CAKA,OAAOA,CACT,CAEA,SAASC,GAAyBvW,GAChC,GAAIgW,GACF,OAAOY,KAET,IAAItQ,EAAOqO,EAAgB5P,IAAI/E,GAM/B,OALKsG,IACHA,EAAO,IAAIwM,EAAsB9S,EAAqBiM,IACtD3F,EAAKzE,GAAGzC,EAAgBqV,aAAcoC,IACtClC,EAAgB3P,IAAIhF,EAAqBsG,IAEpCA,CACT,CAEA,SAASsQ,KAKP,OAJKhC,IACH/X,EAAa/D,MAAM,mCACnB8b,EAAmB,IAAI9B,EAAsB,aAAc7G,KAEtD2I,CACT,CA0CA,SAASiC,GACPpF,EACAzR,EACAyO,GAUAiI,YAR4B,CAC1BD,KAAI,aACJlN,KAAM,CACJvJ,sBACAyO,WACAgD,kBAIN,CA5NA5U,EAAarB,gBAAgB,QAE7Bsb,UAAaC,IACXlC,GAAaK,KAAI,IAAW1B,OAAA,OAAA,OAAA,GAAA,YAC1B,MAAMiD,KAAEA,EAAIlN,KAAEA,GAA4BwN,EAAGxN,KAE7C,OAAQkN,GACN,IAAK,OACH5Z,EAAa1B,SAASoO,EAAKyN,UAC3Bna,EAAaD,KAAK,sBAClBqP,GAAqB1C,EAAK0C,mBAC1B+J,KAAiBzM,EAAK0C,mBAAmB5N,UAMzCqY,YAJwB,CACtBD,KAAM,UACNlN,KAAM,CAAE0N,QAzBiB,SA4B3B,MACF,IAAK,SAqKmBC,EApKD3N,EAAK0N,QAoKajX,EApKJuJ,EAAKvJ,oBAqK9CnD,EAAa/D,MAAK,gDAAA6H,OAAiDX,GAAuB,CACxFkX,WAEF7L,EAAqBrG,IAAIhF,EAAqBkX,GAvKxCra,EAAaD,KAAI,mCAAA+D,OACoB4I,EAAKvJ,oBAAmB,QAAAW,OAAO4I,EAAK0N,UAGzEP,YAAYK,EAAGxN,MACf,MACF,IAAK,SACW0M,GAAgB1M,EAAKvJ,oBAAqBuJ,EAAKgD,SACrDY,eACNsJ,EACAlN,EAAK4N,eACL5N,EAAK6N,eACL7N,EAAKgD,QACLhD,EAAKgE,QACLhE,EAAKgB,OAEP,MACF,IAAK,SACc0L,GAAgB1M,EAAKvJ,oBAAqBuJ,EAAKgD,SACrDY,eACTsJ,EACAlN,EAAK4N,eACL5N,EAAK6N,eACL7N,EAAKgD,QACLhD,EAAKgE,QACLhE,EAAKgB,OAEP,MACF,IAAK,SACCyL,SA6IZ,SAA4BhY,EAAgBiI,4CAC1CpJ,EAAaD,KAAK,iBAAkB,CAAEqJ,gBAChC2Q,KAAsBvD,OAAOrV,EAAKiI,EAC1C,GAAC,CA/IeoR,CAAa9N,EAAKvL,IAAKuL,EAAKkF,UACzBlF,EAAKvJ,qBACdnD,EAAaD,KAAI,8BAAA+D,OACe4I,EAAKvJ,oBAAmB,WAAAW,OAAU4I,EAAKkF,iBAEjE8H,GAAyBhN,EAAKvJ,qBAAqBqT,OAAO9J,EAAKvL,IAAKuL,EAAKkF,WAE/E5R,EAAa2C,MAAM,mEAErB,MACF,IAAK,mBAyGX,SAAiC+M,EAAiBvM,GAChD,MAAMkW,EAAWxB,EAAoByB,QAClCvY,GAAMA,EAAEmP,2BAA6B/M,GAAuBpC,EAAEoP,eAAiBT,IAE9E2J,EAASvd,OAAS,GACpBkE,EAAa2C,MAAM,2EAA4E,CAC7F+M,UACAvM,wBAGJ,MAAMsW,EAAUJ,EAAS,GACpBI,EAGHA,EAAQzJ,mBAFRhQ,EAAa0G,KAAK,yCAA0C,CAAEgJ,UAASvM,uBAI3E,CAxHQsX,CAAwB/N,EAAKgD,QAAShD,EAAKvJ,qBAC3C,MACF,IAAK,cACHiW,GAAgB1M,EAAKvJ,oBAAqBuJ,EAAKgD,SAASU,cAAc1D,EAAKgB,OAC3E1N,EAAaD,KAAK,gBAAiB,CACjCoD,oBAAqBuJ,EAAKvJ,oBAC1BuM,QAAShD,EAAKgD,QACdhC,MAAOhB,EAAKgB,QAEd,MACF,IAAK,YAEHyB,GAASzC,EAAK5M,IACd+X,EAAoB6C,SAASC,IACvBA,EAAGzK,2BAA6BxD,EAAKvJ,qBACvCwX,EAAGtK,UAAU3D,EAAK5M,IACpB,IAEF,MACF,IAAK,kBAYX,SAAoC4M,qCAClC,GAAIyM,GAAc,CAChB,MAAMyB,EAAab,WACba,EAAW9F,WAAWpI,EAAKkF,UACjCgJ,EAAWrE,gBACb,MAAO,GAAI7J,EAAKvJ,oBAAqB,CACnC,MAAMyX,EAAalB,GAAyBhN,EAAKvJ,2BAC3CyX,EAAW9F,WAAWpI,EAAKkF,UACjCgJ,EAAWrE,gBACb,MACEvW,EAAa2C,MACX,sFAGN,GAAC,CAzBOkY,CAAqBnO,GACrB,MACF,IAAK,gBA0Ie2E,EAzID3E,EAAK2E,QA0I5BhC,GAAagC,EACbwG,EAAoB6C,SAAS3Z,IAC3BA,EAAEqQ,cAAcC,EAAQ,IAH5B,IAA0BA,EAtCIgJ,EAAiBlX,CA9F7C,KAAE,EA6IApG,KAAK+d,oBACP9a,EAAa/D,MAAM,yBAEnBc,KAAKge,eAAkBC,IAErB,MAAMC,EAAcD,EAAMC,YAC1Bjb,EAAa/D,MAAM,cAAegf,GAElC,MAAMrB,KAAEA,EAAIzW,oBAAEA,EAAmBuM,QAAEA,EAAOhC,MAAEA,GAC1CuN,EAAYC,QACRzB,EAAUL,GAAgBjW,EAAqBuM,GACrD1P,EAAa/D,MAAM,YAAa,CAAEyR,UAClC+L,EAAQnJ,eAAesJ,EAAMqB,EAAYzK,SAAUyK,EAAYxK,SAAUf,GAAS,EAAOhC,EAAM", "x_google_ignoreList": [0, 3, 8]}