{"version": 3, "file": "livekit-client.e2ee.worker.mjs", "sources": ["../node_modules/.pnpm/loglevel@1.9.2/node_modules/loglevel/lib/loglevel.js", "../src/logger.ts", "../node_modules/.pnpm/@livekit+mutex@1.1.1/node_modules/@livekit/mutex/dist/index.mjs", "../src/utils/AsyncQueue.ts", "../src/e2ee/constants.ts", "../src/room/errors.ts", "../src/e2ee/errors.ts", "../src/e2ee/events.ts", "../node_modules/.pnpm/events@3.3.0/node_modules/events/events.js", "../src/e2ee/utils.ts", "../src/e2ee/worker/naluUtils.ts", "../src/e2ee/worker/sifPayload.ts", "../src/e2ee/worker/FrameCryptor.ts", "../src/e2ee/worker/ParticipantKeyHandler.ts", "../src/e2ee/worker/e2ee.worker.ts"], "sourcesContent": ["/*\n* loglevel - https://github.com/pimterry/loglevel\n*\n* Copyright (c) 2013 <PERSON>\n* Licensed under the MIT license.\n*/\n(function (root, definition) {\n    \"use strict\";\n    if (typeof define === 'function' && define.amd) {\n        define(definition);\n    } else if (typeof module === 'object' && module.exports) {\n        module.exports = definition();\n    } else {\n        root.log = definition();\n    }\n}(this, function () {\n    \"use strict\";\n\n    // Slightly dubious tricks to cut down minimized file size\n    var noop = function() {};\n    var undefinedType = \"undefined\";\n    var isIE = (typeof window !== undefinedType) && (typeof window.navigator !== undefinedType) && (\n        /Trident\\/|MSIE /.test(window.navigator.userAgent)\n    );\n\n    var logMethods = [\n        \"trace\",\n        \"debug\",\n        \"info\",\n        \"warn\",\n        \"error\"\n    ];\n\n    var _loggersByName = {};\n    var defaultLogger = null;\n\n    // Cross-browser bind equivalent that works at least back to IE6\n    function bindMethod(obj, methodName) {\n        var method = obj[methodName];\n        if (typeof method.bind === 'function') {\n            return method.bind(obj);\n        } else {\n            try {\n                return Function.prototype.bind.call(method, obj);\n            } catch (e) {\n                // Missing bind shim or IE8 + Modernizr, fallback to wrapping\n                return function() {\n                    return Function.prototype.apply.apply(method, [obj, arguments]);\n                };\n            }\n        }\n    }\n\n    // Trace() doesn't print the message in IE, so for that case we need to wrap it\n    function traceForIE() {\n        if (console.log) {\n            if (console.log.apply) {\n                console.log.apply(console, arguments);\n            } else {\n                // In old IE, native console methods themselves don't have apply().\n                Function.prototype.apply.apply(console.log, [console, arguments]);\n            }\n        }\n        if (console.trace) console.trace();\n    }\n\n    // Build the best logging method possible for this env\n    // Wherever possible we want to bind, not wrap, to preserve stack traces\n    function realMethod(methodName) {\n        if (methodName === 'debug') {\n            methodName = 'log';\n        }\n\n        if (typeof console === undefinedType) {\n            return false; // No method possible, for now - fixed later by enableLoggingWhenConsoleArrives\n        } else if (methodName === 'trace' && isIE) {\n            return traceForIE;\n        } else if (console[methodName] !== undefined) {\n            return bindMethod(console, methodName);\n        } else if (console.log !== undefined) {\n            return bindMethod(console, 'log');\n        } else {\n            return noop;\n        }\n    }\n\n    // These private functions always need `this` to be set properly\n\n    function replaceLoggingMethods() {\n        /*jshint validthis:true */\n        var level = this.getLevel();\n\n        // Replace the actual methods.\n        for (var i = 0; i < logMethods.length; i++) {\n            var methodName = logMethods[i];\n            this[methodName] = (i < level) ?\n                noop :\n                this.methodFactory(methodName, level, this.name);\n        }\n\n        // Define log.log as an alias for log.debug\n        this.log = this.debug;\n\n        // Return any important warnings.\n        if (typeof console === undefinedType && level < this.levels.SILENT) {\n            return \"No console available for logging\";\n        }\n    }\n\n    // In old IE versions, the console isn't present until you first open it.\n    // We build realMethod() replacements here that regenerate logging methods\n    function enableLoggingWhenConsoleArrives(methodName) {\n        return function () {\n            if (typeof console !== undefinedType) {\n                replaceLoggingMethods.call(this);\n                this[methodName].apply(this, arguments);\n            }\n        };\n    }\n\n    // By default, we use closely bound real methods wherever possible, and\n    // otherwise we wait for a console to appear, and then try again.\n    function defaultMethodFactory(methodName, _level, _loggerName) {\n        /*jshint validthis:true */\n        return realMethod(methodName) ||\n               enableLoggingWhenConsoleArrives.apply(this, arguments);\n    }\n\n    function Logger(name, factory) {\n      // Private instance variables.\n      var self = this;\n      /**\n       * The level inherited from a parent logger (or a global default). We\n       * cache this here rather than delegating to the parent so that it stays\n       * in sync with the actual logging methods that we have installed (the\n       * parent could change levels but we might not have rebuilt the loggers\n       * in this child yet).\n       * @type {number}\n       */\n      var inheritedLevel;\n      /**\n       * The default level for this logger, if any. If set, this overrides\n       * `inheritedLevel`.\n       * @type {number|null}\n       */\n      var defaultLevel;\n      /**\n       * A user-specific level for this logger. If set, this overrides\n       * `defaultLevel`.\n       * @type {number|null}\n       */\n      var userLevel;\n\n      var storageKey = \"loglevel\";\n      if (typeof name === \"string\") {\n        storageKey += \":\" + name;\n      } else if (typeof name === \"symbol\") {\n        storageKey = undefined;\n      }\n\n      function persistLevelIfPossible(levelNum) {\n          var levelName = (logMethods[levelNum] || 'silent').toUpperCase();\n\n          if (typeof window === undefinedType || !storageKey) return;\n\n          // Use localStorage if available\n          try {\n              window.localStorage[storageKey] = levelName;\n              return;\n          } catch (ignore) {}\n\n          // Use session cookie as fallback\n          try {\n              window.document.cookie =\n                encodeURIComponent(storageKey) + \"=\" + levelName + \";\";\n          } catch (ignore) {}\n      }\n\n      function getPersistedLevel() {\n          var storedLevel;\n\n          if (typeof window === undefinedType || !storageKey) return;\n\n          try {\n              storedLevel = window.localStorage[storageKey];\n          } catch (ignore) {}\n\n          // Fallback to cookies if local storage gives us nothing\n          if (typeof storedLevel === undefinedType) {\n              try {\n                  var cookie = window.document.cookie;\n                  var cookieName = encodeURIComponent(storageKey);\n                  var location = cookie.indexOf(cookieName + \"=\");\n                  if (location !== -1) {\n                      storedLevel = /^([^;]+)/.exec(\n                          cookie.slice(location + cookieName.length + 1)\n                      )[1];\n                  }\n              } catch (ignore) {}\n          }\n\n          // If the stored level is not valid, treat it as if nothing was stored.\n          if (self.levels[storedLevel] === undefined) {\n              storedLevel = undefined;\n          }\n\n          return storedLevel;\n      }\n\n      function clearPersistedLevel() {\n          if (typeof window === undefinedType || !storageKey) return;\n\n          // Use localStorage if available\n          try {\n              window.localStorage.removeItem(storageKey);\n          } catch (ignore) {}\n\n          // Use session cookie as fallback\n          try {\n              window.document.cookie =\n                encodeURIComponent(storageKey) + \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC\";\n          } catch (ignore) {}\n      }\n\n      function normalizeLevel(input) {\n          var level = input;\n          if (typeof level === \"string\" && self.levels[level.toUpperCase()] !== undefined) {\n              level = self.levels[level.toUpperCase()];\n          }\n          if (typeof level === \"number\" && level >= 0 && level <= self.levels.SILENT) {\n              return level;\n          } else {\n              throw new TypeError(\"log.setLevel() called with invalid level: \" + input);\n          }\n      }\n\n      /*\n       *\n       * Public logger API - see https://github.com/pimterry/loglevel for details\n       *\n       */\n\n      self.name = name;\n\n      self.levels = { \"TRACE\": 0, \"DEBUG\": 1, \"INFO\": 2, \"WARN\": 3,\n          \"ERROR\": 4, \"SILENT\": 5};\n\n      self.methodFactory = factory || defaultMethodFactory;\n\n      self.getLevel = function () {\n          if (userLevel != null) {\n            return userLevel;\n          } else if (defaultLevel != null) {\n            return defaultLevel;\n          } else {\n            return inheritedLevel;\n          }\n      };\n\n      self.setLevel = function (level, persist) {\n          userLevel = normalizeLevel(level);\n          if (persist !== false) {  // defaults to true\n              persistLevelIfPossible(userLevel);\n          }\n\n          // NOTE: in v2, this should call rebuild(), which updates children.\n          return replaceLoggingMethods.call(self);\n      };\n\n      self.setDefaultLevel = function (level) {\n          defaultLevel = normalizeLevel(level);\n          if (!getPersistedLevel()) {\n              self.setLevel(level, false);\n          }\n      };\n\n      self.resetLevel = function () {\n          userLevel = null;\n          clearPersistedLevel();\n          replaceLoggingMethods.call(self);\n      };\n\n      self.enableAll = function(persist) {\n          self.setLevel(self.levels.TRACE, persist);\n      };\n\n      self.disableAll = function(persist) {\n          self.setLevel(self.levels.SILENT, persist);\n      };\n\n      self.rebuild = function () {\n          if (defaultLogger !== self) {\n              inheritedLevel = normalizeLevel(defaultLogger.getLevel());\n          }\n          replaceLoggingMethods.call(self);\n\n          if (defaultLogger === self) {\n              for (var childName in _loggersByName) {\n                _loggersByName[childName].rebuild();\n              }\n          }\n      };\n\n      // Initialize all the internal levels.\n      inheritedLevel = normalizeLevel(\n          defaultLogger ? defaultLogger.getLevel() : \"WARN\"\n      );\n      var initialLevel = getPersistedLevel();\n      if (initialLevel != null) {\n          userLevel = normalizeLevel(initialLevel);\n      }\n      replaceLoggingMethods.call(self);\n    }\n\n    /*\n     *\n     * Top-level API\n     *\n     */\n\n    defaultLogger = new Logger();\n\n    defaultLogger.getLogger = function getLogger(name) {\n        if ((typeof name !== \"symbol\" && typeof name !== \"string\") || name === \"\") {\n            throw new TypeError(\"You must supply a name when creating a logger.\");\n        }\n\n        var logger = _loggersByName[name];\n        if (!logger) {\n            logger = _loggersByName[name] = new Logger(\n                name,\n                defaultLogger.methodFactory\n            );\n        }\n        return logger;\n    };\n\n    // Grab the current global log variable in case of overwrite\n    var _log = (typeof window !== undefinedType) ? window.log : undefined;\n    defaultLogger.noConflict = function() {\n        if (typeof window !== undefinedType &&\n               window.log === defaultLogger) {\n            window.log = _log;\n        }\n\n        return defaultLogger;\n    };\n\n    defaultLogger.getLoggers = function getLoggers() {\n        return _loggersByName;\n    };\n\n    // ES6 default export, for compatibility\n    defaultLogger['default'] = defaultLogger;\n\n    return defaultLogger;\n}));\n", "import * as log from 'loglevel';\n\nexport enum LogLevel {\n  trace = 0,\n  debug = 1,\n  info = 2,\n  warn = 3,\n  error = 4,\n  silent = 5,\n}\n\nexport enum LoggerNames {\n  Default = 'livekit',\n  Room = 'livekit-room',\n  Participant = 'livekit-participant',\n  Track = 'livekit-track',\n  Publication = 'livekit-track-publication',\n  Engine = 'livekit-engine',\n  Signal = 'livekit-signal',\n  PCManager = 'livekit-pc-manager',\n  PCTransport = 'livekit-pc-transport',\n  E2EE = 'lk-e2ee',\n}\n\ntype LogLevelString = keyof typeof LogLevel;\n\nexport type StructuredLogger = log.Logger & {\n  trace: (msg: string, context?: object) => void;\n  debug: (msg: string, context?: object) => void;\n  info: (msg: string, context?: object) => void;\n  warn: (msg: string, context?: object) => void;\n  error: (msg: string, context?: object) => void;\n  setDefaultLevel: (level: log.LogLevelDesc) => void;\n  setLevel: (level: log.LogLevelDesc) => void;\n  getLevel: () => number;\n};\n\nlet livekitLogger = log.getLogger('livekit');\nconst livekitLoggers = Object.values(LoggerNames).map((name) => log.getLogger(name));\n\nlivekitLogger.setDefaultLevel(LogLevel.info);\n\nexport default livekitLogger as StructuredLogger;\n\n/**\n * @internal\n */\nexport function getLogger(name: string) {\n  const logger = log.getLogger(name);\n  logger.setDefaultLevel(livekitLogger.getLevel());\n  return logger as StructuredLogger;\n}\n\nexport function setLogLevel(level: LogLevel | LogLevelString, loggerName?: LoggerNames) {\n  if (loggerName) {\n    log.getLogger(loggerName).setLevel(level);\n  } else {\n    for (const logger of livekitLoggers) {\n      logger.setLevel(level);\n    }\n  }\n}\n\nexport type LogExtension = (level: LogLevel, msg: string, context?: object) => void;\n\n/**\n * use this to hook into the logging function to allow sending internal livekit logs to third party services\n * if set, the browser logs will lose their stacktrace information (see https://github.com/pimterry/loglevel#writing-plugins)\n */\nexport function setLogExtension(extension: LogExtension, logger?: StructuredLogger) {\n  const loggers = logger ? [logger] : livekitLoggers;\n\n  loggers.forEach((logR) => {\n    const originalFactory = logR.methodFactory;\n\n    logR.methodFactory = (methodName, configLevel, loggerName) => {\n      const rawMethod = originalFactory(methodName, configLevel, loggerName);\n\n      const logLevel = LogLevel[methodName as LogLevelString];\n      const needLog = logLevel >= configLevel && logLevel < LogLevel.silent;\n\n      return (msg, context?: [msg: string, context: object]) => {\n        if (context) rawMethod(msg, context);\n        else rawMethod(msg);\n        if (needLog) {\n          extension(logLevel, msg, context);\n        }\n      };\n    };\n    logR.setLevel(logR.getLevel());\n  });\n}\n\nexport const workerLogger = log.getLogger('lk-e2ee') as StructuredLogger;\n", "var e = Object.defineProperty;\nvar h = (i, s, t) => s in i ? e(i, s, { enumerable: !0, configurable: !0, writable: !0, value: t }) : i[s] = t;\nvar o = (i, s, t) => h(i, typeof s != \"symbol\" ? s + \"\" : s, t);\nclass _ {\n  constructor() {\n    o(this, \"_locking\");\n    o(this, \"_locks\");\n    this._locking = Promise.resolve(), this._locks = 0;\n  }\n  isLocked() {\n    return this._locks > 0;\n  }\n  lock() {\n    this._locks += 1;\n    let s;\n    const t = new Promise(\n      (l) => s = () => {\n        this._locks -= 1, l();\n      }\n    ), c = this._locking.then(() => s);\n    return this._locking = this._locking.then(() => t), c;\n  }\n}\nclass n {\n  constructor(s) {\n    o(this, \"_queue\");\n    o(this, \"_limit\");\n    o(this, \"_locks\");\n    this._queue = [], this._limit = s, this._locks = 0;\n  }\n  isLocked() {\n    return this._locks >= this._limit;\n  }\n  async lock() {\n    return this.isLocked() ? new Promise((s) => {\n      this._queue.push(() => {\n        this._locks++, s(this._unlock.bind(this));\n      });\n    }) : (this._locks++, this._unlock.bind(this));\n  }\n  _unlock() {\n    if (this._locks--, this._queue.length && !this.isLocked()) {\n      const s = this._queue.shift();\n      s == null || s();\n    }\n  }\n}\nexport {\n  n as MultiMutex,\n  _ as Mutex\n};\n//# sourceMappingURL=index.mjs.map\n", "import { Mutex } from '@livekit/mutex';\n\ntype QueueTask<T> = () => PromiseLike<T>;\n\nenum QueueTaskStatus {\n  'WAITING',\n  'RUNNING',\n  'COMPLETED',\n}\n\ntype QueueTaskInfo = {\n  id: number;\n  enqueuedAt: number;\n  executedAt?: number;\n  status: QueueTaskStatus;\n};\n\nexport class AsyncQueue {\n  private pendingTasks: Map<number, QueueTaskInfo>;\n\n  private taskMutex: Mutex;\n\n  private nextTaskIndex: number;\n\n  constructor() {\n    this.pendingTasks = new Map();\n    this.taskMutex = new Mutex();\n    this.nextTaskIndex = 0;\n  }\n\n  async run<T>(task: QueueTask<T>) {\n    const taskInfo: QueueTaskInfo = {\n      id: this.nextTaskIndex++,\n      enqueuedAt: Date.now(),\n      status: QueueTaskStatus.WAITING,\n    };\n    this.pendingTasks.set(taskInfo.id, taskInfo);\n    const unlock = await this.taskMutex.lock();\n    try {\n      taskInfo.executedAt = Date.now();\n      taskInfo.status = QueueTaskStatus.RUNNING;\n      return await task();\n    } finally {\n      taskInfo.status = QueueTaskStatus.COMPLETED;\n      this.pendingTasks.delete(taskInfo.id);\n      unlock();\n    }\n  }\n\n  async flush() {\n    return this.run(async () => {});\n  }\n\n  snapshot() {\n    return Array.from(this.pendingTasks.values());\n  }\n}\n", "import type { KeyProviderOptions } from './types';\n\nexport const ENCRYPTION_ALGORITHM = 'AES-GCM';\n\n// How many consecutive frames can fail decrypting before a particular key gets marked as invalid\nexport const DECRYPTION_FAILURE_TOLERANCE = 10;\n\n// We copy the first bytes of the VP8 payload unencrypted.\n// For keyframes this is 10 bytes, for non-keyframes (delta) 3. See\n//   https://tools.ietf.org/html/rfc6386#section-9.1\n// This allows the bridge to continue detecting keyframes (only one byte needed in the JVB)\n// and is also a bit easier for the VP8 decoder (i.e. it generates funny garbage pictures\n// instead of being unable to decode).\n// This is a bit for show and we might want to reduce to 1 unconditionally in the final version.\n//\n// For audio (where frame.type is not set) we do not encrypt the opus TOC byte:\n//   https://tools.ietf.org/html/rfc6716#section-3.1\nexport const UNENCRYPTED_BYTES = {\n  key: 10,\n  delta: 3,\n  audio: 1, // frame.type is not set on audio, so this is set manually\n  empty: 0,\n} as const;\n\n/* We use a 12 byte bit IV. This is signalled in plain together with the\n packet. See https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/encrypt#parameters */\nexport const IV_LENGTH = 12;\n\n// flag set to indicate that e2ee has been setup for sender/receiver;\nexport const E2EE_FLAG = 'lk_e2ee';\n\nexport const SALT = 'LKFrameEncryptionKey';\n\nexport const KEY_PROVIDER_DEFAULTS: KeyProviderOptions = {\n  sharedKey: false,\n  ratchetSalt: SALT,\n  ratchetWindowSize: 8,\n  failureTolerance: DECRYPTION_FAILURE_TOLERANCE,\n  keyringSize: 16,\n} as const;\n\nexport const MAX_SIF_COUNT = 100;\nexport const MAX_SIF_DURATION = 2000;\n", "import { DisconnectReason, RequestResponse_Reason } from '@livekit/protocol';\n\nexport class LivekitError extends Error {\n  code: number;\n\n  constructor(code: number, message?: string) {\n    super(message || 'an error has occured');\n    this.name = 'LiveKitError';\n    this.code = code;\n  }\n}\n\nexport enum ConnectionErrorReason {\n  NotAllowed,\n  ServerUnreachable,\n  InternalError,\n  Cancelled,\n  LeaveRequest,\n  Timeout,\n}\n\nexport class ConnectionError extends LivekitError {\n  status?: number;\n\n  context?: unknown | DisconnectReason;\n\n  reason: ConnectionErrorReason;\n\n  reasonName: string;\n\n  constructor(\n    message: string,\n    reason: ConnectionErrorReason,\n    status?: number,\n    context?: unknown | DisconnectReason,\n  ) {\n    super(1, message);\n    this.name = 'ConnectionError';\n    this.status = status;\n    this.reason = reason;\n    this.context = context;\n    this.reasonName = ConnectionErrorReason[reason];\n  }\n}\n\nexport class DeviceUnsupportedError extends LivekitError {\n  constructor(message?: string) {\n    super(21, message ?? 'device is unsupported');\n    this.name = 'DeviceUnsupportedError';\n  }\n}\n\nexport class TrackInvalidError extends LivekitError {\n  constructor(message?: string) {\n    super(20, message ?? 'track is invalid');\n    this.name = 'TrackInvalidError';\n  }\n}\n\nexport class UnsupportedServer extends LivekitError {\n  constructor(message?: string) {\n    super(10, message ?? 'unsupported server');\n    this.name = 'UnsupportedServer';\n  }\n}\n\nexport class UnexpectedConnectionState extends LivekitError {\n  constructor(message?: string) {\n    super(12, message ?? 'unexpected connection state');\n    this.name = 'UnexpectedConnectionState';\n  }\n}\n\nexport class NegotiationError extends LivekitError {\n  constructor(message?: string) {\n    super(13, message ?? 'unable to negotiate');\n    this.name = 'NegotiationError';\n  }\n}\n\nexport class PublishDataError extends LivekitError {\n  constructor(message?: string) {\n    super(14, message ?? 'unable to publish data');\n    this.name = 'PublishDataError';\n  }\n}\n\nexport class PublishTrackError extends LivekitError {\n  status: number;\n\n  constructor(message: string, status: number) {\n    super(15, message);\n    this.name = 'PublishTrackError';\n    this.status = status;\n  }\n}\n\nexport type RequestErrorReason =\n  | Exclude<RequestResponse_Reason, RequestResponse_Reason.OK>\n  | 'TimeoutError';\n\nexport class SignalRequestError extends LivekitError {\n  reason: RequestErrorReason;\n\n  reasonName: string;\n\n  constructor(message: string, reason: RequestErrorReason) {\n    super(15, message);\n    this.reason = reason;\n    this.reasonName = typeof reason === 'string' ? reason : RequestResponse_Reason[reason];\n  }\n}\n\n// NOTE: matches with https://github.com/livekit/client-sdk-swift/blob/f37bbd260d61e165084962db822c79f995f1a113/Sources/LiveKit/DataStream/StreamError.swift#L17\nexport enum DataStreamErrorReason {\n  // Unable to open a stream with the same ID more than once.\n  AlreadyOpened = 0,\n\n  // Stream closed abnormally by remote participant.\n  AbnormalEnd = 1,\n\n  // Incoming chunk data could not be decoded.\n  DecodeFailed = 2,\n\n  // Read length exceeded total length specified in stream header.\n  LengthExceeded = 3,\n\n  // Read length less than total length specified in stream header.\n  Incomplete = 4,\n\n  // Unable to register a stream handler more than once.\n  HandlerAlreadyRegistered = 7,\n}\n\nexport class DataStreamError extends LivekitError {\n  reason: DataStreamErrorReason;\n\n  reasonName: string;\n\n  constructor(message: string, reason: DataStreamErrorReason) {\n    super(16, message);\n    this.name = 'DataStreamError';\n    this.reason = reason;\n    this.reasonName = DataStreamErrorReason[reason];\n  }\n}\n\nexport enum MediaDeviceFailure {\n  // user rejected permissions\n  PermissionDenied = 'PermissionDenied',\n  // device is not available\n  NotFound = 'NotFound',\n  // device is in use. On Windows, only a single tab may get access to a device at a time.\n  DeviceInUse = 'DeviceInUse',\n  Other = 'Other',\n}\n\nexport namespace MediaDeviceFailure {\n  export function getFailure(error: any): MediaDeviceFailure | undefined {\n    if (error && 'name' in error) {\n      if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {\n        return MediaDeviceFailure.NotFound;\n      }\n      if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {\n        return MediaDeviceFailure.PermissionDenied;\n      }\n      if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {\n        return MediaDeviceFailure.DeviceInUse;\n      }\n      return MediaDeviceFailure.Other;\n    }\n  }\n}\n", "import { LivekitError } from '../room/errors';\n\nexport enum CryptorErrorReason {\n  InvalidKey = 0,\n  MissingKey = 1,\n  InternalError = 2,\n}\n\nexport class CryptorError extends LivekitError {\n  reason: CryptorErrorReason;\n\n  participantIdentity?: string;\n\n  constructor(\n    message?: string,\n    reason: CryptorErrorReason = CryptorErrorReason.InternalError,\n    participantIdentity?: string,\n  ) {\n    super(40, message);\n    this.reason = reason;\n    this.participantIdentity = participantIdentity;\n  }\n}\n", "import type Participant from '../room/participant/Participant';\nimport type { CryptorError } from './errors';\nimport type { KeyInfo, RatchetResult } from './types';\n\nexport enum KeyProviderEvent {\n  SetKey = 'setKey',\n  /** Event for requesting to ratchet the key used to encrypt the stream */\n  RatchetRequest = 'ratchetRequest',\n  /** Emitted when a key is ratcheted. Could be after auto-ratcheting on decryption failure or\n   *  following a `RatchetRequest`, will contain the ratcheted key material */\n  KeyRatcheted = 'keyRatcheted',\n}\n\nexport type KeyProviderCallbacks = {\n  [KeyProviderEvent.SetKey]: (keyInfo: KeyInfo) => void;\n  [KeyProviderEvent.RatchetRequest]: (participantIdentity?: string, keyIndex?: number) => void;\n  [KeyProviderEvent.KeyRatcheted]: (\n    ratchetedResult: RatchetResult,\n    participantIdentity?: string,\n    keyIndex?: number,\n  ) => void;\n};\n\nexport enum KeyHandlerEvent {\n  /** Emitted when a key has been ratcheted. Is emitted when any key has been ratcheted\n   * i.e. when the FrameCryptor tried to ratchet when decryption is failing  */\n  KeyRatcheted = 'keyRatcheted',\n}\n\nexport type ParticipantKeyHandlerCallbacks = {\n  [KeyHandlerEvent.KeyRatcheted]: (\n    ratchetResult: RatchetResult,\n    participantIdentity: string,\n    keyIndex?: number,\n  ) => void;\n};\n\nexport enum EncryptionEvent {\n  ParticipantEncryptionStatusChanged = 'participantEncryptionStatusChanged',\n  EncryptionError = 'encryptionError',\n}\n\nexport type E2EEManagerCallbacks = {\n  [EncryptionEvent.ParticipantEncryptionStatusChanged]: (\n    enabled: boolean,\n    participant: Participant,\n  ) => void;\n  [EncryptionEvent.EncryptionError]: (error: Error) => void;\n};\n\nexport type CryptorCallbacks = {\n  [CryptorEvent.Error]: (error: CryptorError) => void;\n};\n\nexport enum CryptorEvent {\n  Error = 'cryptorError',\n}\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "import { ENCRYPTION_ALGORITHM } from './constants';\n\nexport function isE2EESupported() {\n  return isInsertableStreamSupported() || isScriptTransformSupported();\n}\n\nexport function isScriptTransformSupported() {\n  // @ts-ignore\n  return typeof window.RTCRtpScriptTransform !== 'undefined';\n}\n\nexport function isInsertableStreamSupported() {\n  return (\n    typeof window.RTCRtpSender !== 'undefined' &&\n    // @ts-ignore\n    typeof window.RTCRtpSender.prototype.createEncodedStreams !== 'undefined'\n  );\n}\n\nexport function isVideoFrame(\n  frame: RTCEncodedAudioFrame | RTCEncodedVideoFrame,\n): frame is RTCEncodedVideoFrame {\n  return 'type' in frame;\n}\n\nexport async function importKey(\n  keyBytes: Uint8Array | ArrayBuffer,\n  algorithm: string | { name: string } = { name: ENCRYPTION_ALGORITHM },\n  usage: 'derive' | 'encrypt' = 'encrypt',\n) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/importKey\n  return crypto.subtle.importKey(\n    'raw',\n    keyBytes,\n    algorithm,\n    false,\n    usage === 'derive' ? ['deriveBits', 'deriveKey'] : ['encrypt', 'decrypt'],\n  );\n}\n\nexport async function createKeyMaterialFromString(password: string) {\n  let enc = new TextEncoder();\n\n  const keyMaterial = await crypto.subtle.importKey(\n    'raw',\n    enc.encode(password),\n    {\n      name: 'PBKDF2',\n    },\n    false,\n    ['deriveBits', 'deriveKey'],\n  );\n\n  return keyMaterial;\n}\n\nexport async function createKeyMaterialFromBuffer(cryptoBuffer: ArrayBuffer) {\n  const keyMaterial = await crypto.subtle.importKey('raw', cryptoBuffer, 'HKDF', false, [\n    'deriveBits',\n    'deriveKey',\n  ]);\n\n  return keyMaterial;\n}\n\nfunction getAlgoOptions(algorithmName: string, salt: string) {\n  const textEncoder = new TextEncoder();\n  const encodedSalt = textEncoder.encode(salt);\n  switch (algorithmName) {\n    case 'HKDF':\n      return {\n        name: 'HKDF',\n        salt: encodedSalt,\n        hash: 'SHA-256',\n        info: new ArrayBuffer(128),\n      };\n    case 'PBKDF2': {\n      return {\n        name: 'PBKDF2',\n        salt: encodedSalt,\n        hash: 'SHA-256',\n        iterations: 100000,\n      };\n    }\n    default:\n      throw new Error(`algorithm ${algorithmName} is currently unsupported`);\n  }\n}\n\n/**\n * Derives a set of keys from the master key.\n * See https://tools.ietf.org/html/draft-omara-sframe-00#section-4.3.1\n */\nexport async function deriveKeys(material: CryptoKey, salt: string) {\n  const algorithmOptions = getAlgoOptions(material.algorithm.name, salt);\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/deriveKey#HKDF\n  // https://developer.mozilla.org/en-US/docs/Web/API/HkdfParams\n  const encryptionKey = await crypto.subtle.deriveKey(\n    algorithmOptions,\n    material,\n    {\n      name: ENCRYPTION_ALGORITHM,\n      length: 128,\n    },\n    false,\n    ['encrypt', 'decrypt'],\n  );\n\n  return { material, encryptionKey };\n}\n\nexport function createE2EEKey(): Uint8Array {\n  return window.crypto.getRandomValues(new Uint8Array(32));\n}\n\n/**\n * Ratchets a key. See\n * https://tools.ietf.org/html/draft-omara-sframe-00#section-4.3.5.1\n */\nexport async function ratchet(material: CryptoKey, salt: string): Promise<ArrayBuffer> {\n  const algorithmOptions = getAlgoOptions(material.algorithm.name, salt);\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/deriveBits\n  return crypto.subtle.deriveBits(algorithmOptions, material, 256);\n}\n\nexport function needsRbspUnescaping(frameData: Uint8Array) {\n  for (var i = 0; i < frameData.length - 3; i++) {\n    if (frameData[i] == 0 && frameData[i + 1] == 0 && frameData[i + 2] == 3) return true;\n  }\n  return false;\n}\n\nexport function parseRbsp(stream: Uint8Array): Uint8Array {\n  const dataOut: number[] = [];\n  var length = stream.length;\n  for (var i = 0; i < stream.length; ) {\n    // Be careful about over/underflow here. byte_length_ - 3 can underflow, and\n    // i + 3 can overflow, but byte_length_ - i can't, because i < byte_length_\n    // above, and that expression will produce the number of bytes left in\n    // the stream including the byte at i.\n    if (length - i >= 3 && !stream[i] && !stream[i + 1] && stream[i + 2] == 3) {\n      // Two rbsp bytes.\n      dataOut.push(stream[i++]);\n      dataOut.push(stream[i++]);\n      // Skip the emulation byte.\n      i++;\n    } else {\n      // Single rbsp byte.\n      dataOut.push(stream[i++]);\n    }\n  }\n  return new Uint8Array(dataOut);\n}\n\nconst kZerosInStartSequence = 2;\nconst kEmulationByte = 3;\n\nexport function writeRbsp(data_in: Uint8Array): Uint8Array {\n  const dataOut: number[] = [];\n  var numConsecutiveZeros = 0;\n  for (var i = 0; i < data_in.length; ++i) {\n    var byte = data_in[i];\n    if (byte <= kEmulationByte && numConsecutiveZeros >= kZerosInStartSequence) {\n      // Need to escape.\n      dataOut.push(kEmulationByte);\n      numConsecutiveZeros = 0;\n    }\n    dataOut.push(byte);\n    if (byte == 0) {\n      ++numConsecutiveZeros;\n    } else {\n      numConsecutiveZeros = 0;\n    }\n  }\n  return new Uint8Array(dataOut);\n}\n", "/**\n * NALU (Network Abstraction Layer Unit) utilities for H.264 and H.265 video processing\n * Contains functions for parsing and working with NALUs in video frames\n */\n\n/**\n * Mask for extracting NALU type from H.264 header byte\n */\nconst kH264NaluTypeMask = 0x1f;\n\n/**\n * H.264 NALU types according to RFC 6184\n */\nenum H264NALUType {\n  /** Coded slice of a non-IDR picture */\n  SLICE_NON_IDR = 1,\n  /** Coded slice data partition A */\n  SLICE_PARTITION_A = 2,\n  /** Coded slice data partition B */\n  SLICE_PARTITION_B = 3,\n  /** Coded slice data partition C */\n  SLICE_PARTITION_C = 4,\n  /** Coded slice of an IDR picture */\n  SLICE_IDR = 5,\n  /** Supplemental enhancement information */\n  SEI = 6,\n  /** Sequence parameter set */\n  SPS = 7,\n  /** Picture parameter set */\n  PPS = 8,\n  /** Access unit delimiter */\n  AUD = 9,\n  /** End of sequence */\n  END_SEQ = 10,\n  /** End of stream */\n  END_STREAM = 11,\n  /** Filler data */\n  FILLER_DATA = 12,\n  /** Sequence parameter set extension */\n  SPS_EXT = 13,\n  /** Prefix NAL unit */\n  PREFIX_NALU = 14,\n  /** Subset sequence parameter set */\n  SUBSET_SPS = 15,\n  /** Depth parameter set */\n  DPS = 16,\n\n  // 17, 18 reserved\n\n  /** Coded slice of an auxiliary coded picture without partitioning */\n  SLICE_AUX = 19,\n  /** Coded slice extension */\n  SLICE_EXT = 20,\n  /** Coded slice extension for a depth view component or a 3D-AVC texture view component */\n  SLICE_LAYER_EXT = 21,\n\n  // 22, 23 reserved\n}\n\n/**\n * H.265/HEVC NALU types according to ITU-T H.265\n */\nenum H265NALUType {\n  /** Coded slice segment of a non-TSA, non-STSA trailing picture */\n  TRAIL_N = 0,\n  /** Coded slice segment of a non-TSA, non-STSA trailing picture */\n  TRAIL_R = 1,\n  /** Coded slice segment of a TSA picture */\n  TSA_N = 2,\n  /** Coded slice segment of a TSA picture */\n  TSA_R = 3,\n  /** Coded slice segment of an STSA picture */\n  STSA_N = 4,\n  /** Coded slice segment of an STSA picture */\n  STSA_R = 5,\n  /** Coded slice segment of a RADL picture */\n  RADL_N = 6,\n  /** Coded slice segment of a RADL picture */\n  RADL_R = 7,\n  /** Coded slice segment of a RASL picture */\n  RASL_N = 8,\n  /** Coded slice segment of a RASL picture */\n  RASL_R = 9,\n\n  // 10-15 reserved\n\n  /** Coded slice segment of a BLA picture */\n  BLA_W_LP = 16,\n  /** Coded slice segment of a BLA picture */\n  BLA_W_RADL = 17,\n  /** Coded slice segment of a BLA picture */\n  BLA_N_LP = 18,\n  /** Coded slice segment of an IDR picture */\n  IDR_W_RADL = 19,\n  /** Coded slice segment of an IDR picture */\n  IDR_N_LP = 20,\n  /** Coded slice segment of a CRA picture */\n  CRA_NUT = 21,\n\n  // 22-31 reserved\n\n  /** Video parameter set */\n  VPS_NUT = 32,\n  /** Sequence parameter set */\n  SPS_NUT = 33,\n  /** Picture parameter set */\n  PPS_NUT = 34,\n  /** Access unit delimiter */\n  AUD_NUT = 35,\n  /** End of sequence */\n  EOS_NUT = 36,\n  /** End of bitstream */\n  EOB_NUT = 37,\n  /** Filler data */\n  FD_NUT = 38,\n  /** Supplemental enhancement information */\n  PREFIX_SEI_NUT = 39,\n  /** Supplemental enhancement information */\n  SUFFIX_SEI_NUT = 40,\n\n  // 41-47 reserved\n  // 48-63 unspecified\n}\n\n/**\n * Parse H.264 NALU type from the first byte of a NALU\n * @param startByte First byte of the NALU\n * @returns H.264 NALU type\n */\nfunction parseH264NALUType(startByte: number): H264NALUType {\n  return startByte & kH264NaluTypeMask;\n}\n\n/**\n * Parse H.265 NALU type from the first byte of a NALU\n * @param firstByte First byte of the NALU\n * @returns H.265 NALU type\n */\nfunction parseH265NALUType(firstByte: number): H265NALUType {\n  // In H.265, NALU type is in bits 1-6 (shifted right by 1)\n  return (firstByte >> 1) & 0x3f;\n}\n\n/**\n * Check if H.264 NALU type is a slice (IDR or non-IDR)\n * @param naluType H.264 NALU type\n * @returns True if the NALU is a slice\n */\nfunction isH264SliceNALU(naluType: H264NALUType): boolean {\n  return naluType === H264NALUType.SLICE_IDR || naluType === H264NALUType.SLICE_NON_IDR;\n}\n\n/**\n * Check if H.265 NALU type is a slice\n * @param naluType H.265 NALU type\n * @returns True if the NALU is a slice\n */\nfunction isH265SliceNALU(naluType: H265NALUType): boolean {\n  return (\n    // VCL NALUs (Video Coding Layer) - slice segments\n    naluType === H265NALUType.TRAIL_N ||\n    naluType === H265NALUType.TRAIL_R ||\n    naluType === H265NALUType.TSA_N ||\n    naluType === H265NALUType.TSA_R ||\n    naluType === H265NALUType.STSA_N ||\n    naluType === H265NALUType.STSA_R ||\n    naluType === H265NALUType.RADL_N ||\n    naluType === H265NALUType.RADL_R ||\n    naluType === H265NALUType.RASL_N ||\n    naluType === H265NALUType.RASL_R ||\n    naluType === H265NALUType.BLA_W_LP ||\n    naluType === H265NALUType.BLA_W_RADL ||\n    naluType === H265NALUType.BLA_N_LP ||\n    naluType === H265NALUType.IDR_W_RADL ||\n    naluType === H265NALUType.IDR_N_LP ||\n    naluType === H265NALUType.CRA_NUT\n  );\n}\n\n/**\n * Detected codec type from NALU analysis\n */\nexport type DetectedCodec = 'h264' | 'h265' | 'unknown';\n\n/**\n * Result of NALU processing for frame encryption\n */\nexport interface NALUProcessingResult {\n  /** Number of unencrypted bytes at the start of the frame */\n  unencryptedBytes: number;\n  /** Detected codec type */\n  detectedCodec: DetectedCodec;\n  /** Whether this frame requires NALU processing */\n  requiresNALUProcessing: boolean;\n}\n\n/**\n * Detect codec type by examining NALU types in the data\n * @param data Frame data\n * @param naluIndices Indices where NALUs start\n * @returns Detected codec type\n */\nfunction detectCodecFromNALUs(data: Uint8Array, naluIndices: number[]): DetectedCodec {\n  for (const naluIndex of naluIndices) {\n    if (isH264SliceNALU(parseH264NALUType(data[naluIndex]))) return 'h264';\n    if (isH265SliceNALU(parseH265NALUType(data[naluIndex]))) return 'h265';\n  }\n  return 'unknown';\n}\n\n/**\n * Find the first slice NALU and return the number of unencrypted bytes\n * @param data Frame data\n * @param naluIndices Indices where NALUs start\n * @param codec Codec type to use for parsing\n * @returns Number of unencrypted bytes (index + 2) or null if no slice found\n */\nfunction findSliceNALUUnencryptedBytes(\n  data: Uint8Array,\n  naluIndices: number[],\n  codec: 'h264' | 'h265',\n): number | null {\n  for (const index of naluIndices) {\n    if (codec === 'h265') {\n      const type = parseH265NALUType(data[index]);\n      if (isH265SliceNALU(type)) {\n        return index + 2;\n      }\n    } else {\n      const type = parseH264NALUType(data[index]);\n      if (isH264SliceNALU(type)) {\n        return index + 2;\n      }\n    }\n  }\n  return null;\n}\n\n/**\n * Find all NALU start indices in a byte stream\n * Supports both H.264 and H.265 with 3-byte and 4-byte start codes\n *\n * This function slices the NALUs present in the supplied buffer, assuming it is already byte-aligned.\n * Code adapted from https://github.com/medooze/h264-frame-parser/blob/main/lib/NalUnits.ts to return indices only\n *\n * @param stream Byte stream containing NALUs\n * @returns Array of indices where NALUs start (after the start code)\n */\nfunction findNALUIndices(stream: Uint8Array): number[] {\n  const result: number[] = [];\n  let start = 0,\n    pos = 0,\n    searchLength = stream.length - 3; // Changed to -3 to handle 4-byte start codes\n\n  while (pos < searchLength) {\n    // skip until end of current NALU - check for both 3-byte and 4-byte start codes\n    while (pos < searchLength) {\n      // Check for 4-byte start code: 0x00 0x00 0x00 0x01\n      if (\n        pos < searchLength - 1 &&\n        stream[pos] === 0 &&\n        stream[pos + 1] === 0 &&\n        stream[pos + 2] === 0 &&\n        stream[pos + 3] === 1\n      ) {\n        break;\n      }\n      // Check for 3-byte start code: 0x00 0x00 0x01\n      if (stream[pos] === 0 && stream[pos + 1] === 0 && stream[pos + 2] === 1) {\n        break;\n      }\n      pos++;\n    }\n\n    if (pos >= searchLength) pos = stream.length;\n\n    // remove trailing zeros from current NALU\n    let end = pos;\n    while (end > start && stream[end - 1] === 0) end--;\n\n    // save current NALU\n    if (start === 0) {\n      if (end !== start) throw TypeError('byte stream contains leading data');\n    } else {\n      result.push(start);\n    }\n\n    // begin new NALU - determine start code length\n    let startCodeLength = 3;\n    if (\n      pos < stream.length - 3 &&\n      stream[pos] === 0 &&\n      stream[pos + 1] === 0 &&\n      stream[pos + 2] === 0 &&\n      stream[pos + 3] === 1\n    ) {\n      startCodeLength = 4;\n    }\n\n    start = pos = pos + startCodeLength;\n  }\n  return result;\n}\n\n/**\n * Process NALU data for frame encryption, detecting codec and finding unencrypted bytes\n * @param data Frame data\n * @param knownCodec Known codec from other sources (optional)\n * @returns NALU processing result\n */\nexport function processNALUsForEncryption(\n  data: Uint8Array,\n  knownCodec?: 'h264' | 'h265',\n): NALUProcessingResult {\n  const naluIndices = findNALUIndices(data);\n  const detectedCodec = knownCodec ?? detectCodecFromNALUs(data, naluIndices);\n\n  if (detectedCodec === 'unknown') {\n    return { unencryptedBytes: 0, detectedCodec, requiresNALUProcessing: false };\n  }\n\n  const unencryptedBytes = findSliceNALUUnencryptedBytes(data, naluIndices, detectedCodec);\n  if (unencryptedBytes === null) {\n    throw new TypeError('Could not find NALU');\n  }\n\n  return { unencryptedBytes, detectedCodec, requiresNALUProcessing: true };\n}\n", "import type { VideoCodec } from '../..';\n\n//  Payload definitions taken from https://github.com/livekit/livekit/blob/master/pkg/sfu/downtrack.go#L104\n\nexport const VP8KeyFrame8x8 = new Uint8Array([\n  0x10, 0x02, 0x00, 0x9d, 0x01, 0x2a, 0x08, 0x00, 0x08, 0x00, 0x00, 0x47, 0x08, 0x85, 0x85, 0x88,\n  0x85, 0x84, 0x88, 0x02, 0x02, 0x00, 0x0c, 0x0d, 0x60, 0x00, 0xfe, 0xff, 0xab, 0x50, 0x80,\n]);\n\nexport const H264KeyFrame2x2SPS = new Uint8Array([\n  0x67, 0x42, 0xc0, 0x1f, 0x0f, 0xd9, 0x1f, 0x88, 0x88, 0x84, 0x00, 0x00, 0x03, 0x00, 0x04, 0x00,\n  0x00, 0x03, 0x00, 0xc8, 0x3c, 0x60, 0xc9, 0x20,\n]);\n\nexport const H264KeyFrame2x2PPS = new Uint8Array([0x68, 0x87, 0xcb, 0x83, 0xcb, 0x20]);\n\nexport const H264KeyFrame2x2IDR = new Uint8Array([\n  0x65, 0x88, 0x84, 0x0a, 0xf2, 0x62, 0x80, 0x00, 0xa7, 0xbe,\n]);\n\nexport const H264KeyFrame2x2 = [H264KeyFrame2x2SPS, H264KeyFrame2x2PPS, H264KeyFrame2x2IDR];\n\nexport const OpusSilenceFrame = new Uint8Array([\n  0xf8, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n]);\n\n/**\n * Create a crypto hash using Web Crypto API for secure comparison operations\n */\nasync function cryptoHash(data: Uint8Array | ArrayBuffer): Promise<string> {\n  const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n  const hashArray = new Uint8Array(hashBuffer);\n  return Array.from(hashArray)\n    .map((b) => b.toString(16).padStart(2, '0'))\n    .join('');\n}\n\n/**\n * Pre-computed SHA-256 hashes for secure comparison operations\n */\nexport const CryptoHashes = {\n  VP8KeyFrame8x8: 'ef0161653d8b2b23aad46624b420af1d03ce48950e9fc85718028f91b50f9219',\n  H264KeyFrame2x2SPS: 'f0a0e09647d891d6d50aa898bce7108090375d0d55e50a2bb21147afee558e44',\n  H264KeyFrame2x2PPS: '61d9665eed71b6d424ae9539330a3bdd5cb386d4d781c808219a6e36750493a7',\n  H264KeyFrame2x2IDR: 'faffc26b68a2fc09096fa20f3351e706398b6f838a7500c8063472c2e476e90d',\n  OpusSilenceFrame: 'aad8d31fc56b2802ca500e58c2fb9d0b29ad71bb7cb52cd6530251eade188988',\n} as const;\n\n/**\n * Check if a byte array matches any of the known SIF payload frame types using secure crypto hashes\n */\nexport async function identifySifPayload(\n  data: Uint8Array | ArrayBuffer,\n): Promise<VideoCodec | 'opus' | null> {\n  const hash = await cryptoHash(data);\n\n  switch (hash) {\n    case CryptoHashes.VP8KeyFrame8x8:\n      return 'vp8';\n    case CryptoHashes.H264KeyFrame2x2SPS:\n      return 'h264';\n    case CryptoHashes.H264KeyFrame2x2PPS:\n      return 'h264';\n    case CryptoHashes.H264KeyFrame2x2IDR:\n      return 'h264';\n    case CryptoHashes.OpusSilenceFrame:\n      return 'opus';\n    default:\n      return null;\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-unused-vars */\n// TODO code inspired by https://github.com/webrtc/samples/blob/gh-pages/src/content/insertable-streams/endtoend-encryption/js/worker.js\nimport { EventEmitter } from 'events';\nimport type TypedEventEmitter from 'typed-emitter';\nimport { workerLogger } from '../../logger';\nimport type { VideoCodec } from '../../room/track/options';\nimport { ENCRYPTION_ALGORITHM, IV_LENGTH, UNENCRYPTED_BYTES } from '../constants';\nimport { CryptorError, CryptorErrorReason } from '../errors';\nimport { type CryptorCallbacks, CryptorEvent } from '../events';\nimport type { DecodeRatchetOptions, KeyProviderOptions, KeySet, RatchetResult } from '../types';\nimport { deriveKeys, isVideoFrame, needsRbspUnescaping, parseRbsp, writeRbsp } from '../utils';\nimport type { ParticipantKeyHandler } from './ParticipantKeyHandler';\nimport { processNALUsForEncryption } from './naluUtils';\nimport { identifySifPayload } from './sifPayload';\n\nexport const encryptionEnabledMap: Map<string, boolean> = new Map();\n\nexport interface FrameCryptorConstructor {\n  new (opts?: unknown): BaseFrameCryptor;\n}\n\nexport interface TransformerInfo {\n  readable: ReadableStream;\n  writable: WritableStream;\n  transformer: TransformStream;\n  abortController: AbortController;\n}\n\nexport class BaseFrameCryptor extends (EventEmitter as new () => TypedEventEmitter<CryptorCallbacks>) {\n  protected encodeFunction(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    controller: TransformStreamDefaultController,\n  ): Promise<any> {\n    throw Error('not implemented for subclass');\n  }\n\n  protected decodeFunction(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    controller: TransformStreamDefaultController,\n  ): Promise<any> {\n    throw Error('not implemented for subclass');\n  }\n}\n\n/**\n * Cryptor is responsible for en-/decrypting media frames.\n * Each Cryptor instance is responsible for en-/decrypting a single mediaStreamTrack.\n */\nexport class FrameCryptor extends BaseFrameCryptor {\n  private sendCounts: Map<number, number>;\n\n  private participantIdentity: string | undefined;\n\n  private trackId: string | undefined;\n\n  private keys: ParticipantKeyHandler;\n\n  private videoCodec?: VideoCodec;\n\n  private rtpMap: Map<number, VideoCodec>;\n\n  private keyProviderOptions: KeyProviderOptions;\n\n  /**\n   * used for detecting server injected unencrypted frames\n   */\n  private sifTrailer: Uint8Array;\n\n  private detectedCodec?: VideoCodec;\n\n  private isTransformActive: boolean = false;\n\n  constructor(opts: {\n    keys: ParticipantKeyHandler;\n    participantIdentity: string;\n    keyProviderOptions: KeyProviderOptions;\n    sifTrailer?: Uint8Array;\n  }) {\n    super();\n    this.sendCounts = new Map();\n    this.keys = opts.keys;\n    this.participantIdentity = opts.participantIdentity;\n    this.rtpMap = new Map();\n    this.keyProviderOptions = opts.keyProviderOptions;\n    this.sifTrailer = opts.sifTrailer ?? Uint8Array.from([]);\n  }\n\n  private get logContext() {\n    return {\n      participant: this.participantIdentity,\n      mediaTrackId: this.trackId,\n      fallbackCodec: this.videoCodec,\n    };\n  }\n\n  /**\n   * Assign a different participant to the cryptor.\n   * useful for transceiver re-use\n   * @param id\n   * @param keys\n   */\n  setParticipant(id: string, keys: ParticipantKeyHandler) {\n    workerLogger.debug('setting new participant on cryptor', {\n      ...this.logContext,\n      participant: id,\n    });\n    if (this.participantIdentity) {\n      workerLogger.error(\n        'cryptor has already a participant set, participant should have been unset before',\n        {\n          ...this.logContext,\n        },\n      );\n    }\n    this.participantIdentity = id;\n    this.keys = keys;\n  }\n\n  unsetParticipant() {\n    workerLogger.debug('unsetting participant', this.logContext);\n    this.participantIdentity = undefined;\n  }\n\n  isEnabled() {\n    if (this.participantIdentity) {\n      return encryptionEnabledMap.get(this.participantIdentity);\n    } else {\n      return undefined;\n    }\n  }\n\n  getParticipantIdentity() {\n    return this.participantIdentity;\n  }\n\n  getTrackId() {\n    return this.trackId;\n  }\n\n  /**\n   * Update the video codec used by the mediaStreamTrack\n   * @param codec\n   */\n  setVideoCodec(codec: VideoCodec) {\n    this.videoCodec = codec;\n  }\n\n  /**\n   * rtp payload type map used for figuring out codec of payload type when encoding\n   * @param map\n   */\n  setRtpMap(map: Map<number, VideoCodec>) {\n    this.rtpMap = map;\n  }\n\n  setupTransform(\n    operation: 'encode' | 'decode',\n    readable: ReadableStream<RTCEncodedVideoFrame | RTCEncodedAudioFrame>,\n    writable: WritableStream<RTCEncodedVideoFrame | RTCEncodedAudioFrame>,\n    trackId: string,\n    isReuse: boolean,\n    codec?: VideoCodec,\n  ) {\n    if (codec) {\n      workerLogger.info('setting codec on cryptor to', { codec });\n      this.videoCodec = codec;\n    }\n\n    workerLogger.debug('Setting up frame cryptor transform', {\n      operation,\n      passedTrackId: trackId,\n      codec,\n      ...this.logContext,\n    });\n\n    if (isReuse && this.isTransformActive) {\n      workerLogger.debug('reuse transform', {\n        ...this.logContext,\n      });\n      return;\n    }\n\n    const transformFn = operation === 'encode' ? this.encodeFunction : this.decodeFunction;\n    const transformStream = new TransformStream({\n      transform: transformFn.bind(this),\n    });\n\n    this.isTransformActive = true;\n\n    readable\n      .pipeThrough(transformStream)\n      .pipeTo(writable)\n      .catch((e) => {\n        workerLogger.warn(e);\n        this.emit(\n          CryptorEvent.Error,\n          e instanceof CryptorError\n            ? e\n            : new CryptorError(e.message, undefined, this.participantIdentity),\n        );\n      })\n      .finally(() => {\n        this.isTransformActive = false;\n      });\n    this.trackId = trackId;\n  }\n\n  setSifTrailer(trailer: Uint8Array) {\n    workerLogger.debug('setting SIF trailer', { ...this.logContext, trailer });\n    this.sifTrailer = trailer;\n  }\n\n  /**\n   * Function that will be injected in a stream and will encrypt the given encoded frames.\n   *\n   * @param {RTCEncodedVideoFrame|RTCEncodedAudioFrame} encodedFrame - Encoded video frame.\n   * @param {TransformStreamDefaultController} controller - TransportStreamController.\n   *\n   * The VP8 payload descriptor described in\n   * https://tools.ietf.org/html/rfc7741#section-4.2\n   * is part of the RTP packet and not part of the frame and is not controllable by us.\n   * This is fine as the SFU keeps having access to it for routing.\n   *\n   * The encrypted frame is formed as follows:\n   * 1) Find unencrypted byte length, depending on the codec, frame type and kind.\n   * 2) Form the GCM IV for the frame as described above.\n   * 3) Encrypt the rest of the frame using AES-GCM.\n   * 4) Allocate space for the encrypted frame.\n   * 5) Copy the unencrypted bytes to the start of the encrypted frame.\n   * 6) Append the ciphertext to the encrypted frame.\n   * 7) Append the IV.\n   * 8) Append a single byte for the key identifier.\n   * 9) Enqueue the encrypted frame for sending.\n   */\n  protected async encodeFunction(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    controller: TransformStreamDefaultController,\n  ) {\n    if (\n      !this.isEnabled() ||\n      // skip for encryption for empty dtx frames\n      encodedFrame.data.byteLength === 0\n    ) {\n      return controller.enqueue(encodedFrame);\n    }\n    const keySet = this.keys.getKeySet();\n    if (!keySet) {\n      this.emit(\n        CryptorEvent.Error,\n        new CryptorError(\n          `key set not found for ${\n            this.participantIdentity\n          } at index ${this.keys.getCurrentKeyIndex()}`,\n          CryptorErrorReason.MissingKey,\n          this.participantIdentity,\n        ),\n      );\n      return;\n    }\n    const { encryptionKey } = keySet;\n    const keyIndex = this.keys.getCurrentKeyIndex();\n\n    if (encryptionKey) {\n      const iv = this.makeIV(\n        encodedFrame.getMetadata().synchronizationSource ?? -1,\n        encodedFrame.timestamp,\n      );\n      let frameInfo = this.getUnencryptedBytes(encodedFrame);\n\n      // Thіs is not encrypted and contains the VP8 payload descriptor or the Opus TOC byte.\n      const frameHeader = new Uint8Array(encodedFrame.data, 0, frameInfo.unencryptedBytes);\n\n      // Frame trailer contains the R|IV_LENGTH and key index\n      const frameTrailer = new Uint8Array(2);\n\n      frameTrailer[0] = IV_LENGTH;\n      frameTrailer[1] = keyIndex;\n\n      // Construct frame trailer. Similar to the frame header described in\n      // https://tools.ietf.org/html/draft-omara-sframe-00#section-4.2\n      // but we put it at the end.\n      //\n      // ---------+-------------------------+-+---------+----\n      // payload  |IV...(length = IV_LENGTH)|R|IV_LENGTH|KID |\n      // ---------+-------------------------+-+---------+----\n      try {\n        const cipherText = await crypto.subtle.encrypt(\n          {\n            name: ENCRYPTION_ALGORITHM,\n            iv,\n            additionalData: new Uint8Array(encodedFrame.data, 0, frameHeader.byteLength),\n          },\n          encryptionKey,\n          new Uint8Array(encodedFrame.data, frameInfo.unencryptedBytes),\n        );\n\n        let newDataWithoutHeader = new Uint8Array(\n          cipherText.byteLength + iv.byteLength + frameTrailer.byteLength,\n        );\n        newDataWithoutHeader.set(new Uint8Array(cipherText)); // add ciphertext.\n        newDataWithoutHeader.set(new Uint8Array(iv), cipherText.byteLength); // append IV.\n        newDataWithoutHeader.set(frameTrailer, cipherText.byteLength + iv.byteLength); // append frame trailer.\n\n        if (frameInfo.requiresNALUProcessing) {\n          newDataWithoutHeader = writeRbsp(newDataWithoutHeader);\n        }\n\n        var newData = new Uint8Array(frameHeader.byteLength + newDataWithoutHeader.byteLength);\n        newData.set(frameHeader);\n        newData.set(newDataWithoutHeader, frameHeader.byteLength);\n\n        encodedFrame.data = newData.buffer;\n\n        return controller.enqueue(encodedFrame);\n      } catch (e: any) {\n        // TODO: surface this to the app.\n        workerLogger.error(e);\n      }\n    } else {\n      workerLogger.debug('failed to encrypt, emitting error', this.logContext);\n      this.emit(\n        CryptorEvent.Error,\n        new CryptorError(\n          `encryption key missing for encoding`,\n          CryptorErrorReason.MissingKey,\n          this.participantIdentity,\n        ),\n      );\n    }\n  }\n\n  /**\n   * Function that will be injected in a stream and will decrypt the given encoded frames.\n   *\n   * @param {RTCEncodedVideoFrame|RTCEncodedAudioFrame} encodedFrame - Encoded video frame.\n   * @param {TransformStreamDefaultController} controller - TransportStreamController.\n   */\n  protected async decodeFunction(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    controller: TransformStreamDefaultController,\n  ) {\n    if (\n      !this.isEnabled() ||\n      // skip for decryption for empty dtx frames\n      encodedFrame.data.byteLength === 0\n    ) {\n      return controller.enqueue(encodedFrame);\n    }\n\n    if (isFrameServerInjected(encodedFrame.data, this.sifTrailer)) {\n      encodedFrame.data = encodedFrame.data.slice(\n        0,\n        encodedFrame.data.byteLength - this.sifTrailer.byteLength,\n      );\n      if (await identifySifPayload(encodedFrame.data)) {\n        workerLogger.debug('enqueue SIF', this.logContext);\n        return controller.enqueue(encodedFrame);\n      } else {\n        workerLogger.warn('Unexpected SIF frame payload, dropping frame', this.logContext);\n        return;\n      }\n    }\n    const data = new Uint8Array(encodedFrame.data);\n    const keyIndex = data[encodedFrame.data.byteLength - 1];\n\n    if (this.keys.hasInvalidKeyAtIndex(keyIndex)) {\n      // drop frame\n      return;\n    }\n\n    if (this.keys.getKeySet(keyIndex)) {\n      try {\n        const decodedFrame = await this.decryptFrame(encodedFrame, keyIndex);\n        this.keys.decryptionSuccess(keyIndex);\n        if (decodedFrame) {\n          return controller.enqueue(decodedFrame);\n        }\n      } catch (error) {\n        if (error instanceof CryptorError && error.reason === CryptorErrorReason.InvalidKey) {\n          // emit an error if the key handler thinks we have a valid key\n          if (this.keys.hasValidKey) {\n            this.emit(CryptorEvent.Error, error);\n            this.keys.decryptionFailure(keyIndex);\n          }\n        } else {\n          workerLogger.warn('decoding frame failed', { error });\n        }\n      }\n    } else {\n      // emit an error if the key index is out of bounds but the key handler thinks we still have a valid key\n      workerLogger.warn(`skipping decryption due to missing key at index ${keyIndex}`);\n      this.emit(\n        CryptorEvent.Error,\n        new CryptorError(\n          `missing key at index ${keyIndex} for participant ${this.participantIdentity}`,\n          CryptorErrorReason.MissingKey,\n          this.participantIdentity,\n        ),\n      );\n      this.keys.decryptionFailure(keyIndex);\n    }\n  }\n\n  /**\n   * Function that will decrypt the given encoded frame. If the decryption fails, it will\n   * ratchet the key for up to RATCHET_WINDOW_SIZE times.\n   */\n  private async decryptFrame(\n    encodedFrame: RTCEncodedVideoFrame | RTCEncodedAudioFrame,\n    keyIndex: number,\n    initialMaterial: KeySet | undefined = undefined,\n    ratchetOpts: DecodeRatchetOptions = { ratchetCount: 0 },\n  ): Promise<RTCEncodedVideoFrame | RTCEncodedAudioFrame | undefined> {\n    const keySet = this.keys.getKeySet(keyIndex);\n    if (!ratchetOpts.encryptionKey && !keySet) {\n      throw new TypeError(`no encryption key found for decryption of ${this.participantIdentity}`);\n    }\n    let frameInfo = this.getUnencryptedBytes(encodedFrame);\n\n    // Construct frame trailer. Similar to the frame header described in\n    // https://tools.ietf.org/html/draft-omara-sframe-00#section-4.2\n    // but we put it at the end.\n    //\n    // ---------+-------------------------+-+---------+----\n    // payload  |IV...(length = IV_LENGTH)|R|IV_LENGTH|KID |\n    // ---------+-------------------------+-+---------+----\n\n    try {\n      const frameHeader = new Uint8Array(encodedFrame.data, 0, frameInfo.unencryptedBytes);\n      var encryptedData = new Uint8Array(\n        encodedFrame.data,\n        frameHeader.length,\n        encodedFrame.data.byteLength - frameHeader.length,\n      );\n      if (frameInfo.requiresNALUProcessing && needsRbspUnescaping(encryptedData)) {\n        encryptedData = parseRbsp(encryptedData);\n        const newUint8 = new Uint8Array(frameHeader.byteLength + encryptedData.byteLength);\n        newUint8.set(frameHeader);\n        newUint8.set(encryptedData, frameHeader.byteLength);\n        encodedFrame.data = newUint8.buffer;\n      }\n\n      const frameTrailer = new Uint8Array(encodedFrame.data, encodedFrame.data.byteLength - 2, 2);\n\n      const ivLength = frameTrailer[0];\n      const iv = new Uint8Array(\n        encodedFrame.data,\n        encodedFrame.data.byteLength - ivLength - frameTrailer.byteLength,\n        ivLength,\n      );\n\n      const cipherTextStart = frameHeader.byteLength;\n      const cipherTextLength =\n        encodedFrame.data.byteLength -\n        (frameHeader.byteLength + ivLength + frameTrailer.byteLength);\n\n      const plainText = await crypto.subtle.decrypt(\n        {\n          name: ENCRYPTION_ALGORITHM,\n          iv,\n          additionalData: new Uint8Array(encodedFrame.data, 0, frameHeader.byteLength),\n        },\n        ratchetOpts.encryptionKey ?? keySet!.encryptionKey,\n        new Uint8Array(encodedFrame.data, cipherTextStart, cipherTextLength),\n      );\n\n      const newData = new ArrayBuffer(frameHeader.byteLength + plainText.byteLength);\n      const newUint8 = new Uint8Array(newData);\n\n      newUint8.set(new Uint8Array(encodedFrame.data, 0, frameHeader.byteLength));\n      newUint8.set(new Uint8Array(plainText), frameHeader.byteLength);\n\n      encodedFrame.data = newData;\n\n      return encodedFrame;\n    } catch (error: any) {\n      if (this.keyProviderOptions.ratchetWindowSize > 0) {\n        if (ratchetOpts.ratchetCount < this.keyProviderOptions.ratchetWindowSize) {\n          workerLogger.debug(\n            `ratcheting key attempt ${ratchetOpts.ratchetCount} of ${\n              this.keyProviderOptions.ratchetWindowSize\n            }, for kind ${encodedFrame instanceof RTCEncodedAudioFrame ? 'audio' : 'video'}`,\n          );\n\n          let ratchetedKeySet: KeySet | undefined;\n          let ratchetResult: RatchetResult | undefined;\n          if ((initialMaterial ?? keySet) === this.keys.getKeySet(keyIndex)) {\n            // only ratchet if the currently set key is still the same as the one used to decrypt this frame\n            // if not, it might be that a different frame has already ratcheted and we try with that one first\n            ratchetResult = await this.keys.ratchetKey(keyIndex, false);\n\n            ratchetedKeySet = await deriveKeys(\n              ratchetResult.cryptoKey,\n              this.keyProviderOptions.ratchetSalt,\n            );\n          }\n\n          const frame = await this.decryptFrame(encodedFrame, keyIndex, initialMaterial || keySet, {\n            ratchetCount: ratchetOpts.ratchetCount + 1,\n            encryptionKey: ratchetedKeySet?.encryptionKey,\n          });\n          if (frame && ratchetedKeySet) {\n            // before updating the keys, make sure that the keySet used for this frame is still the same as the currently set key\n            // if it's not, a new key might have been set already, which we don't want to override\n            if ((initialMaterial ?? keySet) === this.keys.getKeySet(keyIndex)) {\n              this.keys.setKeySet(ratchetedKeySet, keyIndex, ratchetResult);\n              // decryption was successful, set the new key index to reflect the ratcheted key set\n              this.keys.setCurrentKeyIndex(keyIndex);\n            }\n          }\n          return frame;\n        } else {\n          /**\n           * Because we only set a new key once decryption has been successful,\n           * we can be sure that we don't need to reset the key to the initial material at this point\n           * as the key has not been updated on the keyHandler instance\n           */\n\n          workerLogger.warn('maximum ratchet attempts exceeded');\n          throw new CryptorError(\n            `valid key missing for participant ${this.participantIdentity}`,\n            CryptorErrorReason.InvalidKey,\n            this.participantIdentity,\n          );\n        }\n      } else {\n        throw new CryptorError(\n          `Decryption failed: ${error.message}`,\n          CryptorErrorReason.InvalidKey,\n          this.participantIdentity,\n        );\n      }\n    }\n  }\n\n  /**\n   * Construct the IV used for AES-GCM and sent (in plain) with the packet similar to\n   * https://tools.ietf.org/html/rfc7714#section-8.1\n   * It concatenates\n   * - the 32 bit synchronization source (SSRC) given on the encoded frame,\n   * - the 32 bit rtp timestamp given on the encoded frame,\n   * - a send counter that is specific to the SSRC. Starts at a random number.\n   * The send counter is essentially the pictureId but we currently have to implement this ourselves.\n   * There is no XOR with a salt. Note that this IV leaks the SSRC to the receiver but since this is\n   * randomly generated and SFUs may not rewrite this is considered acceptable.\n   * The SSRC is used to allow demultiplexing multiple streams with the same key, as described in\n   *   https://tools.ietf.org/html/rfc3711#section-4.1.1\n   * The RTP timestamp is 32 bits and advances by the codec clock rate (90khz for video, 48khz for\n   * opus audio) every second. For video it rolls over roughly every 13 hours.\n   * The send counter will advance at the frame rate (30fps for video, 50fps for 20ms opus audio)\n   * every second. It will take a long time to roll over.\n   *\n   * See also https://developer.mozilla.org/en-US/docs/Web/API/AesGcmParams\n   */\n  private makeIV(synchronizationSource: number, timestamp: number) {\n    const iv = new ArrayBuffer(IV_LENGTH);\n    const ivView = new DataView(iv);\n\n    // having to keep our own send count (similar to a picture id) is not ideal.\n    if (!this.sendCounts.has(synchronizationSource)) {\n      // Initialize with a random offset, similar to the RTP sequence number.\n      this.sendCounts.set(synchronizationSource, Math.floor(Math.random() * 0xffff));\n    }\n\n    const sendCount = this.sendCounts.get(synchronizationSource) ?? 0;\n\n    ivView.setUint32(0, synchronizationSource);\n    ivView.setUint32(4, timestamp);\n    ivView.setUint32(8, timestamp - (sendCount % 0xffff));\n\n    this.sendCounts.set(synchronizationSource, sendCount + 1);\n\n    return iv;\n  }\n\n  private getUnencryptedBytes(frame: RTCEncodedVideoFrame | RTCEncodedAudioFrame): {\n    unencryptedBytes: number;\n    requiresNALUProcessing: boolean;\n  } {\n    // Handle audio frames\n    if (!isVideoFrame(frame)) {\n      return { unencryptedBytes: UNENCRYPTED_BYTES.audio, requiresNALUProcessing: false };\n    }\n\n    // Detect and track codec changes\n    const detectedCodec = this.getVideoCodec(frame) ?? this.videoCodec;\n    if (detectedCodec !== this.detectedCodec) {\n      workerLogger.debug('detected different codec', {\n        detectedCodec,\n        oldCodec: this.detectedCodec,\n        ...this.logContext,\n      });\n      this.detectedCodec = detectedCodec;\n    }\n\n    // Check for unsupported codecs\n    if (detectedCodec === 'av1') {\n      throw new Error(`${detectedCodec} is not yet supported for end to end encryption`);\n    }\n\n    // Handle VP8/VP9 codecs (no NALU processing needed)\n    if (detectedCodec === 'vp8') {\n      return { unencryptedBytes: UNENCRYPTED_BYTES[frame.type], requiresNALUProcessing: false };\n    }\n    if (detectedCodec === 'vp9') {\n      return { unencryptedBytes: 0, requiresNALUProcessing: false };\n    }\n\n    // Try NALU processing for H.264/H.265 codecs\n    try {\n      const knownCodec =\n        detectedCodec === 'h264' || detectedCodec === 'h265' ? detectedCodec : undefined;\n      const naluResult = processNALUsForEncryption(new Uint8Array(frame.data), knownCodec);\n\n      if (naluResult.requiresNALUProcessing) {\n        return {\n          unencryptedBytes: naluResult.unencryptedBytes,\n          requiresNALUProcessing: true,\n        };\n      }\n    } catch (e) {\n      workerLogger.debug('NALU processing failed, falling back to VP8 handling', {\n        error: e,\n        ...this.logContext,\n      });\n    }\n\n    // Fallback to VP8 handling\n    return { unencryptedBytes: UNENCRYPTED_BYTES[frame.type], requiresNALUProcessing: false };\n  }\n\n  /**\n   * inspects frame payloadtype if available and maps it to the codec specified in rtpMap\n   */\n  private getVideoCodec(frame: RTCEncodedVideoFrame): VideoCodec | undefined {\n    if (this.rtpMap.size === 0) {\n      return undefined;\n    }\n    const payloadType = frame.getMetadata().payloadType;\n    const codec = payloadType ? this.rtpMap.get(payloadType) : undefined;\n    return codec;\n  }\n}\n\n/**\n * we use a magic frame trailer to detect whether a frame is injected\n * by the livekit server and thus to be treated as unencrypted\n * @internal\n */\nexport function isFrameServerInjected(frameData: ArrayBuffer, trailerBytes: Uint8Array): boolean {\n  if (trailerBytes.byteLength === 0) {\n    return false;\n  }\n  const frameTrailer = new Uint8Array(\n    frameData.slice(frameData.byteLength - trailerBytes.byteLength),\n  );\n  return trailerBytes.every((value, index) => value === frameTrailer[index]);\n}\n", "import { EventEmitter } from 'events';\nimport type TypedEventEmitter from 'typed-emitter';\nimport { workerLogger } from '../../logger';\nimport { KeyHandlerEvent, type ParticipantKeyHandlerCallbacks } from '../events';\nimport type { KeyProviderOptions, KeySet, RatchetResult } from '../types';\nimport { deriveKeys, importKey, ratchet } from '../utils';\n\n// TODO ParticipantKeyHandlers currently don't get destroyed on participant disconnect\n// we could do this by having a separate worker message on participant disconnected.\n\n/**\n * ParticipantKeyHandler is responsible for providing a cryptor instance with the\n * en-/decryption key of a participant. It assumes that all tracks of a specific participant\n * are encrypted with the same key.\n * Additionally it exposes a method to ratchet a key which can be used by the cryptor either automatically\n * if decryption fails or can be triggered manually on both sender and receiver side.\n *\n */\nexport class ParticipantKeyHandler extends (EventEmitter as new () => TypedEventEmitter<ParticipantKeyHandlerCallbacks>) {\n  private currentKeyIndex: number;\n\n  private cryptoKeyRing: Array<KeySet | undefined>;\n\n  private decryptionFailureCounts: Array<number>;\n\n  private keyProviderOptions: KeyProviderOptions;\n\n  private ratchetPromiseMap: Map<number, Promise<RatchetResult>>;\n\n  private participantIdentity: string;\n\n  /**\n   * true if the current key has not been marked as invalid\n   */\n  get hasValidKey(): boolean {\n    return !this.hasInvalidKeyAtIndex(this.currentKeyIndex);\n  }\n\n  constructor(participantIdentity: string, keyProviderOptions: KeyProviderOptions) {\n    super();\n    this.currentKeyIndex = 0;\n    if (keyProviderOptions.keyringSize < 1 || keyProviderOptions.keyringSize > 256) {\n      throw new TypeError('Keyring size needs to be between 1 and 256');\n    }\n    this.cryptoKeyRing = new Array(keyProviderOptions.keyringSize).fill(undefined);\n    this.decryptionFailureCounts = new Array(keyProviderOptions.keyringSize).fill(0);\n    this.keyProviderOptions = keyProviderOptions;\n    this.ratchetPromiseMap = new Map();\n    this.participantIdentity = participantIdentity;\n  }\n\n  /**\n   * Returns true if the key at the given index is marked as invalid.\n   *\n   * @param keyIndex the index of the key\n   */\n  hasInvalidKeyAtIndex(keyIndex: number): boolean {\n    return (\n      this.keyProviderOptions.failureTolerance >= 0 &&\n      this.decryptionFailureCounts[keyIndex] > this.keyProviderOptions.failureTolerance\n    );\n  }\n\n  /**\n   * Informs the key handler that a decryption failure occurred for an encryption key.\n   * @internal\n   * @param keyIndex the key index for which the failure occurred. Defaults to the current key index.\n   */\n  decryptionFailure(keyIndex: number = this.currentKeyIndex): void {\n    if (this.keyProviderOptions.failureTolerance < 0) {\n      return;\n    }\n\n    this.decryptionFailureCounts[keyIndex] += 1;\n\n    if (this.decryptionFailureCounts[keyIndex] > this.keyProviderOptions.failureTolerance) {\n      workerLogger.warn(\n        `key for ${this.participantIdentity} at index ${keyIndex} is being marked as invalid`,\n      );\n    }\n  }\n\n  /**\n   * Informs the key handler that a frame was successfully decrypted using an encryption key.\n   * @internal\n   * @param keyIndex the key index for which the success occurred. Defaults to the current key index.\n   */\n  decryptionSuccess(keyIndex: number = this.currentKeyIndex): void {\n    this.resetKeyStatus(keyIndex);\n  }\n\n  /**\n   * Call this after user initiated ratchet or a new key has been set in order to make sure to mark potentially\n   * invalid keys as valid again\n   *\n   * @param keyIndex the index of the key. Defaults to the current key index.\n   */\n  resetKeyStatus(keyIndex?: number): void {\n    if (keyIndex === undefined) {\n      this.decryptionFailureCounts.fill(0);\n    } else {\n      this.decryptionFailureCounts[keyIndex] = 0;\n    }\n  }\n\n  /**\n   * Ratchets the current key (or the one at keyIndex if provided) and\n   * returns the ratcheted material\n   * if `setKey` is true (default), it will also set the ratcheted key directly on the crypto key ring\n   * @param keyIndex\n   * @param setKey\n   */\n  ratchetKey(keyIndex?: number, setKey = true): Promise<RatchetResult> {\n    const currentKeyIndex = keyIndex ?? this.getCurrentKeyIndex();\n\n    const existingPromise = this.ratchetPromiseMap.get(currentKeyIndex);\n    if (typeof existingPromise !== 'undefined') {\n      return existingPromise;\n    }\n    const ratchetPromise = new Promise<RatchetResult>(async (resolve, reject) => {\n      try {\n        const keySet = this.getKeySet(currentKeyIndex);\n        if (!keySet) {\n          throw new TypeError(\n            `Cannot ratchet key without a valid keyset of participant ${this.participantIdentity}`,\n          );\n        }\n        const currentMaterial = keySet.material;\n        const chainKey = await ratchet(currentMaterial, this.keyProviderOptions.ratchetSalt);\n        const newMaterial = await importKey(chainKey, currentMaterial.algorithm.name, 'derive');\n        const ratchetResult: RatchetResult = {\n          chainKey,\n          cryptoKey: newMaterial,\n        };\n        if (setKey) {\n          // Set the new key and emit a ratchet event with the ratcheted chain key\n          await this.setKeyFromMaterial(newMaterial, currentKeyIndex, ratchetResult);\n        }\n        resolve(ratchetResult);\n      } catch (e) {\n        reject(e);\n      } finally {\n        this.ratchetPromiseMap.delete(currentKeyIndex);\n      }\n    });\n    this.ratchetPromiseMap.set(currentKeyIndex, ratchetPromise);\n    return ratchetPromise;\n  }\n\n  /**\n   * takes in a key material with `deriveBits` and `deriveKey` set as key usages\n   * and derives encryption keys from the material and sets it on the key ring buffer\n   * together with the material\n   * also resets the valid key property and updates the currentKeyIndex\n   */\n  async setKey(material: CryptoKey, keyIndex = 0) {\n    await this.setKeyFromMaterial(material, keyIndex);\n    this.resetKeyStatus(keyIndex);\n  }\n\n  /**\n   * takes in a key material with `deriveBits` and `deriveKey` set as key usages\n   * and derives encryption keys from the material and sets it on the key ring buffers\n   * together with the material\n   * also updates the currentKeyIndex\n   */\n  async setKeyFromMaterial(\n    material: CryptoKey,\n    keyIndex: number,\n    ratchetedResult: RatchetResult | null = null,\n  ) {\n    const keySet = await deriveKeys(material, this.keyProviderOptions.ratchetSalt);\n    const newIndex = keyIndex >= 0 ? keyIndex % this.cryptoKeyRing.length : this.currentKeyIndex;\n    workerLogger.debug(`setting new key with index ${keyIndex}`, {\n      usage: material.usages,\n      algorithm: material.algorithm,\n      ratchetSalt: this.keyProviderOptions.ratchetSalt,\n    });\n    this.setKeySet(keySet, newIndex, ratchetedResult);\n    if (newIndex >= 0) this.currentKeyIndex = newIndex;\n  }\n\n  setKeySet(keySet: KeySet, keyIndex: number, ratchetedResult: RatchetResult | null = null) {\n    this.cryptoKeyRing[keyIndex % this.cryptoKeyRing.length] = keySet;\n\n    if (ratchetedResult) {\n      this.emit(KeyHandlerEvent.KeyRatcheted, ratchetedResult, this.participantIdentity, keyIndex);\n    }\n  }\n\n  async setCurrentKeyIndex(index: number) {\n    this.currentKeyIndex = index % this.cryptoKeyRing.length;\n    this.resetKeyStatus(index);\n  }\n\n  getCurrentKeyIndex() {\n    return this.currentKeyIndex;\n  }\n\n  /**\n   * returns currently used KeySet or the one at `keyIndex` if provided\n   * @param keyIndex\n   * @returns\n   */\n  getKeySet(keyIndex?: number) {\n    return this.cryptoKeyRing[keyIndex ?? this.currentKeyIndex];\n  }\n}\n", "import { workerLogger } from '../../logger';\nimport type { VideoCodec } from '../../room/track/options';\nimport { AsyncQueue } from '../../utils/AsyncQueue';\nimport { KEY_PROVIDER_DEFAULTS } from '../constants';\nimport { CryptorErrorReason } from '../errors';\nimport { CryptorEvent, KeyHandlerEvent } from '../events';\nimport type {\n  E2EEWorkerMessage,\n  ErrorMessage,\n  InitAck,\n  KeyProviderOptions,\n  RatchetMessage,\n  RatchetRequestMessage,\n  RatchetResult,\n  ScriptTransformOptions,\n} from '../types';\nimport { FrameCryptor, encryptionEnabledMap } from './FrameCryptor';\nimport { ParticipantKeyHandler } from './ParticipantKeyHandler';\n\nconst participantCryptors: FrameCryptor[] = [];\nconst participantKeys: Map<string, ParticipantKeyHandler> = new Map();\nlet sharedKeyHandler: ParticipantKeyHandler | undefined;\nlet messageQueue = new AsyncQueue();\n\nlet isEncryptionEnabled: boolean = false;\n\nlet useSharedKey: boolean = false;\n\nlet sifTrailer: Uint8Array | undefined;\n\nlet keyProviderOptions: KeyProviderOptions = KEY_PROVIDER_DEFAULTS;\n\nlet rtpMap: Map<number, VideoCodec> = new Map();\n\nworkerLogger.setDefaultLevel('info');\n\nonmessage = (ev) => {\n  messageQueue.run(async () => {\n    const { kind, data }: E2EEWorkerMessage = ev.data;\n\n    switch (kind) {\n      case 'init':\n        workerLogger.setLevel(data.loglevel);\n        workerLogger.info('worker initialized');\n        keyProviderOptions = data.keyProviderOptions;\n        useSharedKey = !!data.keyProviderOptions.sharedKey;\n        // acknowledge init successful\n        const ackMsg: InitAck = {\n          kind: 'initAck',\n          data: { enabled: isEncryptionEnabled },\n        };\n        postMessage(ackMsg);\n        break;\n      case 'enable':\n        setEncryptionEnabled(data.enabled, data.participantIdentity);\n        workerLogger.info(\n          `updated e2ee enabled status for ${data.participantIdentity} to ${data.enabled}`,\n        );\n        // acknowledge enable call successful\n        postMessage(ev.data);\n        break;\n      case 'decode':\n        let cryptor = getTrackCryptor(data.participantIdentity, data.trackId);\n        cryptor.setupTransform(\n          kind,\n          data.readableStream,\n          data.writableStream,\n          data.trackId,\n          data.isReuse,\n          data.codec,\n        );\n        break;\n      case 'encode':\n        let pubCryptor = getTrackCryptor(data.participantIdentity, data.trackId);\n        pubCryptor.setupTransform(\n          kind,\n          data.readableStream,\n          data.writableStream,\n          data.trackId,\n          data.isReuse,\n          data.codec,\n        );\n        break;\n      case 'setKey':\n        if (useSharedKey) {\n          await setSharedKey(data.key, data.keyIndex);\n        } else if (data.participantIdentity) {\n          workerLogger.info(\n            `set participant sender key ${data.participantIdentity} index ${data.keyIndex}`,\n          );\n          await getParticipantKeyHandler(data.participantIdentity).setKey(data.key, data.keyIndex);\n        } else {\n          workerLogger.error('no participant Id was provided and shared key usage is disabled');\n        }\n        break;\n      case 'removeTransform':\n        unsetCryptorParticipant(data.trackId, data.participantIdentity);\n        break;\n      case 'updateCodec':\n        getTrackCryptor(data.participantIdentity, data.trackId).setVideoCodec(data.codec);\n        workerLogger.info('updated codec', {\n          participantIdentity: data.participantIdentity,\n          trackId: data.trackId,\n          codec: data.codec,\n        });\n        break;\n      case 'setRTPMap':\n        // this is only used for the local participant\n        rtpMap = data.map;\n        participantCryptors.forEach((cr) => {\n          if (cr.getParticipantIdentity() === data.participantIdentity) {\n            cr.setRtpMap(data.map);\n          }\n        });\n        break;\n      case 'ratchetRequest':\n        handleRatchetRequest(data);\n        break;\n      case 'setSifTrailer':\n        handleSifTrailer(data.trailer);\n        break;\n      default:\n        break;\n    }\n  });\n};\n\nasync function handleRatchetRequest(data: RatchetRequestMessage['data']) {\n  if (useSharedKey) {\n    const keyHandler = getSharedKeyHandler();\n    await keyHandler.ratchetKey(data.keyIndex);\n    keyHandler.resetKeyStatus();\n  } else if (data.participantIdentity) {\n    const keyHandler = getParticipantKeyHandler(data.participantIdentity);\n    await keyHandler.ratchetKey(data.keyIndex);\n    keyHandler.resetKeyStatus();\n  } else {\n    workerLogger.error(\n      'no participant Id was provided for ratchet request and shared key usage is disabled',\n    );\n  }\n}\n\nfunction getTrackCryptor(participantIdentity: string, trackId: string) {\n  let cryptors = participantCryptors.filter((c) => c.getTrackId() === trackId);\n  if (cryptors.length > 1) {\n    const debugInfo = cryptors\n      .map((c) => {\n        return { participant: c.getParticipantIdentity() };\n      })\n      .join(',');\n    workerLogger.error(\n      `Found multiple cryptors for the same trackID ${trackId}. target participant: ${participantIdentity} `,\n      { participants: debugInfo },\n    );\n  }\n  let cryptor = cryptors[0];\n  if (!cryptor) {\n    workerLogger.info('creating new cryptor for', { participantIdentity, trackId });\n    if (!keyProviderOptions) {\n      throw Error('Missing keyProvider options');\n    }\n    cryptor = new FrameCryptor({\n      participantIdentity,\n      keys: getParticipantKeyHandler(participantIdentity),\n      keyProviderOptions,\n      sifTrailer,\n    });\n    cryptor.setRtpMap(rtpMap);\n    setupCryptorErrorEvents(cryptor);\n    participantCryptors.push(cryptor);\n  } else if (participantIdentity !== cryptor.getParticipantIdentity()) {\n    // assign new participant id to track cryptor and pass in correct key handler\n    cryptor.setParticipant(participantIdentity, getParticipantKeyHandler(participantIdentity));\n  }\n\n  return cryptor;\n}\n\nfunction getParticipantKeyHandler(participantIdentity: string) {\n  if (useSharedKey) {\n    return getSharedKeyHandler();\n  }\n  let keys = participantKeys.get(participantIdentity);\n  if (!keys) {\n    keys = new ParticipantKeyHandler(participantIdentity, keyProviderOptions);\n    keys.on(KeyHandlerEvent.KeyRatcheted, emitRatchetedKeys);\n    participantKeys.set(participantIdentity, keys);\n  }\n  return keys;\n}\n\nfunction getSharedKeyHandler() {\n  if (!sharedKeyHandler) {\n    workerLogger.debug('creating new shared key handler');\n    sharedKeyHandler = new ParticipantKeyHandler('shared-key', keyProviderOptions);\n  }\n  return sharedKeyHandler;\n}\n\nfunction unsetCryptorParticipant(trackId: string, participantIdentity: string) {\n  const cryptors = participantCryptors.filter(\n    (c) => c.getParticipantIdentity() === participantIdentity && c.getTrackId() === trackId,\n  );\n  if (cryptors.length > 1) {\n    workerLogger.error('Found multiple cryptors for the same participant and trackID combination', {\n      trackId,\n      participantIdentity,\n    });\n  }\n  const cryptor = cryptors[0];\n  if (!cryptor) {\n    workerLogger.warn('Could not unset participant on cryptor', { trackId, participantIdentity });\n  } else {\n    cryptor.unsetParticipant();\n  }\n}\n\nfunction setEncryptionEnabled(enable: boolean, participantIdentity: string) {\n  workerLogger.debug(`setting encryption enabled for all tracks of ${participantIdentity}`, {\n    enable,\n  });\n  encryptionEnabledMap.set(participantIdentity, enable);\n}\n\nasync function setSharedKey(key: CryptoKey, index?: number) {\n  workerLogger.info('set shared key', { index });\n  await getSharedKeyHandler().setKey(key, index);\n}\n\nfunction setupCryptorErrorEvents(cryptor: FrameCryptor) {\n  cryptor.on(CryptorEvent.Error, (error) => {\n    const msg: ErrorMessage = {\n      kind: 'error',\n      data: { error: new Error(`${CryptorErrorReason[error.reason]}: ${error.message}`) },\n    };\n    postMessage(msg);\n  });\n}\n\nfunction emitRatchetedKeys(\n  ratchetResult: RatchetResult,\n  participantIdentity: string,\n  keyIndex?: number,\n) {\n  const msg: RatchetMessage = {\n    kind: `ratchetKey`,\n    data: {\n      participantIdentity,\n      keyIndex,\n      ratchetResult,\n    },\n  };\n  postMessage(msg);\n}\n\nfunction handleSifTrailer(trailer: Uint8Array) {\n  sifTrailer = trailer;\n  participantCryptors.forEach((c) => {\n    c.setSifTrailer(trailer);\n  });\n}\n\n// Operations using RTCRtpScriptTransform.\n// @ts-ignore\nif (self.RTCTransformEvent) {\n  workerLogger.debug('setup transform event');\n  // @ts-ignore\n  self.onrtctransform = (event: RTCTransformEvent) => {\n    // @ts-ignore\n    const transformer = event.transformer;\n    workerLogger.debug('transformer', transformer);\n\n    const { kind, participantIdentity, trackId, codec } =\n      transformer.options as ScriptTransformOptions;\n    const cryptor = getTrackCryptor(participantIdentity, trackId);\n    workerLogger.debug('transform', { codec });\n    cryptor.setupTransform(kind, transformer.readable, transformer.writable, trackId, false, codec);\n  };\n}\n"], "names": ["root", "definition", "module", "exports", "log", "this", "noop", "undefinedType", "isIE", "window", "navigator", "test", "userAgent", "logMethods", "_loggersByName", "defaultLogger", "bindMethod", "obj", "methodName", "method", "bind", "Function", "prototype", "call", "e", "apply", "arguments", "traceForIE", "console", "trace", "realMethod", "undefined", "replaceLoggingMethods", "level", "getLevel", "i", "length", "methodFactory", "name", "debug", "levels", "SILENT", "enableLoggingWhenConsoleArrives", "defaultMethodFactory", "_level", "_loggerName", "<PERSON><PERSON>", "factory", "self", "inheritedLevel", "defaultLevel", "userLevel", "storageKey", "persistLevelIfPossible", "levelNum", "levelName", "toUpperCase", "localStorage", "ignore", "document", "cookie", "encodeURIComponent", "getPersistedLevel", "storedLevel", "cookieName", "location", "indexOf", "exec", "slice", "clearPersistedLevel", "removeItem", "normalizeLevel", "input", "TypeError", "setLevel", "persist", "setDefaultLevel", "resetLevel", "enableAll", "TRACE", "disableAll", "rebuild", "<PERSON><PERSON><PERSON>", "initialLevel", "<PERSON><PERSON><PERSON><PERSON>", "logger", "_log", "noConflict", "getLoggers", "LogLevel", "LoggerNames", "livekitLogger", "Object", "values", "map", "info", "worker<PERSON>ogger", "_", "constructor", "o", "_locking", "Promise", "resolve", "_locks", "isLocked", "lock", "s", "t", "l", "unlockNext", "c", "then", "QueueTaskStatus", "AsyncQueue", "pendingTasks", "Map", "taskMutex", "Mutex", "nextTaskIndex", "run", "task", "taskInfo", "id", "enqueuedAt", "Date", "now", "status", "WAITING", "set", "unlock", "executedAt", "RUNNING", "COMPLETED", "delete", "flush", "__awaiter", "snapshot", "Array", "from", "ENCRYPTION_ALGORITHM", "DECRYPTION_FAILURE_TOLERANCE", "UNENCRYPTED_BYTES", "key", "delta", "audio", "empty", "IV_LENGTH", "SALT", "KEY_PROVIDER_DEFAULTS", "sharedKey", "ratchetSalt", "ratchetWindowSize", "failureTolerance", "keyringSize", "LivekitError", "Error", "code", "message", "ConnectionErrorReason", "DataStreamErrorReason", "MediaDeviceFailure", "getFailure", "error", "NotFound", "PermissionDenied", "DeviceInUse", "Other", "CryptorErrorReason", "CryptorError", "reason", "InternalError", "participantIdentity", "KeyProviderEvent", "KeyHandlerEvent", "EncryptionEvent", "CryptorEvent", "R", "Reflect", "ReflectApply", "target", "receiver", "args", "ReflectOwnKeys", "ownKeys", "getOwnPropertySymbols", "getOwnPropertyNames", "concat", "ProcessEmitWarning", "warning", "warn", "NumberIsNaN", "Number", "isNaN", "value", "EventEmitter", "init", "eventsModule", "once", "_events", "_eventsCount", "_maxListeners", "defaultMaxListeners", "checkListener", "listener", "defineProperty", "enumerable", "get", "arg", "RangeError", "getPrototypeOf", "create", "setMaxListeners", "n", "_getMaxListeners", "that", "getMaxListeners", "emit", "type", "push", "do<PERSON><PERSON><PERSON>", "events", "er", "err", "context", "handler", "len", "listeners", "arrayClone", "_addListener", "prepend", "m", "existing", "newListener", "unshift", "warned", "w", "String", "emitter", "count", "addListener", "on", "prependListener", "onceWrapper", "fired", "removeListener", "wrapFn", "_onceWrap", "state", "wrapped", "prependOnceListener", "list", "position", "originalListener", "shift", "spliceOne", "off", "removeAllListeners", "keys", "_listeners", "unwrap", "evlistener", "unwrapListeners", "rawListeners", "listenerCount", "eventNames", "arr", "copy", "index", "pop", "ret", "reject", "errorListener", "resolver", "eventTargetAgnosticAddListener", "addErrorHandlerIfEventEmitter", "flags", "addEventListener", "wrapListener", "removeEventListener", "isVideoFrame", "frame", "importKey", "keyBytes_1", "keyBytes", "algorithm", "usage", "crypto", "subtle", "getAlgoOptions", "algorithmName", "salt", "textEncoder", "TextEncoder", "encodedSalt", "encode", "hash", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iterations", "<PERSON><PERSON><PERSON><PERSON>", "material", "algorithmOptions", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ratchet", "deriveBits", "needsRbspUnescaping", "frameData", "parseRbsp", "stream", "dataOut", "Uint8Array", "kZerosInStartSequence", "kEmulationByte", "writeRbsp", "data_in", "numConsecutiveZeros", "byte", "kH264NaluTypeMask", "H264NALUType", "H265NALUType", "parseH264NALUType", "startByte", "parseH265NALUType", "firstByte", "isH264SliceNALU", "naluType", "SLICE_IDR", "SLICE_NON_IDR", "isH265SliceNALU", "TRAIL_N", "TRAIL_R", "TSA_N", "TSA_R", "STSA_N", "STSA_R", "RADL_N", "RADL_R", "RASL_N", "RASL_R", "BLA_W_LP", "BLA_W_RADL", "BLA_N_LP", "IDR_W_RADL", "IDR_N_LP", "CRA_NUT", "detectCodecFromNALUs", "data", "naluIndices", "naluIndex", "findSliceNALUUnencryptedBytes", "codec", "findNALUIndices", "result", "start", "pos", "searchLength", "end", "startCodeLength", "processNALUsForEncryption", "knownCodec", "detectedCodec", "unencryptedBytes", "requiresNALUProcessing", "cryptoHash", "hash<PERSON><PERSON><PERSON>", "digest", "hashArray", "b", "toString", "padStart", "join", "CryptoHashes", "VP8KeyFrame8x8", "H264KeyFrame2x2SPS", "H264KeyFrame2x2PPS", "H264KeyFrame2x2IDR", "OpusSilenceFrame", "identifySifPayload", "encryptionEnabledMap", "BaseFrameCryptor", "encodeFunction", "encodedFrame", "controller", "decodeFunction", "FrameCryptor", "opts", "isTransformActive", "sendCounts", "rtpMap", "keyProviderOptions", "sifTrailer", "_a", "logContext", "participant", "mediaTrackId", "trackId", "fallbackCodec", "videoCodec", "setParticipant", "assign", "unsetParticipant", "isEnabled", "getParticipantIdentity", "getTrackId", "setVideoCodec", "setRtpMap", "setupTransform", "operation", "readable", "writable", "isReuse", "passedTrackId", "transformFn", "transformStream", "TransformStream", "transform", "pipeThrough", "pipeTo", "catch", "finally", "setSifTrailer", "trailer", "byteLength", "enqueue", "keySet", "getKeySet", "getCurrentKeyIndex", "<PERSON><PERSON><PERSON>", "keyIndex", "iv", "makeIV", "getMetadata", "synchronizationSource", "timestamp", "frameInfo", "getUnencryptedBytes", "frameHeader", "frameTrailer", "cipherText", "encrypt", "additionalData", "newDataWithoutHeader", "newData", "buffer", "isFrameServerInjected", "hasInvalidKeyAtIndex", "decodedFrame", "decryptFrame", "decryptionSuccess", "Invalid<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decryptionFailure", "encodedFrame_1", "keyIndex_1", "_this", "initialMaterial", "ratchetOpts", "ratchetCount", "encryptedData", "newUint8", "iv<PERSON><PERSON><PERSON>", "cipherTextStart", "cipherTextLength", "plainText", "decrypt", "RTCEncodedAudioFrame", "ratchetedKeySet", "ratchetResult", "ratchetKey", "cryptoKey", "setKeySet", "setCurrentKeyIndex", "iv<PERSON><PERSON><PERSON>", "DataView", "has", "Math", "floor", "random", "sendCount", "setUint32", "getVideoCodec", "oldCodec", "naluResult", "size", "payloadType", "trailerBytes", "every", "ParticipantKeyHandler", "currentKeyIndex", "cryptoKeyRing", "fill", "decryptionFailureCounts", "ratchetPromiseMap", "resetKeyStatus", "<PERSON><PERSON><PERSON>", "existingPromise", "ratchetPromise", "currentMaterial", "chainKey", "newMaterial", "setKeyFromMaterial", "material_1", "_this2", "ratchetedResult", "newIndex", "usages", "KeyRatcheted", "participantCryptors", "participant<PERSON><PERSON><PERSON>", "shared<PERSON>eyHandler", "messageQueue", "isEncryptionEnabled", "useSharedKey", "onmessage", "ev", "kind", "loglevel", "ackMsg", "enabled", "postMessage", "setEncryptionEnabled", "cryptor", "getTrackCryptor", "readableStream", "writableStream", "pubCryptor", "setSharedKey", "getParticipantKeyHandler", "unsetCryptorParticipant", "for<PERSON>ach", "cr", "handleRatchetRequest", "handleSifTrailer", "<PERSON><PERSON><PERSON><PERSON>", "getSharedKeyHandler", "cryptors", "filter", "debugInfo", "participants", "setupCryptorErrorEvents", "emitRatchetedKeys", "enable", "msg", "RTCTransformEvent", "onrtctransform", "event", "transformer", "options"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMC,CAAA,UAAUA,IAAI,EAAEC,UAAU,EAAE;;MAIlB,IAAkCC,MAAM,CAACC,OAAO,EAAE;AACrDD,QAAAA,MAAA,CAAAC,OAAA,GAAiBF,UAAU,EAAE;AACrC,MAAA,CAAK,MAAM;AACHD,QAAAA,IAAI,CAACI,GAAG,GAAGH,UAAU,EAAE;AAC/B,MAAA;IACA,CAAC,EAACI,QAAI,EAAE,YAAY;;AAGpB;AACI,MAAA,IAAIC,IAAI,GAAG,YAAW,CAAA,CAAE;MACxB,IAAIC,aAAa,GAAG,WAAW;MAC/B,IAAIC,IAAI,GAAI,OAAOC,MAAM,KAAKF,aAAa,IAAM,OAAOE,MAAM,CAACC,SAAS,KAAKH,aAAc,IACvF,iBAAiB,CAACI,IAAI,CAACF,MAAM,CAACC,SAAS,CAACE,SAAS,CACpD;AAED,MAAA,IAAIC,UAAU,GAAG,CACb,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAA,CACH;MAED,IAAIC,cAAc,GAAG,EAAE;MACvB,IAAIC,aAAa,GAAG,IAAI;;AAE5B;AACI,MAAA,SAASC,UAAUA,CAACC,GAAG,EAAEC,UAAU,EAAE;AACjC,QAAA,IAAIC,MAAM,GAAGF,GAAG,CAACC,UAAU,CAAC;AAC5B,QAAA,IAAI,OAAOC,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;AACnC,UAAA,OAAOD,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC;AACnC,QAAA,CAAS,MAAM;UACH,IAAI;YACA,OAAOI,QAAQ,CAACC,SAAS,CAACF,IAAI,CAACG,IAAI,CAACJ,MAAM,EAAEF,GAAG,CAAC;UAChE,CAAa,CAAC,OAAOO,CAAC,EAAE;AACxB;AACgB,YAAA,OAAO,YAAW;AACd,cAAA,OAAOH,QAAQ,CAACC,SAAS,CAACG,KAAK,CAACA,KAAK,CAACN,MAAM,EAAE,CAACF,GAAG,EAAES,SAAS,CAAC,CAAC;YACnF,CAAiB;AACjB,UAAA;AACA,QAAA;AACA,MAAA;;AAEA;MACI,SAASC,UAAUA,GAAG;QAClB,IAAIC,OAAO,CAACxB,GAAG,EAAE;AACb,UAAA,IAAIwB,OAAO,CAACxB,GAAG,CAACqB,KAAK,EAAE;YACnBG,OAAO,CAACxB,GAAG,CAACqB,KAAK,CAACG,OAAO,EAAEF,SAAS,CAAC;AACrD,UAAA,CAAa,MAAM;AACnB;AACgBL,YAAAA,QAAQ,CAACC,SAAS,CAACG,KAAK,CAACA,KAAK,CAACG,OAAO,CAACxB,GAAG,EAAE,CAACwB,OAAO,EAAEF,SAAS,CAAC,CAAC;AACjF,UAAA;AACA,QAAA;QACQ,IAAIE,OAAO,CAACC,KAAK,EAAED,OAAO,CAACC,KAAK,EAAE;AAC1C,MAAA;;AAEA;AACA;MACI,SAASC,UAAUA,CAACZ,UAAU,EAAE;QAC5B,IAAIA,UAAU,KAAK,OAAO,EAAE;AACxBA,UAAAA,UAAU,GAAG,KAAK;AAC9B,QAAA;AAEQ,QAAA,IAAI,OAAOU,OAAO,KAAKrB,aAAa,EAAE;UAClC,OAAO,KAAK,CAAC;AACzB,QAAA,CAAS,MAAM,IAAIW,UAAU,KAAK,OAAO,IAAIV,IAAI,EAAE;AACvC,UAAA,OAAOmB,UAAU;QAC7B,CAAS,MAAM,IAAIC,OAAO,CAACV,UAAU,CAAC,KAAKa,SAAS,EAAE;AAC1C,UAAA,OAAOf,UAAU,CAACY,OAAO,EAAEV,UAAU,CAAC;AAClD,QAAA,CAAS,MAAM,IAAIU,OAAO,CAACxB,GAAG,KAAK2B,SAAS,EAAE;AAClC,UAAA,OAAOf,UAAU,CAACY,OAAO,EAAE,KAAK,CAAC;AAC7C,QAAA,CAAS,MAAM;AACH,UAAA,OAAOtB,IAAI;AACvB,QAAA;AACA,MAAA;;AAEA;;MAEI,SAAS0B,qBAAqBA,GAAG;AACrC;AACQ,QAAA,IAAIC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;;AAEnC;AACQ,QAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,UAAU,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;AACxC,UAAA,IAAIjB,UAAU,GAAGL,UAAU,CAACsB,CAAC,CAAC;UAC9B,IAAI,CAACjB,UAAU,CAAC,GAAIiB,CAAC,GAAGF,KAAK,GACzB3B,IAAI,GACJ,IAAI,CAAC+B,aAAa,CAACnB,UAAU,EAAEe,KAAK,EAAE,IAAI,CAACK,IAAI,CAAC;AAChE,QAAA;;AAEA;AACQ,QAAA,IAAI,CAAClC,GAAG,GAAG,IAAI,CAACmC,KAAK;;AAE7B;AACQ,QAAA,IAAI,OAAOX,OAAO,KAAKrB,aAAa,IAAI0B,KAAK,GAAG,IAAI,CAACO,MAAM,CAACC,MAAM,EAAE;AAChE,UAAA,OAAO,kCAAkC;AACrD,QAAA;AACA,MAAA;;AAEA;AACA;MACI,SAASC,+BAA+BA,CAACxB,UAAU,EAAE;AACjD,QAAA,OAAO,YAAY;AACf,UAAA,IAAI,OAAOU,OAAO,KAAKrB,aAAa,EAAE;AAClCyB,YAAAA,qBAAqB,CAACT,IAAI,CAAC,IAAI,CAAC;YAChC,IAAI,CAACL,UAAU,CAAC,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AACvD,UAAA;QACA,CAAS;AACT,MAAA;;AAEA;AACA;AACI,MAAA,SAASiB,oBAAoBA,CAACzB,UAAU,EAAE0B,MAAM,EAAEC,WAAW,EAAE;AACnE;AACQ,QAAA,OAAOf,UAAU,CAACZ,UAAU,CAAC,IACtBwB,+BAA+B,CAACjB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AACrE,MAAA;AAEI,MAAA,SAASoB,MAAMA,CAACR,IAAI,EAAES,OAAO,EAAE;AACnC;QACM,IAAIC,IAAI,GAAG,IAAI;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACM,QAAA,IAAIC,cAAc;AACxB;AACA;AACA;AACA;AACA;AACM,QAAA,IAAIC,YAAY;AACtB;AACA;AACA;AACA;AACA;AACM,QAAA,IAAIC,SAAS;QAEb,IAAIC,UAAU,GAAG,UAAU;AAC3B,QAAA,IAAI,OAAOd,IAAI,KAAK,QAAQ,EAAE;UAC5Bc,UAAU,IAAI,GAAG,GAAGd,IAAI;AAChC,QAAA,CAAO,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;AACnCc,UAAAA,UAAU,GAAGrB,SAAS;AAC9B,QAAA;QAEM,SAASsB,sBAAsBA,CAACC,QAAQ,EAAE;AACtC,UAAA,IAAIC,SAAS,GAAG,CAAC1C,UAAU,CAACyC,QAAQ,CAAC,IAAI,QAAQ,EAAEE,WAAW,EAAE;AAEhE,UAAA,IAAI,OAAO/C,MAAM,KAAKF,aAAa,IAAI,CAAC6C,UAAU,EAAE;;AAE9D;UACU,IAAI;AACA3C,YAAAA,MAAM,CAACgD,YAAY,CAACL,UAAU,CAAC,GAAGG,SAAS;AAC3C,YAAA;AACd,UAAA,CAAW,CAAC,OAAOG,MAAM,EAAE,CAAA;;AAE3B;UACU,IAAI;AACAjD,YAAAA,MAAM,CAACkD,QAAQ,CAACC,MAAM,GACpBC,kBAAkB,CAACT,UAAU,CAAC,GAAG,GAAG,GAAGG,SAAS,GAAG,GAAG;AACtE,UAAA,CAAW,CAAC,OAAOG,MAAM,EAAE,CAAA;AAC3B,QAAA;QAEM,SAASI,iBAAiBA,GAAG;AACzB,UAAA,IAAIC,WAAW;AAEf,UAAA,IAAI,OAAOtD,MAAM,KAAKF,aAAa,IAAI,CAAC6C,UAAU,EAAE;UAEpD,IAAI;AACAW,YAAAA,WAAW,GAAGtD,MAAM,CAACgD,YAAY,CAACL,UAAU,CAAC;AAC3D,UAAA,CAAW,CAAC,OAAOM,MAAM,EAAE,CAAA;;AAE3B;AACU,UAAA,IAAI,OAAOK,WAAW,KAAKxD,aAAa,EAAE;YACtC,IAAI;AACA,cAAA,IAAIqD,MAAM,GAAGnD,MAAM,CAACkD,QAAQ,CAACC,MAAM;AACnC,cAAA,IAAII,UAAU,GAAGH,kBAAkB,CAACT,UAAU,CAAC;cAC/C,IAAIa,QAAQ,GAAGL,MAAM,CAACM,OAAO,CAACF,UAAU,GAAG,GAAG,CAAC;AAC/C,cAAA,IAAIC,QAAQ,KAAK,CAAC,CAAC,EAAE;gBACjBF,WAAW,GAAG,UAAU,CAACI,IAAI,CACzBP,MAAM,CAACQ,KAAK,CAACH,QAAQ,GAAGD,UAAU,CAAC5B,MAAM,GAAG,CAAC,CACvE,CAAuB,CAAC,CAAC,CAAC;AAC1B,cAAA;AACA,YAAA,CAAe,CAAC,OAAOsB,MAAM,EAAE,CAAA;AAC/B,UAAA;;AAEA;UACU,IAAIV,IAAI,CAACR,MAAM,CAACuB,WAAW,CAAC,KAAKhC,SAAS,EAAE;AACxCgC,YAAAA,WAAW,GAAGhC,SAAS;AACrC,UAAA;AAEU,UAAA,OAAOgC,WAAW;AAC5B,QAAA;QAEM,SAASM,mBAAmBA,GAAG;AAC3B,UAAA,IAAI,OAAO5D,MAAM,KAAKF,aAAa,IAAI,CAAC6C,UAAU,EAAE;;AAE9D;UACU,IAAI;AACA3C,YAAAA,MAAM,CAACgD,YAAY,CAACa,UAAU,CAAClB,UAAU,CAAC;AACxD,UAAA,CAAW,CAAC,OAAOM,MAAM,EAAE,CAAA;;AAE3B;UACU,IAAI;YACAjD,MAAM,CAACkD,QAAQ,CAACC,MAAM,GACpBC,kBAAkB,CAACT,UAAU,CAAC,GAAG,0CAA0C;AAC3F,UAAA,CAAW,CAAC,OAAOM,MAAM,EAAE,CAAA;AAC3B,QAAA;QAEM,SAASa,cAAcA,CAACC,KAAK,EAAE;UAC3B,IAAIvC,KAAK,GAAGuC,KAAK;AACjB,UAAA,IAAI,OAAOvC,KAAK,KAAK,QAAQ,IAAIe,IAAI,CAACR,MAAM,CAACP,KAAK,CAACuB,WAAW,EAAE,CAAC,KAAKzB,SAAS,EAAE;YAC7EE,KAAK,GAAGe,IAAI,CAACR,MAAM,CAACP,KAAK,CAACuB,WAAW,EAAE,CAAC;AACtD,UAAA;AACU,UAAA,IAAI,OAAOvB,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAIe,IAAI,CAACR,MAAM,CAACC,MAAM,EAAE;AACxE,YAAA,OAAOR,KAAK;AAC1B,UAAA,CAAW,MAAM;AACH,YAAA,MAAM,IAAIwC,SAAS,CAAC,4CAA4C,GAAGD,KAAK,CAAC;AACvF,UAAA;AACA,QAAA;;AAEA;AACA;AACA;AACA;AACA;;QAEMxB,IAAI,CAACV,IAAI,GAAGA,IAAI;QAEhBU,IAAI,CAACR,MAAM,GAAG;AAAE,UAAA,OAAO,EAAE,CAAC;AAAE,UAAA,OAAO,EAAE,CAAC;AAAE,UAAA,MAAM,EAAE,CAAC;AAAE,UAAA,MAAM,EAAE,CAAC;AACxD,UAAA,OAAO,EAAE,CAAC;AAAE,UAAA,QAAQ,EAAE;SAAE;AAE5BQ,QAAAA,IAAI,CAACX,aAAa,GAAGU,OAAO,IAAIJ,oBAAoB;QAEpDK,IAAI,CAACd,QAAQ,GAAG,YAAY;UACxB,IAAIiB,SAAS,IAAI,IAAI,EAAE;AACrB,YAAA,OAAOA,SAAS;AAC5B,UAAA,CAAW,MAAM,IAAID,YAAY,IAAI,IAAI,EAAE;AAC/B,YAAA,OAAOA,YAAY;AAC/B,UAAA,CAAW,MAAM;AACL,YAAA,OAAOD,cAAc;AACjC,UAAA;QACA,CAAO;AAEDD,QAAAA,IAAI,CAAC0B,QAAQ,GAAG,UAAUzC,KAAK,EAAE0C,OAAO,EAAE;AACtCxB,UAAAA,SAAS,GAAGoB,cAAc,CAACtC,KAAK,CAAC;UACjC,IAAI0C,OAAO,KAAK,KAAK,EAAE;AAAA;YACnBtB,sBAAsB,CAACF,SAAS,CAAC;AAC/C,UAAA;;AAEA;AACU,UAAA,OAAOnB,qBAAqB,CAACT,IAAI,CAACyB,IAAI,CAAC;QACjD,CAAO;AAEDA,QAAAA,IAAI,CAAC4B,eAAe,GAAG,UAAU3C,KAAK,EAAE;AACpCiB,UAAAA,YAAY,GAAGqB,cAAc,CAACtC,KAAK,CAAC;AACpC,UAAA,IAAI,CAAC6B,iBAAiB,EAAE,EAAE;AACtBd,YAAAA,IAAI,CAAC0B,QAAQ,CAACzC,KAAK,EAAE,KAAK,CAAC;AACzC,UAAA;QACA,CAAO;QAEDe,IAAI,CAAC6B,UAAU,GAAG,YAAY;AAC1B1B,UAAAA,SAAS,GAAG,IAAI;AAChBkB,UAAAA,mBAAmB,EAAE;AACrBrC,UAAAA,qBAAqB,CAACT,IAAI,CAACyB,IAAI,CAAC;QAC1C,CAAO;AAEDA,QAAAA,IAAI,CAAC8B,SAAS,GAAG,UAASH,OAAO,EAAE;UAC/B3B,IAAI,CAAC0B,QAAQ,CAAC1B,IAAI,CAACR,MAAM,CAACuC,KAAK,EAAEJ,OAAO,CAAC;QACnD,CAAO;AAED3B,QAAAA,IAAI,CAACgC,UAAU,GAAG,UAASL,OAAO,EAAE;UAChC3B,IAAI,CAAC0B,QAAQ,CAAC1B,IAAI,CAACR,MAAM,CAACC,MAAM,EAAEkC,OAAO,CAAC;QACpD,CAAO;QAED3B,IAAI,CAACiC,OAAO,GAAG,YAAY;UACvB,IAAIlE,aAAa,KAAKiC,IAAI,EAAE;YACxBC,cAAc,GAAGsB,cAAc,CAACxD,aAAa,CAACmB,QAAQ,EAAE,CAAC;AACvE,UAAA;AACUF,UAAAA,qBAAqB,CAACT,IAAI,CAACyB,IAAI,CAAC;UAEhC,IAAIjC,aAAa,KAAKiC,IAAI,EAAE;AACxB,YAAA,KAAK,IAAIkC,SAAS,IAAIpE,cAAc,EAAE;AACpCA,cAAAA,cAAc,CAACoE,SAAS,CAAC,CAACD,OAAO,EAAE;AACnD,YAAA;AACA,UAAA;QACA,CAAO;;AAEP;AACMhC,QAAAA,cAAc,GAAGsB,cAAc,CAC3BxD,aAAa,GAAGA,aAAa,CAACmB,QAAQ,EAAE,GAAG,MACrD,CAAO;AACD,QAAA,IAAIiD,YAAY,GAAGrB,iBAAiB,EAAE;QACtC,IAAIqB,YAAY,IAAI,IAAI,EAAE;AACtBhC,UAAAA,SAAS,GAAGoB,cAAc,CAACY,YAAY,CAAC;AAClD,QAAA;AACMnD,QAAAA,qBAAqB,CAACT,IAAI,CAACyB,IAAI,CAAC;AACtC,MAAA;;AAEA;AACA;AACA;AACA;AACA;;AAEIjC,MAAAA,aAAa,GAAG,IAAI+B,MAAM,EAAE;AAE5B/B,MAAAA,aAAa,CAACqE,SAAS,GAAG,SAASA,SAASA,CAAC9C,IAAI,EAAE;AAC/C,QAAA,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAKA,IAAI,KAAK,EAAE,EAAE;AACvE,UAAA,MAAM,IAAImC,SAAS,CAAC,gDAAgD,CAAC;AACjF,QAAA;AAEQ,QAAA,IAAIY,MAAM,GAAGvE,cAAc,CAACwB,IAAI,CAAC;QACjC,IAAI,CAAC+C,MAAM,EAAE;AACTA,UAAAA,MAAM,GAAGvE,cAAc,CAACwB,IAAI,CAAC,GAAG,IAAIQ,MAAM,CACtCR,IAAI,EACJvB,aAAa,CAACsB,aAC9B,CAAa;AACb,QAAA;AACQ,QAAA,OAAOgD,MAAM;MACrB,CAAK;;AAEL;MACI,IAAIC,IAAI,GAAI,OAAO7E,MAAM,KAAKF,aAAa,GAAIE,MAAM,CAACL,GAAG,GAAG2B,SAAS;MACrEhB,aAAa,CAACwE,UAAU,GAAG,YAAW;QAClC,IAAI,OAAO9E,MAAM,KAAKF,aAAa,IAC5BE,MAAM,CAACL,GAAG,KAAKW,aAAa,EAAE;UACjCN,MAAM,CAACL,GAAG,GAAGkF,IAAI;AAC7B,QAAA;AAEQ,QAAA,OAAOvE,aAAa;MAC5B,CAAK;AAEDA,MAAAA,aAAa,CAACyE,UAAU,GAAG,SAASA,UAAUA,GAAG;AAC7C,QAAA,OAAO1E,cAAc;MAC7B,CAAK;;AAEL;AACIC,MAAAA,aAAa,CAAC,SAAS,CAAC,GAAGA,aAAa;AAExC,MAAA,OAAOA,aAAa;AACxB,IAAA,CAAC,CAAC;;;;;;;AClWF,IAAY0E,QAOX;AAPD,CAAA,UAAYA,QAAQ,EAAA;EAClBA,QAAA,CAAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;EACTA,QAAA,CAAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;EACTA,QAAA,CAAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;EACRA,QAAA,CAAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;EACRA,QAAA,CAAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;EACTA,QAAA,CAAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACZ,CAAC,EAPWA,QAAQ,KAARA,QAAQ,GAAA,EAAA,CAAA,CAAA;AASpB,IAAYC,WAWX;AAXD,CAAA,UAAYA,WAAW,EAAA;AACrBA,EAAAA,WAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnBA,EAAAA,WAAA,CAAA,MAAA,CAAA,GAAA,cAAqB;AACrBA,EAAAA,WAAA,CAAA,aAAA,CAAA,GAAA,qBAAmC;AACnCA,EAAAA,WAAA,CAAA,OAAA,CAAA,GAAA,eAAuB;AACvBA,EAAAA,WAAA,CAAA,aAAA,CAAA,GAAA,2BAAyC;AACzCA,EAAAA,WAAA,CAAA,QAAA,CAAA,GAAA,gBAAyB;AACzBA,EAAAA,WAAA,CAAA,QAAA,CAAA,GAAA,gBAAyB;AACzBA,EAAAA,WAAA,CAAA,WAAA,CAAA,GAAA,oBAAgC;AAChCA,EAAAA,WAAA,CAAA,aAAA,CAAA,GAAA,sBAAoC;AACpCA,EAAAA,WAAA,CAAA,MAAA,CAAA,GAAA,SAAgB;AAClB,CAAC,EAXWA,WAAW,KAAXA,WAAW,GAAA,EAAA,CAAA,CAAA;AA0BvB,IAAIC,aAAa,GAAGvF,yBAAa,CAAC,SAAS,CAAC;AACrBwF,MAAM,CAACC,MAAM,CAACH,WAAW,CAAC,CAACI,GAAG,CAAExD,IAAI,IAAKlC,yBAAa,CAACkC,IAAI,CAAC;AAEnFqD,aAAa,CAACf,eAAe,CAACa,QAAQ,CAACM,IAAI,CAAC;AAqDrC,MAAMC,YAAY,GAAG5F,yBAAa,CAAC,SAAS,CAAqB;;;;;;;;;;AC7FjE,MAAM6F,CAAA,CAAM;AAKjBC,EAAAA,WAAAA,GAAc;IAJNC,CAAA,CAAA,IAAA,EAAA,UAAA,CAAA;IAEAA,CAAA,CAAA,IAAA,EAAA,QAAA,CAAA;IAGD,IAAA,CAAAC,QAAA,GAAWC,OAAA,CAAQC,OAAA,EAAA,EACxB,IAAA,CAAKC,MAAA,GAAS,CAAA;AAChB,EAAA;AAEAC,EAAAA,QAAAA,GAAW;IACT,OAAO,IAAA,CAAKD,MAAA,GAAS,CAAA;AACvB,EAAA;AAEAE,EAAAA,IAAAA,GAAO;AACL,IAAA,IAAA,CAAKF,MAAA,IAAU,CAAA;AAEX,IAAA,IAAAG,CAAA;IAEJ,MAAMC,CAAA,GAAW,IAAIN,OAAA,CAClBO,CAAA,IACEF,CAAA,GAAaG,MAAM;QAClB,IAAA,CAAKN,MAAA,IAAU,CAAA,EACPK,CAAA,EAAA;AACV,MAAA,CAAA,CAAA;AAGEE,MAAAA,CAAA,GAAa,IAAA,CAAKV,QAAA,CAASW,IAAA,CAAK,MAAML,CAAU,CAAA;IAEtD,OAAA,IAAA,CAAKN,QAAA,GAAW,IAAA,CAAKA,QAAA,CAASW,IAAA,CAAK,MAAMJ,CAAQ,CAAA,EAE1CG,CAAA;AACT,EAAA;AACF;;AC7BA,IAAKE,eAIJ;AAJD,CAAA,UAAKA,eAAe,EAAA;EAClBA,eAAA,CAAAA,eAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAS;EACTA,eAAA,CAAAA,eAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAS;EACTA,eAAA,CAAAA,eAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAW;AACb,CAAC,EAJIA,eAAe,KAAfA,eAAe,GAAA,EAAA,CAAA,CAAA;MAaPC,UAAU,CAAA;AAOrBf,EAAAA,WAAAA,GAAA;AACE,IAAA,IAAI,CAACgB,YAAY,GAAG,IAAIC,GAAG,EAAE;AAC7B,IAAA,IAAI,CAACC,SAAS,GAAG,IAAIC,CAAK,EAAE;IAC5B,IAAI,CAACC,aAAa,GAAG,CAAC;AACxB,EAAA;EAEMC,GAAGA,CAAIC,IAAkB,EAAA;;AAC7B,MAAA,MAAMC,QAAQ,GAAkB;AAC9BC,QAAAA,EAAE,EAAE,IAAI,CAACJ,aAAa,EAAE;AACxBK,QAAAA,UAAU,EAAEC,IAAI,CAACC,GAAG,EAAE;QACtBC,MAAM,EAAEd,eAAe,CAACe;OACzB;MACD,IAAI,CAACb,YAAY,CAACc,GAAG,CAACP,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAAC;MAC5C,MAAMQ,MAAM,GAAG,MAAM,IAAI,CAACb,SAAS,CAACX,IAAI,EAAE;MAC1C,IAAI;AACFgB,QAAAA,QAAQ,CAACS,UAAU,GAAGN,IAAI,CAACC,GAAG,EAAE;AAChCJ,QAAAA,QAAQ,CAACK,MAAM,GAAGd,eAAe,CAACmB,OAAO;QACzC,OAAO,MAAMX,IAAI,EAAE;AACrB,MAAA,CAAC,SAAS;AACRC,QAAAA,QAAQ,CAACK,MAAM,GAAGd,eAAe,CAACoB,SAAS;QAC3C,IAAI,CAAClB,YAAY,CAACmB,MAAM,CAACZ,QAAQ,CAACC,EAAE,CAAC;AACrCO,QAAAA,MAAM,EAAE;AACV,MAAA;AACF,IAAA,CAAC,CAAA;AAAA,EAAA;AAEKK,EAAAA,KAAKA,GAAA;;AACT,MAAA,OAAO,IAAI,CAACf,GAAG,CAAC,MAAWgB,SAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,aAAA,CAAE,CAAC,CAAA,CAAC;AACjC,IAAA,CAAC,CAAA;AAAA,EAAA;AAEDC,EAAAA,QAAQA,GAAA;IACN,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxB,YAAY,CAACrB,MAAM,EAAE,CAAC;AAC/C,EAAA;AACD;;ACtDM,MAAM8C,oBAAoB,GAAG,SAAS;AAE7C;AACO,MAAMC,4BAA4B,GAAG,EAAE;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,iBAAiB,GAAG;AAC/BC,EAAAA,GAAG,EAAE,EAAE;AACPC,EAAAA,KAAK,EAAE,CAAC;AACRC,EAAAA,KAAK,EAAE,CAAC;AAAE;AACVC,EAAAA,KAAK,EAAE;CACC;AAEV;AACgG;AACzF,MAAMC,SAAS,GAAG,EAAE;AAKpB,MAAMC,IAAI,GAAG,sBAAsB;AAEnC,MAAMC,qBAAqB,GAAuB;AACvDC,EAAAA,SAAS,EAAE,KAAK;AAChBC,EAAAA,WAAW,EAAEH,IAAI;AACjBI,EAAAA,iBAAiB,EAAE,CAAC;AACpBC,EAAAA,gBAAgB,EAAEZ,4BAA4B;AAC9Ca,EAAAA,WAAW,EAAE;CACL;;ACrCJ,MAAOC,YAAa,SAAQC,KAAK,CAAA;AAGrCzD,EAAAA,WAAAA,CAAY0D,IAAY,EAAEC,OAAgB,EAAA;AACxC,IAAA,KAAK,CAACA,OAAO,IAAI,sBAAsB,CAAC;IACxC,IAAI,CAACvH,IAAI,GAAG,cAAc;IAC1B,IAAI,CAACsH,IAAI,GAAGA,IAAI;AAClB,EAAA;AACD;AAED,IAAYE,qBAOX;AAPD,CAAA,UAAYA,qBAAqB,EAAA;EAC/BA,qBAAA,CAAAA,qBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;EACVA,qBAAA,CAAAA,qBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAiB;EACjBA,qBAAA,CAAAA,qBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAa;EACbA,qBAAA,CAAAA,qBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;EACTA,qBAAA,CAAAA,qBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY;EACZA,qBAAA,CAAAA,qBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACT,CAAC,EAPWA,qBAAqB,KAArBA,qBAAqB,GAAA,EAAA,CAAA,CAAA;AAqGjC;AACA,IAAYC,qBAkBX;AAlBD,CAAA,UAAYA,qBAAqB,EAAA;AAC/B;EACAA,qBAAA,CAAAA,qBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AAEjB;EACAA,qBAAA,CAAAA,qBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAe;AAEf;EACAA,qBAAA,CAAAA,qBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB;AAEhB;EACAA,qBAAA,CAAAA,qBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAkB;AAElB;EACAA,qBAAA,CAAAA,qBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc;AAEd;EACAA,qBAAA,CAAAA,qBAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,0BAA4B;AAC9B,CAAC,EAlBWA,qBAAqB,KAArBA,qBAAqB,GAAA,EAAA,CAAA,CAAA;AAiCjC,IAAYC,kBAQX;AARD,CAAA,UAAYA,kBAAkB,EAAA;AAC5B;AACAA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC;AACrC;AACAA,EAAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACrB;AACAA,EAAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B;AAC3BA,EAAAA,kBAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACjB,CAAC,EARWA,kBAAkB,KAAlBA,kBAAkB,GAAA,EAAA,CAAA,CAAA;AAU9B,CAAA,UAAiBA,kBAAkB,EAAA;EACjC,SAAgBC,UAAUA,CAACC,KAAU,EAAA;AACnC,IAAA,IAAIA,KAAK,IAAI,MAAM,IAAIA,KAAK,EAAE;MAC5B,IAAIA,KAAK,CAAC5H,IAAI,KAAK,eAAe,IAAI4H,KAAK,CAAC5H,IAAI,KAAK,sBAAsB,EAAE;QAC3E,OAAO0H,kBAAkB,CAACG,QAAQ;AACpC,MAAA;MACA,IAAID,KAAK,CAAC5H,IAAI,KAAK,iBAAiB,IAAI4H,KAAK,CAAC5H,IAAI,KAAK,uBAAuB,EAAE;QAC9E,OAAO0H,kBAAkB,CAACI,gBAAgB;AAC5C,MAAA;MACA,IAAIF,KAAK,CAAC5H,IAAI,KAAK,kBAAkB,IAAI4H,KAAK,CAAC5H,IAAI,KAAK,iBAAiB,EAAE;QACzE,OAAO0H,kBAAkB,CAACK,WAAW;AACvC,MAAA;MACA,OAAOL,kBAAkB,CAACM,KAAK;AACjC,IAAA;AACF,EAAA;EAbgBN,kBAAA,CAAAC,UAAU,aAazB;AACH,CAAC,EAfgBD,kBAAkB,KAAlBA,kBAAkB,GAAA,EAAA,CAAA,CAAA;;AC3JnC,IAAYO,kBAIX;AAJD,CAAA,UAAYA,kBAAkB,EAAA;EAC5BA,kBAAA,CAAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc;EACdA,kBAAA,CAAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc;EACdA,kBAAA,CAAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AACnB,CAAC,EAJWA,kBAAkB,KAAlBA,kBAAkB,GAAA,EAAA,CAAA,CAAA;AAMxB,MAAOC,YAAa,SAAQd,YAAY,CAAA;EAK5CxD,WAAAA,CACE2D,OAAgB,EAEY;AAAA,IAAA,IAD5BY,MAAA,GAAA/I,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAA6B6I,kBAAkB,CAACG,aAAa;IAAA,IAC7DC,mBAA4B,GAAAjJ,SAAA,CAAAU,MAAA,GAAA,CAAA,GAAAV,SAAA,MAAAK,SAAA;AAE5B,IAAA,KAAK,CAAC,EAAE,EAAE8H,OAAO,CAAC;IAClB,IAAI,CAACY,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,mBAAmB,GAAGA,mBAAmB;AAChD,EAAA;AACD;;AClBD,IAAYC,gBAOX;AAPD,CAAA,UAAYA,gBAAgB,EAAA;AAC1BA,EAAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB;AACAA,EAAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACjC;AAC4E;AAC5EA,EAAAA,gBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC/B,CAAC,EAPWA,gBAAgB,KAAhBA,gBAAgB,GAAA,EAAA,CAAA,CAAA;AAmB5B,IAAYC,eAIX;AAJD,CAAA,UAAYA,eAAe,EAAA;AACzB;AAC6E;AAC7EA,EAAAA,eAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC/B,CAAC,EAJWA,eAAe,KAAfA,eAAe,GAAA,EAAA,CAAA,CAAA;AAc3B,IAAYC,eAGX;AAHD,CAAA,UAAYA,eAAe,EAAA;AACzBA,EAAAA,eAAA,CAAA,oCAAA,CAAA,GAAA,oCAAyE;AACzEA,EAAAA,eAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACrC,CAAC,EAHWA,eAAe,KAAfA,eAAe,GAAA,EAAA,CAAA,CAAA;AAiB3B,IAAYC,YAEX;AAFD,CAAA,UAAYA,YAAY,EAAA;AACtBA,EAAAA,YAAA,CAAA,OAAA,CAAA,GAAA,cAAsB;AACxB,CAAC,EAFWA,YAAY,KAAZA,YAAY,GAAA,EAAA,CAAA,CAAA;;;;;;;;EC/BxB,IAAIC,CAAC,GAAG,OAAOC,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG,IAAA;EAChD,IAAIC,YAAY,GAAGF,CAAC,IAAI,OAAOA,CAAC,CAACvJ,KAAK,KAAK,UAAA,GACvCuJ,CAAC,CAACvJ,KAAA,GACF,SAASyJ,YAAYA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAE;AAC9C,IAAA,OAAOhK,QAAQ,CAACC,SAAS,CAACG,KAAK,CAACF,IAAI,CAAC4J,MAAM,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EAChE,CAAA;AAEA,EAAA,IAAIC,cAAA;EACJ,IAAIN,CAAC,IAAI,OAAOA,CAAC,CAACO,OAAO,KAAK,UAAU,EAAE;IACxCD,cAAc,GAAGN,CAAC,CAACO,OAAA;AACrB,EAAA,CAAC,MAAM,IAAI3F,MAAM,CAAC4F,qBAAqB,EAAE;AACvCF,IAAAA,cAAc,GAAG,SAASA,cAAcA,CAACH,MAAM,EAAE;AAC/C,MAAA,OAAOvF,MAAM,CAAC6F,mBAAmB,CAACN,MAAM,CAAA,CACrCO,MAAM,CAAC9F,MAAM,CAAC4F,qBAAqB,CAACL,MAAM,CAAC,CAAC;IACnD,CAAG;AACH,EAAA,CAAC,MAAM;AACLG,IAAAA,cAAc,GAAG,SAASA,cAAcA,CAACH,MAAM,EAAE;AAC/C,MAAA,OAAOvF,MAAM,CAAC6F,mBAAmB,CAACN,MAAM,CAAC;IAC7C,CAAG;AACH,EAAA;EAEA,SAASQ,kBAAkBA,CAACC,OAAO,EAAE;IACnC,IAAIhK,OAAO,IAAIA,OAAO,CAACiK,IAAI,EAAEjK,OAAO,CAACiK,IAAI,CAACD,OAAO,CAAC;AACpD,EAAA;EAEA,IAAIE,WAAW,GAAGC,MAAM,CAACC,KAAK,IAAI,SAASF,WAAWA,CAACG,KAAK,EAAE;IAC5D,OAAOA,KAAK,KAAKA,KAAK;EACxB,CAAA;EAEA,SAASC,YAAYA,GAAG;AACtBA,IAAAA,YAAY,CAACC,IAAI,CAAC5K,IAAI,CAAC,IAAI,CAAC;AAC9B,EAAA;EACA6K,MAAA,CAAAjM,OAAc,GAAG+L,YAAY;AAC7BE,EAAAA,MAAA,CAAAjM,OAAA,CAAAkM,IAAmB,GAAGA,IAAI;;AAE1B;EACAH,YAAY,CAACA,YAAY,GAAGA,YAAY;AAExCA,EAAAA,YAAY,CAAC5K,SAAS,CAACgL,OAAO,GAAGvK,SAAS;AAC1CmK,EAAAA,YAAY,CAAC5K,SAAS,CAACiL,YAAY,GAAG,CAAC;AACvCL,EAAAA,YAAY,CAAC5K,SAAS,CAACkL,aAAa,GAAGzK,SAAS;;AAEhD;AACA;EACA,IAAI0K,mBAAmB,GAAG,EAAE;EAE5B,SAASC,aAAaA,CAACC,QAAQ,EAAE;AAC/B,IAAA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;AAClC,MAAA,MAAM,IAAIlI,SAAS,CAAC,kEAAkE,GAAG,OAAOkI,QAAQ,CAAC;AAC7G,IAAA;AACA,EAAA;AAEA/G,EAAAA,MAAM,CAACgH,cAAc,CAACV,YAAY,EAAE,qBAAqB,EAAE;AACzDW,IAAAA,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAE,YAAW;AACd,MAAA,OAAOL,mBAAmB;IAC9B,CAAG;AACDzE,IAAAA,GAAG,EAAE,UAAS+E,GAAG,EAAE;AACjB,MAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,GAAG,CAAC,IAAIjB,WAAW,CAACiB,GAAG,CAAC,EAAE;QAC1D,MAAM,IAAIC,UAAU,CAAC,iGAAiG,GAAGD,GAAG,GAAG,GAAG,CAAC;AACzI,MAAA;AACIN,MAAAA,mBAAmB,GAAGM,GAAG;AAC7B,IAAA;AACA,GAAC,CAAC;EAEFb,YAAY,CAACC,IAAI,GAAG,YAAW;AAE7B,IAAA,IAAI,IAAI,CAACG,OAAO,KAAKvK,SAAS,IAC1B,IAAI,CAACuK,OAAO,KAAK1G,MAAM,CAACqH,cAAc,CAAC,IAAI,CAAC,CAACX,OAAO,EAAE;MACxD,IAAI,CAACA,OAAO,GAAG1G,MAAM,CAACsH,MAAM,CAAC,IAAI,CAAC;MAClC,IAAI,CAACX,YAAY,GAAG,CAAC;AACzB,IAAA;AAEE,IAAA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,IAAIzK,SAAS;EACtD,CAAC;;AAED;AACA;EACAmK,YAAY,CAAC5K,SAAS,CAAC6L,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAE;AACnE,IAAA,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,IAAItB,WAAW,CAACsB,CAAC,CAAC,EAAE;MACpD,MAAM,IAAIJ,UAAU,CAAC,+EAA+E,GAAGI,CAAC,GAAG,GAAG,CAAC;AACnH,IAAA;IACE,IAAI,CAACZ,aAAa,GAAGY,CAAC;AACtB,IAAA,OAAO,IAAI;EACb,CAAC;EAED,SAASC,gBAAgBA,CAACC,IAAI,EAAE;IAC9B,IAAIA,IAAI,CAACd,aAAa,KAAKzK,SAAS,EAClC,OAAOmK,YAAY,CAACO,mBAAmB;IACzC,OAAOa,IAAI,CAACd,aAAa;AAC3B,EAAA;EAEAN,YAAY,CAAC5K,SAAS,CAACiM,eAAe,GAAG,SAASA,eAAeA,GAAG;IAClE,OAAOF,gBAAgB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAEDnB,YAAY,CAAC5K,SAAS,CAACkM,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAE;IAChD,IAAIpC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIlJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAACU,MAAM,EAAED,CAAC,EAAE,EAAEkJ,IAAI,CAACqC,IAAI,CAAChM,SAAS,CAACS,CAAC,CAAC,CAAC;AAClE,IAAA,IAAIwL,OAAO,GAAIF,IAAI,KAAK,OAAQ;AAEhC,IAAA,IAAIG,MAAM,GAAG,IAAI,CAACtB,OAAO;IACzB,IAAIsB,MAAM,KAAK7L,SAAS,EACtB4L,OAAO,GAAIA,OAAO,IAAIC,MAAM,CAAC1D,KAAK,KAAKnI,SAAU,CAAA,KAC9C,IAAI,CAAC4L,OAAO,EACf,OAAO,KAAK;;AAEhB;AACE,IAAA,IAAIA,OAAO,EAAE;AACX,MAAA,IAAIE,EAAE;MACN,IAAIxC,IAAI,CAACjJ,MAAM,GAAG,CAAC,EACjByL,EAAE,GAAGxC,IAAI,CAAC,CAAC,CAAC;MACd,IAAIwC,EAAE,YAAYlE,KAAK,EAAE;AAC7B;AACA;QACM,MAAMkE,EAAE,CAAC;AACf,MAAA;AACA;AACI,MAAA,IAAIC,GAAG,GAAG,IAAInE,KAAK,CAAC,kBAAkB,IAAIkE,EAAE,GAAG,IAAI,GAAGA,EAAE,CAAChE,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;MAC7EiE,GAAG,CAACC,OAAO,GAAGF,EAAE;MAChB,MAAMC,GAAG,CAAC;AACd,IAAA;AAEE,IAAA,IAAIE,OAAO,GAAGJ,MAAM,CAACH,IAAI,CAAC;AAE1B,IAAA,IAAIO,OAAO,KAAKjM,SAAS,EACvB,OAAO,KAAK;AAEd,IAAA,IAAI,OAAOiM,OAAO,KAAK,UAAU,EAAE;AACjC9C,MAAAA,YAAY,CAAC8C,OAAO,EAAE,IAAI,EAAE3C,IAAI,CAAC;AACrC,IAAA,CAAG,MAAM;AACL,MAAA,IAAI4C,GAAG,GAAGD,OAAO,CAAC5L,MAAM;AACxB,MAAA,IAAI8L,SAAS,GAAGC,UAAU,CAACH,OAAO,EAAEC,GAAG,CAAC;MACxC,KAAK,IAAI9L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8L,GAAG,EAAE,EAAE9L,CAAC,EAC1B+I,YAAY,CAACgD,SAAS,CAAC/L,CAAC,CAAC,EAAE,IAAI,EAAEkJ,IAAI,CAAC;AAC5C,IAAA;AAEE,IAAA,OAAO,IAAI;EACb,CAAC;EAED,SAAS+C,YAAYA,CAACjD,MAAM,EAAEsC,IAAI,EAAEd,QAAQ,EAAE0B,OAAO,EAAE;AACrD,IAAA,IAAIC,CAAC;AACL,IAAA,IAAIV,MAAM;AACV,IAAA,IAAIW,QAAQ;IAEZ7B,aAAa,CAACC,QAAQ,CAAC;IAEvBiB,MAAM,GAAGzC,MAAM,CAACmB,OAAO;IACvB,IAAIsB,MAAM,KAAK7L,SAAS,EAAE;MACxB6L,MAAM,GAAGzC,MAAM,CAACmB,OAAO,GAAG1G,MAAM,CAACsH,MAAM,CAAC,IAAI,CAAC;MAC7C/B,MAAM,CAACoB,YAAY,GAAG,CAAC;AAC3B,IAAA,CAAG,MAAM;AACT;AACA;AACI,MAAA,IAAIqB,MAAM,CAACY,WAAW,KAAKzM,SAAS,EAAE;AACpCoJ,QAAAA,MAAM,CAACqC,IAAI,CAAC,aAAa,EAAEC,IAAI,EACnBd,QAAQ,CAACA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,GAAGA,QAAQ,CAAC;;AAEnE;AACA;QACMiB,MAAM,GAAGzC,MAAM,CAACmB,OAAO;AAC7B,MAAA;AACIiC,MAAAA,QAAQ,GAAGX,MAAM,CAACH,IAAI,CAAC;AAC3B,IAAA;IAEE,IAAIc,QAAQ,KAAKxM,SAAS,EAAE;AAC9B;AACIwM,MAAAA,QAAQ,GAAGX,MAAM,CAACH,IAAI,CAAC,GAAGd,QAAQ;MAClC,EAAExB,MAAM,CAACoB,YAAY;AACzB,IAAA,CAAG,MAAM;AACL,MAAA,IAAI,OAAOgC,QAAQ,KAAK,UAAU,EAAE;AACxC;AACMA,QAAAA,QAAQ,GAAGX,MAAM,CAACH,IAAI,CAAC,GACrBY,OAAO,GAAG,CAAC1B,QAAQ,EAAE4B,QAAQ,CAAC,GAAG,CAACA,QAAQ,EAAE5B,QAAQ,CAAC;AAC7D;MACA,CAAK,MAAM,IAAI0B,OAAO,EAAE;AAClBE,QAAAA,QAAQ,CAACE,OAAO,CAAC9B,QAAQ,CAAC;AAChC,MAAA,CAAK,MAAM;AACL4B,QAAAA,QAAQ,CAACb,IAAI,CAACf,QAAQ,CAAC;AAC7B,MAAA;;AAEA;AACI2B,MAAAA,CAAC,GAAGjB,gBAAgB,CAAClC,MAAM,CAAC;AAC5B,MAAA,IAAImD,CAAC,GAAG,CAAC,IAAIC,QAAQ,CAACnM,MAAM,GAAGkM,CAAC,IAAI,CAACC,QAAQ,CAACG,MAAM,EAAE;QACpDH,QAAQ,CAACG,MAAM,GAAG,IAAI;AAC5B;AACA;QACM,IAAIC,CAAC,GAAG,IAAIhF,KAAK,CAAC,8CAA8C,GAC5C4E,QAAQ,CAACnM,MAAM,GAAG,GAAG,GAAGwM,MAAM,CAACnB,IAAI,CAAC,GAAG,aAAa,GACpD,0CAA0C,GAC1C,gBAAgB,CAAC;QACrCkB,CAAC,CAACrM,IAAI,GAAG,6BAA6B;QACtCqM,CAAC,CAACE,OAAO,GAAG1D,MAAM;QAClBwD,CAAC,CAAClB,IAAI,GAAGA,IAAI;AACbkB,QAAAA,CAAC,CAACG,KAAK,GAAGP,QAAQ,CAACnM,MAAM;QACzBuJ,kBAAkB,CAACgD,CAAC,CAAC;AAC3B,MAAA;AACA,IAAA;AAEE,IAAA,OAAOxD,MAAM;AACf,EAAA;EAEAe,YAAY,CAAC5K,SAAS,CAACyN,WAAW,GAAG,SAASA,WAAWA,CAACtB,IAAI,EAAEd,QAAQ,EAAE;IACxE,OAAOyB,YAAY,CAAC,IAAI,EAAEX,IAAI,EAAEd,QAAQ,EAAE,KAAK,CAAC;EAClD,CAAC;EAEDT,YAAY,CAAC5K,SAAS,CAAC0N,EAAE,GAAG9C,YAAY,CAAC5K,SAAS,CAACyN,WAAW;EAE9D7C,YAAY,CAAC5K,SAAS,CAAC2N,eAAe,GAClC,SAASA,eAAeA,CAACxB,IAAI,EAAEd,QAAQ,EAAE;IACvC,OAAOyB,YAAY,CAAC,IAAI,EAAEX,IAAI,EAAEd,QAAQ,EAAE,IAAI,CAAC;EACrD,CAAK;EAEL,SAASuC,WAAWA,GAAG;AACrB,IAAA,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;AACf,MAAA,IAAI,CAAChE,MAAM,CAACiE,cAAc,CAAC,IAAI,CAAC3B,IAAI,EAAE,IAAI,CAAC4B,MAAM,CAAC;MAClD,IAAI,CAACF,KAAK,GAAG,IAAI;AACjB,MAAA,IAAIzN,SAAS,CAACU,MAAM,KAAK,CAAC,EACxB,OAAO,IAAI,CAACuK,QAAQ,CAACpL,IAAI,CAAC,IAAI,CAAC4J,MAAM,CAAC;MACxC,OAAO,IAAI,CAACwB,QAAQ,CAAClL,KAAK,CAAC,IAAI,CAAC0J,MAAM,EAAEzJ,SAAS,CAAC;AACtD,IAAA;AACA,EAAA;AAEA,EAAA,SAAS4N,SAASA,CAACnE,MAAM,EAAEsC,IAAI,EAAEd,QAAQ,EAAE;AACzC,IAAA,IAAI4C,KAAK,GAAG;AAAEJ,MAAAA,KAAK,EAAE,KAAK;AAAEE,MAAAA,MAAM,EAAEtN,SAAS;AAAEoJ,MAAAA,MAAM,EAAEA,MAAM;AAAEsC,MAAAA,IAAI,EAAEA,IAAI;AAAEd,MAAAA,QAAQ,EAAEA;KAAU;AAC/F,IAAA,IAAI6C,OAAO,GAAGN,WAAW,CAAC9N,IAAI,CAACmO,KAAK,CAAC;IACrCC,OAAO,CAAC7C,QAAQ,GAAGA,QAAQ;IAC3B4C,KAAK,CAACF,MAAM,GAAGG,OAAO;AACtB,IAAA,OAAOA,OAAO;AAChB,EAAA;EAEAtD,YAAY,CAAC5K,SAAS,CAAC+K,IAAI,GAAG,SAASA,IAAIA,CAACoB,IAAI,EAAEd,QAAQ,EAAE;IAC1DD,aAAa,CAACC,QAAQ,CAAC;AACvB,IAAA,IAAI,CAACqC,EAAE,CAACvB,IAAI,EAAE6B,SAAS,CAAC,IAAI,EAAE7B,IAAI,EAAEd,QAAQ,CAAC,CAAC;AAC9C,IAAA,OAAO,IAAI;EACb,CAAC;EAEDT,YAAY,CAAC5K,SAAS,CAACmO,mBAAmB,GACtC,SAASA,mBAAmBA,CAAChC,IAAI,EAAEd,QAAQ,EAAE;IAC3CD,aAAa,CAACC,QAAQ,CAAC;AACvB,IAAA,IAAI,CAACsC,eAAe,CAACxB,IAAI,EAAE6B,SAAS,CAAC,IAAI,EAAE7B,IAAI,EAAEd,QAAQ,CAAC,CAAC;AAC3D,IAAA,OAAO,IAAI;EACjB,CAAK;;AAEL;EACAT,YAAY,CAAC5K,SAAS,CAAC8N,cAAc,GACjC,SAASA,cAAcA,CAAC3B,IAAI,EAAEd,QAAQ,EAAE;IACtC,IAAI+C,IAAI,EAAE9B,MAAM,EAAE+B,QAAQ,EAAExN,CAAC,EAAEyN,gBAAgB;IAE/ClD,aAAa,CAACC,QAAQ,CAAC;IAEvBiB,MAAM,GAAG,IAAI,CAACtB,OAAO;AACrB,IAAA,IAAIsB,MAAM,KAAK7L,SAAS,EACtB,OAAO,IAAI;AAEb2N,IAAAA,IAAI,GAAG9B,MAAM,CAACH,IAAI,CAAC;AACnB,IAAA,IAAIiC,IAAI,KAAK3N,SAAS,EACpB,OAAO,IAAI;IAEb,IAAI2N,IAAI,KAAK/C,QAAQ,IAAI+C,IAAI,CAAC/C,QAAQ,KAAKA,QAAQ,EAAE;AACnD,MAAA,IAAI,EAAE,IAAI,CAACJ,YAAY,KAAK,CAAC,EAC3B,IAAI,CAACD,OAAO,GAAG1G,MAAM,CAACsH,MAAM,CAAC,IAAI,CAAC,CAAA,KAC/B;QACH,OAAOU,MAAM,CAACH,IAAI,CAAC;AACnB,QAAA,IAAIG,MAAM,CAACwB,cAAc,EACvB,IAAI,CAAC5B,IAAI,CAAC,gBAAgB,EAAEC,IAAI,EAAEiC,IAAI,CAAC/C,QAAQ,IAAIA,QAAQ,CAAC;AACxE,MAAA;AACA,IAAA,CAAO,MAAM,IAAI,OAAO+C,IAAI,KAAK,UAAU,EAAE;MACrCC,QAAQ,GAAG,EAAE;AAEb,MAAA,KAAKxN,CAAC,GAAGuN,IAAI,CAACtN,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AACrC,QAAA,IAAIuN,IAAI,CAACvN,CAAC,CAAC,KAAKwK,QAAQ,IAAI+C,IAAI,CAACvN,CAAC,CAAC,CAACwK,QAAQ,KAAKA,QAAQ,EAAE;AACzDiD,UAAAA,gBAAgB,GAAGF,IAAI,CAACvN,CAAC,CAAC,CAACwK,QAAQ;AACnCgD,UAAAA,QAAQ,GAAGxN,CAAC;AACZ,UAAA;AACZ,QAAA;AACA,MAAA;AAEQ,MAAA,IAAIwN,QAAQ,GAAG,CAAC,EACd,OAAO,IAAI;MAEb,IAAIA,QAAQ,KAAK,CAAC,EAChBD,IAAI,CAACG,KAAK,EAAE,CAAA,KACT;AACHC,QAAAA,SAAS,CAACJ,IAAI,EAAEC,QAAQ,CAAC;AACnC,MAAA;AAEQ,MAAA,IAAID,IAAI,CAACtN,MAAM,KAAK,CAAC,EACnBwL,MAAM,CAACH,IAAI,CAAC,GAAGiC,IAAI,CAAC,CAAC,CAAC;AAExB,MAAA,IAAI9B,MAAM,CAACwB,cAAc,KAAKrN,SAAS,EACrC,IAAI,CAACyL,IAAI,CAAC,gBAAgB,EAAEC,IAAI,EAAEmC,gBAAgB,IAAIjD,QAAQ,CAAC;AACzE,IAAA;AAEM,IAAA,OAAO,IAAI;EACjB,CAAK;EAELT,YAAY,CAAC5K,SAAS,CAACyO,GAAG,GAAG7D,YAAY,CAAC5K,SAAS,CAAC8N,cAAc;EAElElD,YAAY,CAAC5K,SAAS,CAAC0O,kBAAkB,GACrC,SAASA,kBAAkBA,CAACvC,IAAI,EAAE;AAChC,IAAA,IAAIS,SAAS,EAAEN,MAAM,EAAEzL,CAAC;IAExByL,MAAM,GAAG,IAAI,CAACtB,OAAO;AACrB,IAAA,IAAIsB,MAAM,KAAK7L,SAAS,EACtB,OAAO,IAAI;;AAEnB;AACM,IAAA,IAAI6L,MAAM,CAACwB,cAAc,KAAKrN,SAAS,EAAE;AACvC,MAAA,IAAIL,SAAS,CAACU,MAAM,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACkK,OAAO,GAAG1G,MAAM,CAACsH,MAAM,CAAC,IAAI,CAAC;QAClC,IAAI,CAACX,YAAY,GAAG,CAAC;MAC/B,CAAS,MAAM,IAAIqB,MAAM,CAACH,IAAI,CAAC,KAAK1L,SAAS,EAAE;QACrC,IAAI,EAAE,IAAI,CAACwK,YAAY,KAAK,CAAC,EAC3B,IAAI,CAACD,OAAO,GAAG1G,MAAM,CAACsH,MAAM,CAAC,IAAI,CAAC,CAAA,KAElC,OAAOU,MAAM,CAACH,IAAI,CAAC;AAC/B,MAAA;AACQ,MAAA,OAAO,IAAI;AACnB,IAAA;;AAEA;AACM,IAAA,IAAI/L,SAAS,CAACU,MAAM,KAAK,CAAC,EAAE;AAC1B,MAAA,IAAI6N,IAAI,GAAGrK,MAAM,CAACqK,IAAI,CAACrC,MAAM,CAAC;AAC9B,MAAA,IAAI9E,GAAG;AACP,MAAA,KAAK3G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8N,IAAI,CAAC7N,MAAM,EAAE,EAAED,CAAC,EAAE;AAChC2G,QAAAA,GAAG,GAAGmH,IAAI,CAAC9N,CAAC,CAAC;QACb,IAAI2G,GAAG,KAAK,gBAAgB,EAAE;AAC9B,QAAA,IAAI,CAACkH,kBAAkB,CAAClH,GAAG,CAAC;AACtC,MAAA;AACQ,MAAA,IAAI,CAACkH,kBAAkB,CAAC,gBAAgB,CAAC;MACzC,IAAI,CAAC1D,OAAO,GAAG1G,MAAM,CAACsH,MAAM,CAAC,IAAI,CAAC;MAClC,IAAI,CAACX,YAAY,GAAG,CAAC;AACrB,MAAA,OAAO,IAAI;AACnB,IAAA;AAEM2B,IAAAA,SAAS,GAAGN,MAAM,CAACH,IAAI,CAAC;AAExB,IAAA,IAAI,OAAOS,SAAS,KAAK,UAAU,EAAE;AACnC,MAAA,IAAI,CAACkB,cAAc,CAAC3B,IAAI,EAAES,SAAS,CAAC;AAC5C,IAAA,CAAO,MAAM,IAAIA,SAAS,KAAKnM,SAAS,EAAE;AAC1C;AACQ,MAAA,KAAKI,CAAC,GAAG+L,SAAS,CAAC9L,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1C,IAAI,CAACiN,cAAc,CAAC3B,IAAI,EAAES,SAAS,CAAC/L,CAAC,CAAC,CAAC;AACjD,MAAA;AACA,IAAA;AAEM,IAAA,OAAO,IAAI;EACjB,CAAK;AAEL,EAAA,SAAS+N,UAAUA,CAAC/E,MAAM,EAAEsC,IAAI,EAAE0C,MAAM,EAAE;AACxC,IAAA,IAAIvC,MAAM,GAAGzC,MAAM,CAACmB,OAAO;AAE3B,IAAA,IAAIsB,MAAM,KAAK7L,SAAS,EACtB,OAAO,EAAE;AAEX,IAAA,IAAIqO,UAAU,GAAGxC,MAAM,CAACH,IAAI,CAAC;AAC7B,IAAA,IAAI2C,UAAU,KAAKrO,SAAS,EAC1B,OAAO,EAAE;AAEX,IAAA,IAAI,OAAOqO,UAAU,KAAK,UAAU,EAClC,OAAOD,MAAM,GAAG,CAACC,UAAU,CAACzD,QAAQ,IAAIyD,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC;AAEpE,IAAA,OAAOD,MAAM,GACXE,eAAe,CAACD,UAAU,CAAC,GAAGjC,UAAU,CAACiC,UAAU,EAAEA,UAAU,CAAChO,MAAM,CAAC;AAC3E,EAAA;EAEA8J,YAAY,CAAC5K,SAAS,CAAC4M,SAAS,GAAG,SAASA,SAASA,CAACT,IAAI,EAAE;AAC1D,IAAA,OAAOyC,UAAU,CAAC,IAAI,EAAEzC,IAAI,EAAE,IAAI,CAAC;EACrC,CAAC;EAEDvB,YAAY,CAAC5K,SAAS,CAACgP,YAAY,GAAG,SAASA,YAAYA,CAAC7C,IAAI,EAAE;AAChE,IAAA,OAAOyC,UAAU,CAAC,IAAI,EAAEzC,IAAI,EAAE,KAAK,CAAC;EACtC,CAAC;AAEDvB,EAAAA,YAAY,CAACqE,aAAa,GAAG,UAAS1B,OAAO,EAAEpB,IAAI,EAAE;AACnD,IAAA,IAAI,OAAOoB,OAAO,CAAC0B,aAAa,KAAK,UAAU,EAAE;AAC/C,MAAA,OAAO1B,OAAO,CAAC0B,aAAa,CAAC9C,IAAI,CAAC;AACtC,IAAA,CAAG,MAAM;AACL,MAAA,OAAO8C,aAAa,CAAChP,IAAI,CAACsN,OAAO,EAAEpB,IAAI,CAAC;AAC5C,IAAA;EACA,CAAC;AAEDvB,EAAAA,YAAY,CAAC5K,SAAS,CAACiP,aAAa,GAAGA,aAAa;EACpD,SAASA,aAAaA,CAAC9C,IAAI,EAAE;AAC3B,IAAA,IAAIG,MAAM,GAAG,IAAI,CAACtB,OAAO;IAEzB,IAAIsB,MAAM,KAAK7L,SAAS,EAAE;AACxB,MAAA,IAAIqO,UAAU,GAAGxC,MAAM,CAACH,IAAI,CAAC;AAE7B,MAAA,IAAI,OAAO2C,UAAU,KAAK,UAAU,EAAE;AACpC,QAAA,OAAO,CAAC;AACd,MAAA,CAAK,MAAM,IAAIA,UAAU,KAAKrO,SAAS,EAAE;QACnC,OAAOqO,UAAU,CAAChO,MAAM;AAC9B,MAAA;AACA,IAAA;AAEE,IAAA,OAAO,CAAC;AACV,EAAA;EAEA8J,YAAY,CAAC5K,SAAS,CAACkP,UAAU,GAAG,SAASA,UAAUA,GAAG;AACxD,IAAA,OAAO,IAAI,CAACjE,YAAY,GAAG,CAAC,GAAGjB,cAAc,CAAC,IAAI,CAACgB,OAAO,CAAC,GAAG,EAAE;EAClE,CAAC;AAED,EAAA,SAAS6B,UAAUA,CAACsC,GAAG,EAAErD,CAAC,EAAE;AAC1B,IAAA,IAAIsD,IAAI,GAAG,IAAIjI,KAAK,CAAC2E,CAAC,CAAC;IACvB,KAAK,IAAIjL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiL,CAAC,EAAE,EAAEjL,CAAC,EACxBuO,IAAI,CAACvO,CAAC,CAAC,GAAGsO,GAAG,CAACtO,CAAC,CAAC;AAClB,IAAA,OAAOuO,IAAI;AACb,EAAA;AAEA,EAAA,SAASZ,SAASA,CAACJ,IAAI,EAAEiB,KAAK,EAAE;IAC9B,OAAOA,KAAK,GAAG,CAAC,GAAGjB,IAAI,CAACtN,MAAM,EAAEuO,KAAK,EAAE,EACrCjB,IAAI,CAACiB,KAAK,CAAC,GAAGjB,IAAI,CAACiB,KAAK,GAAG,CAAC,CAAC;IAC/BjB,IAAI,CAACkB,GAAG,EAAE;AACZ,EAAA;EAEA,SAASP,eAAeA,CAACI,GAAG,EAAE;IAC5B,IAAII,GAAG,GAAG,IAAIpI,KAAK,CAACgI,GAAG,CAACrO,MAAM,CAAC;AAC/B,IAAA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0O,GAAG,CAACzO,MAAM,EAAE,EAAED,CAAC,EAAE;AACnC0O,MAAAA,GAAG,CAAC1O,CAAC,CAAC,GAAGsO,GAAG,CAACtO,CAAC,CAAC,CAACwK,QAAQ,IAAI8D,GAAG,CAACtO,CAAC,CAAC;AACtC,IAAA;AACE,IAAA,OAAO0O,GAAG;AACZ,EAAA;AAEA,EAAA,SAASxE,IAAIA,CAACwC,OAAO,EAAEvM,IAAI,EAAE;AAC3B,IAAA,OAAO,IAAI+D,OAAO,CAAC,UAAUC,OAAO,EAAEwK,MAAM,EAAE;MAC5C,SAASC,aAAaA,CAACjD,GAAG,EAAE;AAC1Be,QAAAA,OAAO,CAACO,cAAc,CAAC9M,IAAI,EAAE0O,QAAQ,CAAC;QACtCF,MAAM,CAAChD,GAAG,CAAC;AACjB,MAAA;MAEI,SAASkD,QAAQA,GAAG;AAClB,QAAA,IAAI,OAAOnC,OAAO,CAACO,cAAc,KAAK,UAAU,EAAE;AAChDP,UAAAA,OAAO,CAACO,cAAc,CAAC,OAAO,EAAE2B,aAAa,CAAC;AACtD,QAAA;QACMzK,OAAO,CAAC,EAAE,CAAClC,KAAK,CAAC7C,IAAI,CAACG,SAAS,CAAC,CAAC;AACvC,MAAA;AAEIuP,MAAAA,8BAA8B,CAACpC,OAAO,EAAEvM,IAAI,EAAE0O,QAAQ,EAAE;AAAE3E,QAAAA,IAAI,EAAE;OAAM,CAAC;MACvE,IAAI/J,IAAI,KAAK,OAAO,EAAE;AACpB4O,QAAAA,6BAA6B,CAACrC,OAAO,EAAEkC,aAAa,EAAE;AAAE1E,UAAAA,IAAI,EAAE;AAAI,SAAE,CAAC;AAC3E,MAAA;AACA,IAAA,CAAG,CAAC;AACJ,EAAA;AAEA,EAAA,SAAS6E,6BAA6BA,CAACrC,OAAO,EAAEb,OAAO,EAAEmD,KAAK,EAAE;AAC9D,IAAA,IAAI,OAAOtC,OAAO,CAACG,EAAE,KAAK,UAAU,EAAE;MACpCiC,8BAA8B,CAACpC,OAAO,EAAE,OAAO,EAAEb,OAAO,EAAEmD,KAAK,CAAC;AACpE,IAAA;AACA,EAAA;EAEA,SAASF,8BAA8BA,CAACpC,OAAO,EAAEvM,IAAI,EAAEqK,QAAQ,EAAEwE,KAAK,EAAE;AACtE,IAAA,IAAI,OAAOtC,OAAO,CAACG,EAAE,KAAK,UAAU,EAAE;MACpC,IAAImC,KAAK,CAAC9E,IAAI,EAAE;AACdwC,QAAAA,OAAO,CAACxC,IAAI,CAAC/J,IAAI,EAAEqK,QAAQ,CAAC;AAClC,MAAA,CAAK,MAAM;AACLkC,QAAAA,OAAO,CAACG,EAAE,CAAC1M,IAAI,EAAEqK,QAAQ,CAAC;AAChC,MAAA;IACA,CAAG,MAAM,IAAI,OAAOkC,OAAO,CAACuC,gBAAgB,KAAK,UAAU,EAAE;AAC7D;AACA;MACIvC,OAAO,CAACuC,gBAAgB,CAAC9O,IAAI,EAAE,SAAS+O,YAAYA,CAACtE,GAAG,EAAE;AAC9D;AACA;QACM,IAAIoE,KAAK,CAAC9E,IAAI,EAAE;AACdwC,UAAAA,OAAO,CAACyC,mBAAmB,CAAChP,IAAI,EAAE+O,YAAY,CAAC;AACvD,QAAA;QACM1E,QAAQ,CAACI,GAAG,CAAC;AACnB,MAAA,CAAK,CAAC;AACN,IAAA,CAAG,MAAM;AACL,MAAA,MAAM,IAAItI,SAAS,CAAC,qEAAqE,GAAG,OAAOoK,OAAO,CAAC;AAC/G,IAAA;AACA,EAAA;;;;;;AC7dM,SAAU0C,YAAYA,CAC1BC,KAAkD,EAAA;EAElD,OAAO,MAAM,IAAIA,KAAK;AACxB;SAEsBC,SAASA,CAAAC,UAAA,EAAA;sDAC7BC,QAAkC,EAAA;IAAA,IAClCC,gFAAuC;AAAEtP,MAAAA,IAAI,EAAEqG;KAAsB;AAAA,IAAA,IACrEkJ,KAAA,GAAAnQ,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAA8B,SAAS;IAAA,OAAA,aAAA;AAEvC;AACA,MAAA,OAAOoQ,MAAM,CAACC,MAAM,CAACN,SAAS,CAC5B,KAAK,EACLE,QAAQ,EACRC,SAAS,EACT,KAAK,EACLC,KAAK,KAAK,QAAQ,GAAG,CAAC,YAAY,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAC1E;IACH,CAAC,EAAA;EAAA,CAAA,CAAA;AAAA;AA2BD,SAASG,cAAcA,CAACC,aAAqB,EAAEC,IAAY,EAAA;AACzD,EAAA,MAAMC,WAAW,GAAG,IAAIC,WAAW,EAAE;AACrC,EAAA,MAAMC,WAAW,GAAGF,WAAW,CAACG,MAAM,CAACJ,IAAI,CAAC;AAC5C,EAAA,QAAQD,aAAa;AACnB,IAAA,KAAK,MAAM;MACT,OAAO;AACL3P,QAAAA,IAAI,EAAE,MAAM;AACZ4P,QAAAA,IAAI,EAAEG,WAAW;AACjBE,QAAAA,IAAI,EAAE,SAAS;AACfxM,QAAAA,IAAI,EAAE,IAAIyM,WAAW,CAAC,GAAG;OAC1B;AACH,IAAA,KAAK,QAAQ;AAAE,MAAA;QACb,OAAO;AACLlQ,UAAAA,IAAI,EAAE,QAAQ;AACd4P,UAAAA,IAAI,EAAEG,WAAW;AACjBE,UAAAA,IAAI,EAAE,SAAS;AACfE,UAAAA,UAAU,EAAE;SACb;AACH,MAAA;AACA,IAAA;AACE,MAAA,MAAM,IAAI9I,KAAK,CAAA,YAAA,CAAA+B,MAAA,CAAcuG,aAAa,8BAA2B,CAAC;AAC1E;AACF;AAEA;;;AAGG;AACG,SAAgBS,UAAUA,CAACC,QAAmB,EAAET,IAAY,EAAA;;IAChE,MAAMU,gBAAgB,GAAGZ,cAAc,CAACW,QAAQ,CAACf,SAAS,CAACtP,IAAI,EAAE4P,IAAI,CAAC;AAEtE;AACA;AACA,IAAA,MAAMW,aAAa,GAAG,MAAMf,MAAM,CAACC,MAAM,CAACe,SAAS,CACjDF,gBAAgB,EAChBD,QAAQ,EACR;AACErQ,MAAAA,IAAI,EAAEqG,oBAAoB;AAC1BvG,MAAAA,MAAM,EAAE;KACT,EACD,KAAK,EACL,CAAC,SAAS,EAAE,SAAS,CAAC,CACvB;IAED,OAAO;MAAEuQ,QAAQ;AAAEE,MAAAA;KAAe;AACpC,EAAA,CAAC,CAAA;AAAA;AAMD;;;AAGG;AACG,SAAgBE,OAAOA,CAACJ,QAAmB,EAAET,IAAY,EAAA;;IAC7D,MAAMU,gBAAgB,GAAGZ,cAAc,CAACW,QAAQ,CAACf,SAAS,CAACtP,IAAI,EAAE4P,IAAI,CAAC;AAEtE;IACA,OAAOJ,MAAM,CAACC,MAAM,CAACiB,UAAU,CAACJ,gBAAgB,EAAED,QAAQ,EAAE,GAAG,CAAC;AAClE,EAAA,CAAC,CAAA;AAAA;AAEK,SAAUM,mBAAmBA,CAACC,SAAqB,EAAA;AACvD,EAAA,KAAK,IAAI/Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+Q,SAAS,CAAC9Q,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;IAC7C,IAAI+Q,SAAS,CAAC/Q,CAAC,CAAC,IAAI,CAAC,IAAI+Q,SAAS,CAAC/Q,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI+Q,SAAS,CAAC/Q,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI;AACtF,EAAA;AACA,EAAA,OAAO,KAAK;AACd;AAEM,SAAUgR,SAASA,CAACC,MAAkB,EAAA;EAC1C,MAAMC,OAAO,GAAa,EAAE;AAC5B,EAAA,IAAIjR,MAAM,GAAGgR,MAAM,CAAChR,MAAM;EAC1B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiR,MAAM,CAAChR,MAAM,GAAI;AACnC;AACA;AACA;AACA;AACA,IAAA,IAAIA,MAAM,GAAGD,CAAC,IAAI,CAAC,IAAI,CAACiR,MAAM,CAACjR,CAAC,CAAC,IAAI,CAACiR,MAAM,CAACjR,CAAC,GAAG,CAAC,CAAC,IAAIiR,MAAM,CAACjR,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE;AACzE;MACAkR,OAAO,CAAC3F,IAAI,CAAC0F,MAAM,CAACjR,CAAC,EAAE,CAAC,CAAC;MACzBkR,OAAO,CAAC3F,IAAI,CAAC0F,MAAM,CAACjR,CAAC,EAAE,CAAC,CAAC;AACzB;AACAA,MAAAA,CAAC,EAAE;AACL,IAAA,CAAC,MAAM;AACL;MACAkR,OAAO,CAAC3F,IAAI,CAAC0F,MAAM,CAACjR,CAAC,EAAE,CAAC,CAAC;AAC3B,IAAA;AACF,EAAA;AACA,EAAA,OAAO,IAAImR,UAAU,CAACD,OAAO,CAAC;AAChC;AAEA,MAAME,qBAAqB,GAAG,CAAC;AAC/B,MAAMC,cAAc,GAAG,CAAC;AAElB,SAAUC,SAASA,CAACC,OAAmB,EAAA;EAC3C,MAAML,OAAO,GAAa,EAAE;EAC5B,IAAIM,mBAAmB,GAAG,CAAC;AAC3B,EAAA,KAAK,IAAIxR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuR,OAAO,CAACtR,MAAM,EAAE,EAAED,CAAC,EAAE;AACvC,IAAA,IAAIyR,IAAI,GAAGF,OAAO,CAACvR,CAAC,CAAC;AACrB,IAAA,IAAIyR,IAAI,IAAIJ,cAAc,IAAIG,mBAAmB,IAAIJ,qBAAqB,EAAE;AAC1E;AACAF,MAAAA,OAAO,CAAC3F,IAAI,CAAC8F,cAAc,CAAC;AAC5BG,MAAAA,mBAAmB,GAAG,CAAC;AACzB,IAAA;AACAN,IAAAA,OAAO,CAAC3F,IAAI,CAACkG,IAAI,CAAC;IAClB,IAAIA,IAAI,IAAI,CAAC,EAAE;AACb,MAAA,EAAED,mBAAmB;AACvB,IAAA,CAAC,MAAM;AACLA,MAAAA,mBAAmB,GAAG,CAAC;AACzB,IAAA;AACF,EAAA;AACA,EAAA,OAAO,IAAIL,UAAU,CAACD,OAAO,CAAC;AAChC;;ACjLA;;;AAGG;AAEH;;AAEG;AACH,MAAMQ,iBAAiB,GAAG,IAAI;AAE9B;;AAEG;AACH,IAAKC,YA4CJ;AA5CD,CAAA,UAAKA,YAAY,EAAA;AACf;EACAA,YAAA,CAAAA,YAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AACjB;EACAA,YAAA,CAAAA,YAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB;AACrB;EACAA,YAAA,CAAAA,YAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB;AACrB;EACAA,YAAA,CAAAA,YAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAqB;AACrB;EACAA,YAAA,CAAAA,YAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb;EACAA,YAAA,CAAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP;EACAA,YAAA,CAAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP;EACAA,YAAA,CAAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP;EACAA,YAAA,CAAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ;EACAA,YAAA,CAAAA,YAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe;AACf;EACAA,YAAA,CAAAA,YAAA,CAAA,aAAA,CAAA,GAAA,EAAA,CAAA,GAAA,aAAgB;AAChB;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ;EACAA,YAAA,CAAAA,YAAA,CAAA,aAAA,CAAA,GAAA,EAAA,CAAA,GAAA,aAAgB;AAChB;EACAA,YAAA,CAAAA,YAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe;AACf;EACAA,YAAA,CAAAA,YAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAQ;AAER;AAEA;EACAA,YAAA,CAAAA,YAAA,CAAA,WAAA,CAAA,GAAA,EAAA,CAAA,GAAA,WAAc;AACd;EACAA,YAAA,CAAAA,YAAA,CAAA,WAAA,CAAA,GAAA,EAAA,CAAA,GAAA,WAAc;AACd;EACAA,YAAA,CAAAA,YAAA,CAAA,iBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,iBAAoB;AAEpB;AACF,CAAC,EA5CIA,YAAY,KAAZA,YAAY,GAAA,EAAA,CAAA,CAAA;AA8CjB;;AAEG;AACH,IAAKC,YA4DJ;AA5DD,CAAA,UAAKA,YAAY,EAAA;AACf;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACX;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACX;EACAA,YAAA,CAAAA,YAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT;EACAA,YAAA,CAAAA,YAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT;EACAA,YAAA,CAAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV;EACAA,YAAA,CAAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV;EACAA,YAAA,CAAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV;EACAA,YAAA,CAAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV;EACAA,YAAA,CAAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV;EACAA,YAAA,CAAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AAEV;AAEA;EACAA,YAAA,CAAAA,YAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa;AACb;EACAA,YAAA,CAAAA,YAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe;AACf;EACAA,YAAA,CAAAA,YAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa;AACb;EACAA,YAAA,CAAAA,YAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe;AACf;EACAA,YAAA,CAAAA,YAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa;AACb;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AAEZ;AAEA;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ;EACAA,YAAA,CAAAA,YAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACZ;EACAA,YAAA,CAAAA,YAAA,CAAA,QAAA,CAAA,GAAA,EAAA,CAAA,GAAA,QAAW;AACX;EACAA,YAAA,CAAAA,YAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gBAAmB;AACnB;EACAA,YAAA,CAAAA,YAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gBAAmB;AAEnB;AACA;AACF,CAAC,EA5DIA,YAAY,KAAZA,YAAY,GAAA,EAAA,CAAA,CAAA;AA8DjB;;;;AAIG;AACH,SAASC,iBAAiBA,CAACC,SAAiB,EAAA;EAC1C,OAAOA,SAAS,GAAGJ,iBAAiB;AACtC;AAEA;;;;AAIG;AACH,SAASK,iBAAiBA,CAACC,SAAiB,EAAA;AAC1C;AACA,EAAA,OAAQA,SAAS,IAAI,CAAC,GAAI,IAAI;AAChC;AAEA;;;;AAIG;AACH,SAASC,eAAeA,CAACC,QAAsB,EAAA;EAC7C,OAAOA,QAAQ,KAAKP,YAAY,CAACQ,SAAS,IAAID,QAAQ,KAAKP,YAAY,CAACS,aAAa;AACvF;AAEA;;;;AAIG;AACH,SAASC,eAAeA,CAACH,QAAsB,EAAA;AAC7C,EAAA;AACE;IACAA,QAAQ,KAAKN,YAAY,CAACU,OAAO,IACjCJ,QAAQ,KAAKN,YAAY,CAACW,OAAO,IACjCL,QAAQ,KAAKN,YAAY,CAACY,KAAK,IAC/BN,QAAQ,KAAKN,YAAY,CAACa,KAAK,IAC/BP,QAAQ,KAAKN,YAAY,CAACc,MAAM,IAChCR,QAAQ,KAAKN,YAAY,CAACe,MAAM,IAChCT,QAAQ,KAAKN,YAAY,CAACgB,MAAM,IAChCV,QAAQ,KAAKN,YAAY,CAACiB,MAAM,IAChCX,QAAQ,KAAKN,YAAY,CAACkB,MAAM,IAChCZ,QAAQ,KAAKN,YAAY,CAACmB,MAAM,IAChCb,QAAQ,KAAKN,YAAY,CAACoB,QAAQ,IAClCd,QAAQ,KAAKN,YAAY,CAACqB,UAAU,IACpCf,QAAQ,KAAKN,YAAY,CAACsB,QAAQ,IAClChB,QAAQ,KAAKN,YAAY,CAACuB,UAAU,IACpCjB,QAAQ,KAAKN,YAAY,CAACwB,QAAQ,IAClClB,QAAQ,KAAKN,YAAY,CAACyB;AAAO;AAErC;AAmBA;;;;;AAKG;AACH,SAASC,oBAAoBA,CAACC,IAAgB,EAAEC,WAAqB,EAAA;AACnE,EAAA,KAAK,MAAMC,SAAS,IAAID,WAAW,EAAE;AACnC,IAAA,IAAIvB,eAAe,CAACJ,iBAAiB,CAAC0B,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM;AACtE,IAAA,IAAIpB,eAAe,CAACN,iBAAiB,CAACwB,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM;AACxE,EAAA;AACA,EAAA,OAAO,SAAS;AAClB;AAEA;;;;;;AAMG;AACH,SAASC,6BAA6BA,CACpCH,IAAgB,EAChBC,WAAqB,EACrBG,KAAsB,EAAA;AAEtB,EAAA,KAAK,MAAMnF,KAAK,IAAIgF,WAAW,EAAE;IAC/B,IAAIG,KAAK,KAAK,MAAM,EAAE;MACpB,MAAMrI,IAAI,GAAGyG,iBAAiB,CAACwB,IAAI,CAAC/E,KAAK,CAAC,CAAC;AAC3C,MAAA,IAAI6D,eAAe,CAAC/G,IAAI,CAAC,EAAE;QACzB,OAAOkD,KAAK,GAAG,CAAC;AAClB,MAAA;AACF,IAAA,CAAC,MAAM;MACL,MAAMlD,IAAI,GAAGuG,iBAAiB,CAAC0B,IAAI,CAAC/E,KAAK,CAAC,CAAC;AAC3C,MAAA,IAAIyD,eAAe,CAAC3G,IAAI,CAAC,EAAE;QACzB,OAAOkD,KAAK,GAAG,CAAC;AAClB,MAAA;AACF,IAAA;AACF,EAAA;AACA,EAAA,OAAO,IAAI;AACb;AAEA;;;;;;;;;AASG;AACH,SAASoF,eAAeA,CAAC3C,MAAkB,EAAA;EACzC,MAAM4C,MAAM,GAAa,EAAE;EAC3B,IAAIC,KAAK,GAAG,CAAC;AACXC,IAAAA,GAAG,GAAG,CAAC;AACPC,IAAAA,YAAY,GAAG/C,MAAM,CAAChR,MAAM,GAAG,CAAC,CAAC;EAEnC,OAAO8T,GAAG,GAAGC,YAAY,EAAE;AACzB;IACA,OAAOD,GAAG,GAAGC,YAAY,EAAE;AACzB;AACA,MAAA,IACED,GAAG,GAAGC,YAAY,GAAG,CAAC,IACtB/C,MAAM,CAAC8C,GAAG,CAAC,KAAK,CAAC,IACjB9C,MAAM,CAAC8C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IACrB9C,MAAM,CAAC8C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IACrB9C,MAAM,CAAC8C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,EACrB;AACA,QAAA;AACF,MAAA;AACA;MACA,IAAI9C,MAAM,CAAC8C,GAAG,CAAC,KAAK,CAAC,IAAI9C,MAAM,CAAC8C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI9C,MAAM,CAAC8C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;AACvE,QAAA;AACF,MAAA;AACAA,MAAAA,GAAG,EAAE;AACP,IAAA;IAEA,IAAIA,GAAG,IAAIC,YAAY,EAAED,GAAG,GAAG9C,MAAM,CAAChR,MAAM;AAE5C;IACA,IAAIgU,GAAG,GAAGF,GAAG;AACb,IAAA,OAAOE,GAAG,GAAGH,KAAK,IAAI7C,MAAM,CAACgD,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,EAAEA,GAAG,EAAE;AAElD;IACA,IAAIH,KAAK,KAAK,CAAC,EAAE;MACf,IAAIG,GAAG,KAAKH,KAAK,EAAE,MAAMxR,SAAS,CAAC,mCAAmC,CAAC;AACzE,IAAA,CAAC,MAAM;AACLuR,MAAAA,MAAM,CAACtI,IAAI,CAACuI,KAAK,CAAC;AACpB,IAAA;AAEA;IACA,IAAII,eAAe,GAAG,CAAC;AACvB,IAAA,IACEH,GAAG,GAAG9C,MAAM,CAAChR,MAAM,GAAG,CAAC,IACvBgR,MAAM,CAAC8C,GAAG,CAAC,KAAK,CAAC,IACjB9C,MAAM,CAAC8C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IACrB9C,MAAM,CAAC8C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IACrB9C,MAAM,CAAC8C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,EACrB;AACAG,MAAAA,eAAe,GAAG,CAAC;AACrB,IAAA;AAEAJ,IAAAA,KAAK,GAAGC,GAAG,GAAGA,GAAG,GAAGG,eAAe;AACrC,EAAA;AACA,EAAA,OAAOL,MAAM;AACf;AAEA;;;;;AAKG;AACG,SAAUM,yBAAyBA,CACvCZ,IAAgB,EAChBa,UAA4B,EAAA;AAE5B,EAAA,MAAMZ,WAAW,GAAGI,eAAe,CAACL,IAAI,CAAC;AACzC,EAAA,MAAMc,aAAa,GAAGD,UAAU,KAAA,IAAA,IAAVA,UAAU,KAAA,MAAA,GAAVA,UAAU,GAAId,oBAAoB,CAACC,IAAI,EAAEC,WAAW,CAAC;EAE3E,IAAIa,aAAa,KAAK,SAAS,EAAE;IAC/B,OAAO;AAAEC,MAAAA,gBAAgB,EAAE,CAAC;MAAED,aAAa;AAAEE,MAAAA,sBAAsB,EAAE;KAAO;AAC9E,EAAA;EAEA,MAAMD,gBAAgB,GAAGZ,6BAA6B,CAACH,IAAI,EAAEC,WAAW,EAAEa,aAAa,CAAC;EACxF,IAAIC,gBAAgB,KAAK,IAAI,EAAE;AAC7B,IAAA,MAAM,IAAIhS,SAAS,CAAC,qBAAqB,CAAC;AAC5C,EAAA;EAEA,OAAO;IAAEgS,gBAAgB;IAAED,aAAa;AAAEE,IAAAA,sBAAsB,EAAE;GAAM;AAC1E;;ACzSA;;AAEG;AACH,SAAeC,UAAUA,CAACjB,IAA8B,EAAA;;AACtD,IAAA,MAAMkB,UAAU,GAAG,MAAM9E,MAAM,CAACC,MAAM,CAAC8E,MAAM,CAAC,SAAS,EAAEnB,IAAI,CAAC;AAC9D,IAAA,MAAMoB,SAAS,GAAG,IAAIxD,UAAU,CAACsD,UAAU,CAAC;AAC5C,IAAA,OAAOnO,KAAK,CAACC,IAAI,CAACoO,SAAS,CAAC,CACzBhR,GAAG,CAAEiR,CAAC,IAAKA,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAC3CC,IAAI,CAAC,EAAE,CAAC;AACb,EAAA,CAAC,CAAA;AAAA;AAED;;AAEG;AACI,MAAMC,YAAY,GAAG;AAC1BC,EAAAA,cAAc,EAAE,kEAAkE;AAClFC,EAAAA,kBAAkB,EAAE,kEAAkE;AACtFC,EAAAA,kBAAkB,EAAE,kEAAkE;AACtFC,EAAAA,kBAAkB,EAAE,kEAAkE;AACtFC,EAAAA,gBAAgB,EAAE;CACV;AAEV;;AAEG;AACG,SAAgBC,kBAAkBA,CACtC/B,IAA8B,EAAA;;AAE9B,IAAA,MAAMnD,IAAI,GAAG,MAAMoE,UAAU,CAACjB,IAAI,CAAC;AAEnC,IAAA,QAAQnD,IAAI;MACV,KAAK4E,YAAY,CAACC,cAAc;AAC9B,QAAA,OAAO,KAAK;MACd,KAAKD,YAAY,CAACE,kBAAkB;AAClC,QAAA,OAAO,MAAM;MACf,KAAKF,YAAY,CAACG,kBAAkB;AAClC,QAAA,OAAO,MAAM;MACf,KAAKH,YAAY,CAACI,kBAAkB;AAClC,QAAA,OAAO,MAAM;MACf,KAAKJ,YAAY,CAACK,gBAAgB;AAChC,QAAA,OAAO,MAAM;AACf,MAAA;AACE,QAAA,OAAO,IAAI;AACf;AACF,EAAA,CAAC,CAAA;AAAA;;AC3DM,MAAME,oBAAoB,GAAyB,IAAIvQ,GAAG,EAAE;AAa7D,MAAOwQ,gBAAiB,SAASzL,0BAA8D,CAAA;AACzF0L,EAAAA,cAAcA,CACtBC,YAAyD,EACzDC,UAA4C,EAAA;IAE5C,MAAMnO,KAAK,CAAC,8BAA8B,CAAC;AAC7C,EAAA;AAEUoO,EAAAA,cAAcA,CACtBF,YAAyD,EACzDC,UAA4C,EAAA;IAE5C,MAAMnO,KAAK,CAAC,8BAA8B,CAAC;AAC7C,EAAA;AACD;AAED;;;AAGG;AACG,MAAOqO,YAAa,SAAQL,gBAAgB,CAAA;EAwBhDzR,WAAAA,CAAY+R,IAKX,EAAA;;AACC,IAAA,KAAK,EAAE;IARD,IAAA,CAAAC,iBAAiB,GAAY,KAAK;AASxC,IAAA,IAAI,CAACC,UAAU,GAAG,IAAIhR,GAAG,EAAE;AAC3B,IAAA,IAAI,CAAC8I,IAAI,GAAGgI,IAAI,CAAChI,IAAI;AACrB,IAAA,IAAI,CAACtF,mBAAmB,GAAGsN,IAAI,CAACtN,mBAAmB;AACnD,IAAA,IAAI,CAACyN,MAAM,GAAG,IAAIjR,GAAG,EAAE;AACvB,IAAA,IAAI,CAACkR,kBAAkB,GAAGJ,IAAI,CAACI,kBAAkB;IACjD,IAAI,CAACC,UAAU,GAAG,CAAAC,EAAA,GAAAN,IAAI,CAACK,UAAU,MAAA,IAAA,IAAAC,EAAA,KAAA,MAAA,GAAAA,EAAA,GAAIjF,UAAU,CAAC5K,IAAI,CAAC,EAAE,CAAC;AAC1D,EAAA;EAEA,IAAY8P,UAAUA,GAAA;IACpB,OAAO;MACLC,WAAW,EAAE,IAAI,CAAC9N,mBAAmB;MACrC+N,YAAY,EAAE,IAAI,CAACC,OAAO;MAC1BC,aAAa,EAAE,IAAI,CAACC;KACrB;AACH,EAAA;AAEA;;;;;AAKG;AACHC,EAAAA,cAAcA,CAACpR,EAAU,EAAEuI,IAA2B,EAAA;IACpDjK,YAAY,CAACzD,KAAK,CAAC,oCAAoC,EAAAqD,MAAA,CAAAmT,MAAA,CAAAnT,MAAA,CAAAmT,MAAA,CAAA,EAAA,EAClD,IAAI,CAACP,UAAU,CAAA,EAAA;AAClBC,MAAAA,WAAW,EAAE/Q;OACb;IACF,IAAI,IAAI,CAACiD,mBAAmB,EAAE;AAC5B3E,MAAAA,YAAY,CAACkE,KAAK,CAChB,kFAAkF,oBAE7E,IAAI,CAACsO,UAAU,CAAA,CAErB;AACH,IAAA;IACA,IAAI,CAAC7N,mBAAmB,GAAGjD,EAAE;IAC7B,IAAI,CAACuI,IAAI,GAAGA,IAAI;AAClB,EAAA;AAEA+I,EAAAA,gBAAgBA,GAAA;IACdhT,YAAY,CAACzD,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAACiW,UAAU,CAAC;IAC5D,IAAI,CAAC7N,mBAAmB,GAAG5I,SAAS;AACtC,EAAA;AAEAkX,EAAAA,SAASA,GAAA;IACP,IAAI,IAAI,CAACtO,mBAAmB,EAAE;AAC5B,MAAA,OAAO+M,oBAAoB,CAAC5K,GAAG,CAAC,IAAI,CAACnC,mBAAmB,CAAC;AAC3D,IAAA,CAAC,MAAM;AACL,MAAA,OAAO5I,SAAS;AAClB,IAAA;AACF,EAAA;AAEAmX,EAAAA,sBAAsBA,GAAA;IACpB,OAAO,IAAI,CAACvO,mBAAmB;AACjC,EAAA;AAEAwO,EAAAA,UAAUA,GAAA;IACR,OAAO,IAAI,CAACR,OAAO;AACrB,EAAA;AAEA;;;AAGG;EACHS,aAAaA,CAACtD,KAAiB,EAAA;IAC7B,IAAI,CAAC+C,UAAU,GAAG/C,KAAK;AACzB,EAAA;AAEA;;;AAGG;EACHuD,SAASA,CAACvT,GAA4B,EAAA;IACpC,IAAI,CAACsS,MAAM,GAAGtS,GAAG;AACnB,EAAA;AAEAwT,EAAAA,cAAcA,CACZC,SAA8B,EAC9BC,QAAqE,EACrEC,QAAqE,EACrEd,OAAe,EACfe,OAAgB,EAChB5D,KAAkB,EAAA;AAElB,IAAA,IAAIA,KAAK,EAAE;AACT9P,MAAAA,YAAY,CAACD,IAAI,CAAC,6BAA6B,EAAE;AAAE+P,QAAAA;AAAK,OAAE,CAAC;MAC3D,IAAI,CAAC+C,UAAU,GAAG/C,KAAK;AACzB,IAAA;IAEA9P,YAAY,CAACzD,KAAK,CAAC,oCAAoC,EAAAqD,MAAA,CAAAmT,MAAA,CAAA;MACrDQ,SAAS;AACTI,MAAAA,aAAa,EAAEhB,OAAO;AACtB7C,MAAAA;AAAK,KAAA,EACF,IAAI,CAAC0C,UAAU,EAClB;AAEF,IAAA,IAAIkB,OAAO,IAAI,IAAI,CAACxB,iBAAiB,EAAE;AACrClS,MAAAA,YAAY,CAACzD,KAAK,CAAC,iBAAiB,oBAC/B,IAAI,CAACiW,UAAU,CAAA,CAClB;AACF,MAAA;AACF,IAAA;AAEA,IAAA,MAAMoB,WAAW,GAAGL,SAAS,KAAK,QAAQ,GAAG,IAAI,CAAC3B,cAAc,GAAG,IAAI,CAACG,cAAc;AACtF,IAAA,MAAM8B,eAAe,GAAG,IAAIC,eAAe,CAAC;AAC1CC,MAAAA,SAAS,EAAEH,WAAW,CAACxY,IAAI,CAAC,IAAI;AACjC,KAAA,CAAC;IAEF,IAAI,CAAC8W,iBAAiB,GAAG,IAAI;AAE7BsB,IAAAA,QAAQ,CACLQ,WAAW,CAACH,eAAe,CAAC,CAC5BI,MAAM,CAACR,QAAQ,CAAC,CAChBS,KAAK,CAAE1Y,CAAC,IAAI;AACXwE,MAAAA,YAAY,CAAC6F,IAAI,CAACrK,CAAC,CAAC;MACpB,IAAI,CAACgM,IAAI,CACPzC,YAAY,CAACpB,KAAK,EAClBnI,CAAC,YAAYgJ,YAAY,GACrBhJ,CAAC,GACD,IAAIgJ,YAAY,CAAChJ,CAAC,CAACqI,OAAO,EAAE9H,SAAS,EAAE,IAAI,CAAC4I,mBAAmB,CAAC,CACrE;AACH,IAAA,CAAC,CAAC,CACDwP,OAAO,CAAC,MAAK;MACZ,IAAI,CAACjC,iBAAiB,GAAG,KAAK;AAChC,IAAA,CAAC,CAAC;IACJ,IAAI,CAACS,OAAO,GAAGA,OAAO;AACxB,EAAA;EAEAyB,aAAaA,CAACC,OAAmB,EAAA;IAC/BrU,YAAY,CAACzD,KAAK,CAAC,qBAAqB,EAAAqD,MAAA,CAAAmT,MAAA,CAAAnT,MAAA,CAAAmT,MAAA,CAAA,EAAA,EAAO,IAAI,CAACP,UAAU,CAAA,EAAA;AAAE6B,MAAAA;AAAO,KAAA,CAAA,CAAG;IAC1E,IAAI,CAAC/B,UAAU,GAAG+B,OAAO;AAC3B,EAAA;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBG;AACazC,EAAAA,cAAcA,CAC5BC,YAAyD,EACzDC,UAA4C,EAAA;;;AAE5C,MAAA,IACE,CAAC,IAAI,CAACmB,SAAS,EAAE;AACjB;AACApB,MAAAA,YAAY,CAACnC,IAAI,CAAC4E,UAAU,KAAK,CAAC,EAClC;AACA,QAAA,OAAOxC,UAAU,CAACyC,OAAO,CAAC1C,YAAY,CAAC;AACzC,MAAA;MACA,MAAM2C,MAAM,GAAG,IAAI,CAACvK,IAAI,CAACwK,SAAS,EAAE;MACpC,IAAI,CAACD,MAAM,EAAE;AACX,QAAA,IAAI,CAAChN,IAAI,CACPzC,YAAY,CAACpB,KAAK,EAClB,IAAIa,YAAY,0BAAAkB,MAAA,CAEZ,IAAI,CAACf,mBACP,EAAA,YAAA,CAAA,CAAAe,MAAA,CAAa,IAAI,CAACuE,IAAI,CAACyK,kBAAkB,EAAE,CAAA,EAC3CnQ,kBAAkB,CAACoQ,UAAU,EAC7B,IAAI,CAAChQ,mBAAmB,CACzB,CACF;AACD,QAAA;AACF,MAAA;MACA,MAAM;AAAEkI,QAAAA;AAAa,OAAE,GAAG2H,MAAM;MAChC,MAAMI,QAAQ,GAAG,IAAI,CAAC3K,IAAI,CAACyK,kBAAkB,EAAE;AAE/C,MAAA,IAAI7H,aAAa,EAAE;AACjB,QAAA,MAAMgI,EAAE,GAAG,IAAI,CAACC,MAAM,CACpB,CAAAvC,EAAA,GAAAV,YAAY,CAACkD,WAAW,EAAE,CAACC,qBAAqB,mCAAI,EAAE,EACtDnD,YAAY,CAACoD,SAAS,CACvB;AACD,QAAA,IAAIC,SAAS,GAAG,IAAI,CAACC,mBAAmB,CAACtD,YAAY,CAAC;AAEtD;AACA,QAAA,MAAMuD,WAAW,GAAG,IAAI9H,UAAU,CAACuE,YAAY,CAACnC,IAAI,EAAE,CAAC,EAAEwF,SAAS,CAACzE,gBAAgB,CAAC;AAEpF;AACA,QAAA,MAAM4E,YAAY,GAAG,IAAI/H,UAAU,CAAC,CAAC,CAAC;AAEtC+H,QAAAA,YAAY,CAAC,CAAC,CAAC,GAAGnS,SAAS;AAC3BmS,QAAAA,YAAY,CAAC,CAAC,CAAC,GAAGT,QAAQ;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;QACA,IAAI;UACF,MAAMU,UAAU,GAAG,MAAMxJ,MAAM,CAACC,MAAM,CAACwJ,OAAO,CAC5C;AACEjZ,YAAAA,IAAI,EAAEqG,oBAAoB;YAC1BkS,EAAE;AACFW,YAAAA,cAAc,EAAE,IAAIlI,UAAU,CAACuE,YAAY,CAACnC,IAAI,EAAE,CAAC,EAAE0F,WAAW,CAACd,UAAU;AAC5E,WAAA,EACDzH,aAAa,EACb,IAAIS,UAAU,CAACuE,YAAY,CAACnC,IAAI,EAAEwF,SAAS,CAACzE,gBAAgB,CAAC,CAC9D;AAED,UAAA,IAAIgF,oBAAoB,GAAG,IAAInI,UAAU,CACvCgI,UAAU,CAAChB,UAAU,GAAGO,EAAE,CAACP,UAAU,GAAGe,YAAY,CAACf,UAAU,CAChE;UACDmB,oBAAoB,CAACzT,GAAG,CAAC,IAAIsL,UAAU,CAACgI,UAAU,CAAC,CAAC,CAAC;AACrDG,UAAAA,oBAAoB,CAACzT,GAAG,CAAC,IAAIsL,UAAU,CAACuH,EAAE,CAAC,EAAES,UAAU,CAAChB,UAAU,CAAC,CAAC;AACpEmB,UAAAA,oBAAoB,CAACzT,GAAG,CAACqT,YAAY,EAAEC,UAAU,CAAChB,UAAU,GAAGO,EAAE,CAACP,UAAU,CAAC,CAAC;UAE9E,IAAIY,SAAS,CAACxE,sBAAsB,EAAE;AACpC+E,YAAAA,oBAAoB,GAAGhI,SAAS,CAACgI,oBAAoB,CAAC;AACxD,UAAA;AAEA,UAAA,IAAIC,OAAO,GAAG,IAAIpI,UAAU,CAAC8H,WAAW,CAACd,UAAU,GAAGmB,oBAAoB,CAACnB,UAAU,CAAC;AACtFoB,UAAAA,OAAO,CAAC1T,GAAG,CAACoT,WAAW,CAAC;UACxBM,OAAO,CAAC1T,GAAG,CAACyT,oBAAoB,EAAEL,WAAW,CAACd,UAAU,CAAC;AAEzDzC,UAAAA,YAAY,CAACnC,IAAI,GAAGgG,OAAO,CAACC,MAAM;AAElC,UAAA,OAAO7D,UAAU,CAACyC,OAAO,CAAC1C,YAAY,CAAC;QACzC,CAAC,CAAC,OAAOrW,CAAM,EAAE;AACf;AACAwE,UAAAA,YAAY,CAACkE,KAAK,CAAC1I,CAAC,CAAC;AACvB,QAAA;AACF,MAAA,CAAC,MAAM;QACLwE,YAAY,CAACzD,KAAK,CAAC,mCAAmC,EAAE,IAAI,CAACiW,UAAU,CAAC;AACxE,QAAA,IAAI,CAAChL,IAAI,CACPzC,YAAY,CAACpB,KAAK,EAClB,IAAIa,YAAY,CAAA,qCAAA,EAEdD,kBAAkB,CAACoQ,UAAU,EAC7B,IAAI,CAAChQ,mBAAmB,CACzB,CACF;AACH,MAAA;AACF,IAAA,CAAC,CAAA;AAAA,EAAA;AAED;;;;;AAKG;AACaoN,EAAAA,cAAcA,CAC5BF,YAAyD,EACzDC,UAA4C,EAAA;;AAE5C,MAAA,IACE,CAAC,IAAI,CAACmB,SAAS,EAAE;AACjB;AACApB,MAAAA,YAAY,CAACnC,IAAI,CAAC4E,UAAU,KAAK,CAAC,EAClC;AACA,QAAA,OAAOxC,UAAU,CAACyC,OAAO,CAAC1C,YAAY,CAAC;AACzC,MAAA;MAEA,IAAI+D,qBAAqB,CAAC/D,YAAY,CAACnC,IAAI,EAAE,IAAI,CAAC4C,UAAU,CAAC,EAAE;QAC7DT,YAAY,CAACnC,IAAI,GAAGmC,YAAY,CAACnC,IAAI,CAACtR,KAAK,CACzC,CAAC,EACDyT,YAAY,CAACnC,IAAI,CAAC4E,UAAU,GAAG,IAAI,CAAChC,UAAU,CAACgC,UAAU,CAC1D;AACD,QAAA,IAAI,MAAM7C,kBAAkB,CAACI,YAAY,CAACnC,IAAI,CAAC,EAAE;UAC/C1P,YAAY,CAACzD,KAAK,CAAC,aAAa,EAAE,IAAI,CAACiW,UAAU,CAAC;AAClD,UAAA,OAAOV,UAAU,CAACyC,OAAO,CAAC1C,YAAY,CAAC;AACzC,QAAA,CAAC,MAAM;UACL7R,YAAY,CAAC6F,IAAI,CAAC,8CAA8C,EAAE,IAAI,CAAC2M,UAAU,CAAC;AAClF,UAAA;AACF,QAAA;AACF,MAAA;MACA,MAAM9C,IAAI,GAAG,IAAIpC,UAAU,CAACuE,YAAY,CAACnC,IAAI,CAAC;MAC9C,MAAMkF,QAAQ,GAAGlF,IAAI,CAACmC,YAAY,CAACnC,IAAI,CAAC4E,UAAU,GAAG,CAAC,CAAC;MAEvD,IAAI,IAAI,CAACrK,IAAI,CAAC4L,oBAAoB,CAACjB,QAAQ,CAAC,EAAE;AAC5C;AACA,QAAA;AACF,MAAA;MAEA,IAAI,IAAI,CAAC3K,IAAI,CAACwK,SAAS,CAACG,QAAQ,CAAC,EAAE;QACjC,IAAI;UACF,MAAMkB,YAAY,GAAG,MAAM,IAAI,CAACC,YAAY,CAAClE,YAAY,EAAE+C,QAAQ,CAAC;AACpE,UAAA,IAAI,CAAC3K,IAAI,CAAC+L,iBAAiB,CAACpB,QAAQ,CAAC;AACrC,UAAA,IAAIkB,YAAY,EAAE;AAChB,YAAA,OAAOhE,UAAU,CAACyC,OAAO,CAACuB,YAAY,CAAC;AACzC,UAAA;QACF,CAAC,CAAC,OAAO5R,KAAK,EAAE;UACd,IAAIA,KAAK,YAAYM,YAAY,IAAIN,KAAK,CAACO,MAAM,KAAKF,kBAAkB,CAAC0R,UAAU,EAAE;AACnF;AACA,YAAA,IAAI,IAAI,CAAChM,IAAI,CAACiM,WAAW,EAAE;cACzB,IAAI,CAAC1O,IAAI,CAACzC,YAAY,CAACpB,KAAK,EAAEO,KAAK,CAAC;AACpC,cAAA,IAAI,CAAC+F,IAAI,CAACkM,iBAAiB,CAACvB,QAAQ,CAAC;AACvC,YAAA;AACF,UAAA,CAAC,MAAM;AACL5U,YAAAA,YAAY,CAAC6F,IAAI,CAAC,uBAAuB,EAAE;AAAE3B,cAAAA;AAAK,aAAE,CAAC;AACvD,UAAA;AACF,QAAA;AACF,MAAA,CAAC,MAAM;AACL;AACAlE,QAAAA,YAAY,CAAC6F,IAAI,CAAA,kDAAA,CAAAH,MAAA,CAAoDkP,QAAQ,CAAE,CAAC;AAChF,QAAA,IAAI,CAACpN,IAAI,CACPzC,YAAY,CAACpB,KAAK,EAClB,IAAIa,YAAY,CAAA,uBAAA,CAAAkB,MAAA,CACUkP,QAAQ,EAAA,mBAAA,CAAA,CAAAlP,MAAA,CAAoB,IAAI,CAACf,mBAAmB,CAAA,EAC5EJ,kBAAkB,CAACoQ,UAAU,EAC7B,IAAI,CAAChQ,mBAAmB,CACzB,CACF;AACD,QAAA,IAAI,CAACsF,IAAI,CAACkM,iBAAiB,CAACvB,QAAQ,CAAC;AACvC,MAAA;AACF,IAAA,CAAC,CAAA;AAAA,EAAA;AAED;;;AAGG;AACWmB,EAAAA,YAAYA,CAAAK,cAAA,EAAAC,UAAA,EAAA;wDACxBxE,YAAyD,EACzD+C,QAAgB,EAAA;AAAA,MAAA,IAAA0B,KAAA,GAAA,IAAA;AAAA,MAAA,IAChBC,eAAA,GAAA7a,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAAsCK,SAAS;MAAA,IAC/Cya,WAAA,GAAA9a,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAAoC;AAAE+a,QAAAA,YAAY,EAAE;OAAG;MAAA,OAAA,aAAA;;QAEvD,MAAMjC,MAAM,GAAG8B,KAAI,CAACrM,IAAI,CAACwK,SAAS,CAACG,QAAQ,CAAC;AAC5C,QAAA,IAAI,CAAC4B,WAAW,CAAC3J,aAAa,IAAI,CAAC2H,MAAM,EAAE;UACzC,MAAM,IAAI/V,SAAS,CAAA,4CAAA,CAAAiH,MAAA,CAA8C4Q,KAAI,CAAC3R,mBAAmB,CAAE,CAAC;AAC9F,QAAA;AACA,QAAA,IAAIuQ,SAAS,GAAGoB,KAAI,CAACnB,mBAAmB,CAACtD,YAAY,CAAC;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;QAEA,IAAI;AACF,UAAA,MAAMuD,WAAW,GAAG,IAAI9H,UAAU,CAACuE,YAAY,CAACnC,IAAI,EAAE,CAAC,EAAEwF,SAAS,CAACzE,gBAAgB,CAAC;UACpF,IAAIiG,aAAa,GAAG,IAAIpJ,UAAU,CAChCuE,YAAY,CAACnC,IAAI,EACjB0F,WAAW,CAAChZ,MAAM,EAClByV,YAAY,CAACnC,IAAI,CAAC4E,UAAU,GAAGc,WAAW,CAAChZ,MAAM,CAClD;UACD,IAAI8Y,SAAS,CAACxE,sBAAsB,IAAIzD,mBAAmB,CAACyJ,aAAa,CAAC,EAAE;AAC1EA,YAAAA,aAAa,GAAGvJ,SAAS,CAACuJ,aAAa,CAAC;AACxC,YAAA,MAAMC,QAAQ,GAAG,IAAIrJ,UAAU,CAAC8H,WAAW,CAACd,UAAU,GAAGoC,aAAa,CAACpC,UAAU,CAAC;AAClFqC,YAAAA,QAAQ,CAAC3U,GAAG,CAACoT,WAAW,CAAC;YACzBuB,QAAQ,CAAC3U,GAAG,CAAC0U,aAAa,EAAEtB,WAAW,CAACd,UAAU,CAAC;AACnDzC,YAAAA,YAAY,CAACnC,IAAI,GAAGiH,QAAQ,CAAChB,MAAM;AACrC,UAAA;AAEA,UAAA,MAAMN,YAAY,GAAG,IAAI/H,UAAU,CAACuE,YAAY,CAACnC,IAAI,EAAEmC,YAAY,CAACnC,IAAI,CAAC4E,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC;AAE3F,UAAA,MAAMsC,QAAQ,GAAGvB,YAAY,CAAC,CAAC,CAAC;UAChC,MAAMR,EAAE,GAAG,IAAIvH,UAAU,CACvBuE,YAAY,CAACnC,IAAI,EACjBmC,YAAY,CAACnC,IAAI,CAAC4E,UAAU,GAAGsC,QAAQ,GAAGvB,YAAY,CAACf,UAAU,EACjEsC,QAAQ,CACT;AAED,UAAA,MAAMC,eAAe,GAAGzB,WAAW,CAACd,UAAU;AAC9C,UAAA,MAAMwC,gBAAgB,GACpBjF,YAAY,CAACnC,IAAI,CAAC4E,UAAU,IAC3Bc,WAAW,CAACd,UAAU,GAAGsC,QAAQ,GAAGvB,YAAY,CAACf,UAAU,CAAC;UAE/D,MAAMyC,SAAS,GAAG,MAAMjL,MAAM,CAACC,MAAM,CAACiL,OAAO,CAC3C;AACE1a,YAAAA,IAAI,EAAEqG,oBAAoB;YAC1BkS,EAAE;AACFW,YAAAA,cAAc,EAAE,IAAIlI,UAAU,CAACuE,YAAY,CAACnC,IAAI,EAAE,CAAC,EAAE0F,WAAW,CAACd,UAAU;WAC5E,EACD,CAAA/B,EAAA,GAAAiE,WAAW,CAAC3J,aAAa,mCAAI2H,MAAO,CAAC3H,aAAa,EAClD,IAAIS,UAAU,CAACuE,YAAY,CAACnC,IAAI,EAAEmH,eAAe,EAAEC,gBAAgB,CAAC,CACrE;AAED,UAAA,MAAMpB,OAAO,GAAG,IAAIlJ,WAAW,CAAC4I,WAAW,CAACd,UAAU,GAAGyC,SAAS,CAACzC,UAAU,CAAC;AAC9E,UAAA,MAAMqC,QAAQ,GAAG,IAAIrJ,UAAU,CAACoI,OAAO,CAAC;AAExCiB,UAAAA,QAAQ,CAAC3U,GAAG,CAAC,IAAIsL,UAAU,CAACuE,YAAY,CAACnC,IAAI,EAAE,CAAC,EAAE0F,WAAW,CAACd,UAAU,CAAC,CAAC;AAC1EqC,UAAAA,QAAQ,CAAC3U,GAAG,CAAC,IAAIsL,UAAU,CAACyJ,SAAS,CAAC,EAAE3B,WAAW,CAACd,UAAU,CAAC;UAE/DzC,YAAY,CAACnC,IAAI,GAAGgG,OAAO;AAE3B,UAAA,OAAO7D,YAAY;QACrB,CAAC,CAAC,OAAO3N,KAAU,EAAE;AACnB,UAAA,IAAIoS,KAAI,CAACjE,kBAAkB,CAAC9O,iBAAiB,GAAG,CAAC,EAAE;YACjD,IAAIiT,WAAW,CAACC,YAAY,GAAGH,KAAI,CAACjE,kBAAkB,CAAC9O,iBAAiB,EAAE;cACxEvD,YAAY,CAACzD,KAAK,CAAA,yBAAA,CAAAmJ,MAAA,CACU8Q,WAAW,CAACC,YAAY,EAAA,MAAA,CAAA,CAAA/Q,MAAA,CAChD4Q,KAAI,CAACjE,kBAAkB,CAAC9O,iBAC1B,EAAA,aAAA,CAAA,CAAAmC,MAAA,CAAcmM,YAAY,YAAYoF,oBAAoB,GAAG,OAAO,GAAG,OAAO,CAAE,CACjF;AAED,cAAA,IAAIC,eAAmC;AACvC,cAAA,IAAIC,aAAwC;cAC5C,IAAI,CAACZ,eAAe,KAAA,IAAA,IAAfA,eAAe,KAAA,MAAA,GAAfA,eAAe,GAAI/B,MAAM,MAAM8B,KAAI,CAACrM,IAAI,CAACwK,SAAS,CAACG,QAAQ,CAAC,EAAE;AACjE;AACA;gBACAuC,aAAa,GAAG,MAAMb,KAAI,CAACrM,IAAI,CAACmN,UAAU,CAACxC,QAAQ,EAAE,KAAK,CAAC;AAE3DsC,gBAAAA,eAAe,GAAG,MAAMxK,UAAU,CAChCyK,aAAa,CAACE,SAAS,EACvBf,KAAI,CAACjE,kBAAkB,CAAC/O,WAAW,CACpC;AACH,cAAA;AAEA,cAAA,MAAMkI,KAAK,GAAG,MAAM8K,KAAI,CAACP,YAAY,CAAClE,YAAY,EAAE+C,QAAQ,EAAE2B,eAAe,IAAI/B,MAAM,EAAE;AACvFiC,gBAAAA,YAAY,EAAED,WAAW,CAACC,YAAY,GAAG,CAAC;AAC1C5J,gBAAAA,aAAa,EAAEqK,eAAe,KAAA,IAAA,IAAfA,eAAe,KAAA,MAAA,GAAA,MAAA,GAAfA,eAAe,CAAErK;AACjC,eAAA,CAAC;cACF,IAAIrB,KAAK,IAAI0L,eAAe,EAAE;AAC5B;AACA;gBACA,IAAI,CAACX,eAAe,KAAA,IAAA,IAAfA,eAAe,KAAA,MAAA,GAAfA,eAAe,GAAI/B,MAAM,MAAM8B,KAAI,CAACrM,IAAI,CAACwK,SAAS,CAACG,QAAQ,CAAC,EAAE;kBACjE0B,KAAI,CAACrM,IAAI,CAACqN,SAAS,CAACJ,eAAe,EAAEtC,QAAQ,EAAEuC,aAAa,CAAC;AAC7D;AACAb,kBAAAA,KAAI,CAACrM,IAAI,CAACsN,kBAAkB,CAAC3C,QAAQ,CAAC;AACxC,gBAAA;AACF,cAAA;AACA,cAAA,OAAOpJ,KAAK;AACd,YAAA,CAAC,MAAM;AACL;;;;AAIG;AAEHxL,cAAAA,YAAY,CAAC6F,IAAI,CAAC,mCAAmC,CAAC;AACtD,cAAA,MAAM,IAAIrB,YAAY,CAAA,oCAAA,CAAAkB,MAAA,CACiB4Q,KAAI,CAAC3R,mBAAmB,CAAA,EAC7DJ,kBAAkB,CAAC0R,UAAU,EAC7BK,KAAI,CAAC3R,mBAAmB,CACzB;AACH,YAAA;AACF,UAAA,CAAC,MAAM;AACL,YAAA,MAAM,IAAIH,YAAY,CAAA,qBAAA,CAAAkB,MAAA,CACExB,KAAK,CAACL,OAAO,CAAA,EACnCU,kBAAkB,CAAC0R,UAAU,EAC7BK,KAAI,CAAC3R,mBAAmB,CACzB;AACH,UAAA;AACF,QAAA;MACF,CAAC,EAAA;IAAA,CAAA,CAAA;AAAA,EAAA;AAED;;;;;;;;;;;;;;;;;;AAkBG;AACKmQ,EAAAA,MAAMA,CAACE,qBAA6B,EAAEC,SAAiB,EAAA;;AAC7D,IAAA,MAAMJ,EAAE,GAAG,IAAIrI,WAAW,CAACtJ,SAAS,CAAC;AACrC,IAAA,MAAMsU,MAAM,GAAG,IAAIC,QAAQ,CAAC5C,EAAE,CAAC;AAE/B;IACA,IAAI,CAAC,IAAI,CAAC1C,UAAU,CAACuF,GAAG,CAAC1C,qBAAqB,CAAC,EAAE;AAC/C;AACA,MAAA,IAAI,CAAC7C,UAAU,CAACnQ,GAAG,CAACgT,qBAAqB,EAAE2C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC;AAChF,IAAA;IAEA,MAAMC,SAAS,GAAG,CAAAvF,EAAA,GAAA,IAAI,CAACJ,UAAU,CAACrL,GAAG,CAACkO,qBAAqB,CAAC,MAAA,IAAA,IAAAzC,EAAA,KAAA,MAAA,GAAAA,EAAA,GAAI,CAAC;AAEjEiF,IAAAA,MAAM,CAACO,SAAS,CAAC,CAAC,EAAE/C,qBAAqB,CAAC;AAC1CwC,IAAAA,MAAM,CAACO,SAAS,CAAC,CAAC,EAAE9C,SAAS,CAAC;IAC9BuC,MAAM,CAACO,SAAS,CAAC,CAAC,EAAE9C,SAAS,GAAI6C,SAAS,GAAG,MAAO,CAAC;IAErD,IAAI,CAAC3F,UAAU,CAACnQ,GAAG,CAACgT,qBAAqB,EAAE8C,SAAS,GAAG,CAAC,CAAC;AAEzD,IAAA,OAAOjD,EAAE;AACX,EAAA;EAEQM,mBAAmBA,CAAC3J,KAAkD,EAAA;;AAI5E;AACA,IAAA,IAAI,CAACD,YAAY,CAACC,KAAK,CAAC,EAAE;MACxB,OAAO;QAAEiF,gBAAgB,EAAE5N,iBAAiB,CAACG,KAAK;AAAE0N,QAAAA,sBAAsB,EAAE;OAAO;AACrF,IAAA;AAEA;IACA,MAAMF,aAAa,GAAG,CAAA+B,EAAA,GAAA,IAAI,CAACyF,aAAa,CAACxM,KAAK,CAAC,MAAA,IAAA,IAAA+G,EAAA,KAAA,MAAA,GAAAA,EAAA,GAAI,IAAI,CAACM,UAAU;AAClE,IAAA,IAAIrC,aAAa,KAAK,IAAI,CAACA,aAAa,EAAE;MACxCxQ,YAAY,CAACzD,KAAK,CAAC,0BAA0B,EAAAqD,MAAA,CAAAmT,MAAA,CAAA;QAC3CvC,aAAa;QACbyH,QAAQ,EAAE,IAAI,CAACzH;AAAa,OAAA,EACzB,IAAI,CAACgC,UAAU,EAClB;MACF,IAAI,CAAChC,aAAa,GAAGA,aAAa;AACpC,IAAA;AAEA;IACA,IAAIA,aAAa,KAAK,KAAK,EAAE;AAC3B,MAAA,MAAM,IAAI7M,KAAK,CAAA,EAAA,CAAA+B,MAAA,CAAI8K,aAAa,oDAAiD,CAAC;AACpF,IAAA;AAEA;IACA,IAAIA,aAAa,KAAK,KAAK,EAAE;MAC3B,OAAO;AAAEC,QAAAA,gBAAgB,EAAE5N,iBAAiB,CAAC2I,KAAK,CAAC/D,IAAI,CAAC;AAAEiJ,QAAAA,sBAAsB,EAAE;OAAO;AAC3F,IAAA;IACA,IAAIF,aAAa,KAAK,KAAK,EAAE;MAC3B,OAAO;AAAEC,QAAAA,gBAAgB,EAAE,CAAC;AAAEC,QAAAA,sBAAsB,EAAE;OAAO;AAC/D,IAAA;AAEA;IACA,IAAI;AACF,MAAA,MAAMH,UAAU,GACdC,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,MAAM,GAAGA,aAAa,GAAGzU,SAAS;AAClF,MAAA,MAAMmc,UAAU,GAAG5H,yBAAyB,CAAC,IAAIhD,UAAU,CAAC9B,KAAK,CAACkE,IAAI,CAAC,EAAEa,UAAU,CAAC;MAEpF,IAAI2H,UAAU,CAACxH,sBAAsB,EAAE;QACrC,OAAO;UACLD,gBAAgB,EAAEyH,UAAU,CAACzH,gBAAgB;AAC7CC,UAAAA,sBAAsB,EAAE;SACzB;AACH,MAAA;IACF,CAAC,CAAC,OAAOlV,CAAC,EAAE;MACVwE,YAAY,CAACzD,KAAK,CAAC,sDAAsD,EAAAqD,MAAA,CAAAmT,MAAA,CAAA;AACvE7O,QAAAA,KAAK,EAAE1I;AAAC,OAAA,EACL,IAAI,CAACgX,UAAU,EAClB;AACJ,IAAA;AAEA;IACA,OAAO;AAAE/B,MAAAA,gBAAgB,EAAE5N,iBAAiB,CAAC2I,KAAK,CAAC/D,IAAI,CAAC;AAAEiJ,MAAAA,sBAAsB,EAAE;KAAO;AAC3F,EAAA;AAEA;;AAEG;EACKsH,aAAaA,CAACxM,KAA2B,EAAA;AAC/C,IAAA,IAAI,IAAI,CAAC4G,MAAM,CAAC+F,IAAI,KAAK,CAAC,EAAE;AAC1B,MAAA,OAAOpc,SAAS;AAClB,IAAA;IACA,MAAMqc,WAAW,GAAG5M,KAAK,CAACuJ,WAAW,EAAE,CAACqD,WAAW;AACnD,IAAA,MAAMtI,KAAK,GAAGsI,WAAW,GAAG,IAAI,CAAChG,MAAM,CAACtL,GAAG,CAACsR,WAAW,CAAC,GAAGrc,SAAS;AACpE,IAAA,OAAO+T,KAAK;AACd,EAAA;AACD;AAED;;;;AAIG;AACG,SAAU8F,qBAAqBA,CAAC1I,SAAsB,EAAEmL,YAAwB,EAAA;AACpF,EAAA,IAAIA,YAAY,CAAC/D,UAAU,KAAK,CAAC,EAAE;AACjC,IAAA,OAAO,KAAK;AACd,EAAA;AACA,EAAA,MAAMe,YAAY,GAAG,IAAI/H,UAAU,CACjCJ,SAAS,CAAC9O,KAAK,CAAC8O,SAAS,CAACoH,UAAU,GAAG+D,YAAY,CAAC/D,UAAU,CAAC,CAChE;AACD,EAAA,OAAO+D,YAAY,CAACC,KAAK,CAAC,CAACrS,KAAK,EAAE0E,KAAK,KAAK1E,KAAK,KAAKoP,YAAY,CAAC1K,KAAK,CAAC,CAAC;AAC5E;;AC1oBA;AACA;AAEA;;;;;;;AAOG;AACG,MAAO4N,qBAAsB,SAASrS,0BAA4E,CAAA;AAatH;;AAEG;EACH,IAAIgQ,WAAWA,GAAA;IACb,OAAO,CAAC,IAAI,CAACL,oBAAoB,CAAC,IAAI,CAAC2C,eAAe,CAAC;AACzD,EAAA;AAEAtY,EAAAA,WAAAA,CAAYyE,mBAA2B,EAAE0N,kBAAsC,EAAA;AAC7E,IAAA,KAAK,EAAE;IACP,IAAI,CAACmG,eAAe,GAAG,CAAC;IACxB,IAAInG,kBAAkB,CAAC5O,WAAW,GAAG,CAAC,IAAI4O,kBAAkB,CAAC5O,WAAW,GAAG,GAAG,EAAE;AAC9E,MAAA,MAAM,IAAIhF,SAAS,CAAC,4CAA4C,CAAC;AACnE,IAAA;AACA,IAAA,IAAI,CAACga,aAAa,GAAG,IAAIhW,KAAK,CAAC4P,kBAAkB,CAAC5O,WAAW,CAAC,CAACiV,IAAI,CAAC3c,SAAS,CAAC;AAC9E,IAAA,IAAI,CAAC4c,uBAAuB,GAAG,IAAIlW,KAAK,CAAC4P,kBAAkB,CAAC5O,WAAW,CAAC,CAACiV,IAAI,CAAC,CAAC,CAAC;IAChF,IAAI,CAACrG,kBAAkB,GAAGA,kBAAkB;AAC5C,IAAA,IAAI,CAACuG,iBAAiB,GAAG,IAAIzX,GAAG,EAAE;IAClC,IAAI,CAACwD,mBAAmB,GAAGA,mBAAmB;AAChD,EAAA;AAEA;;;;AAIG;EACHkR,oBAAoBA,CAACjB,QAAgB,EAAA;AACnC,IAAA,OACE,IAAI,CAACvC,kBAAkB,CAAC7O,gBAAgB,IAAI,CAAC,IAC7C,IAAI,CAACmV,uBAAuB,CAAC/D,QAAQ,CAAC,GAAG,IAAI,CAACvC,kBAAkB,CAAC7O,gBAAgB;AAErF,EAAA;AAEA;;;;AAIG;AACH2S,EAAAA,iBAAiBA,GAAwC;AAAA,IAAA,IAAvCvB,QAAA,GAAAlZ,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAAmB,IAAI,CAAC8c,eAAe;AACvD,IAAA,IAAI,IAAI,CAACnG,kBAAkB,CAAC7O,gBAAgB,GAAG,CAAC,EAAE;AAChD,MAAA;AACF,IAAA;AAEA,IAAA,IAAI,CAACmV,uBAAuB,CAAC/D,QAAQ,CAAC,IAAI,CAAC;AAE3C,IAAA,IAAI,IAAI,CAAC+D,uBAAuB,CAAC/D,QAAQ,CAAC,GAAG,IAAI,CAACvC,kBAAkB,CAAC7O,gBAAgB,EAAE;AACrFxD,MAAAA,YAAY,CAAC6F,IAAI,CAAA,UAAA,CAAAH,MAAA,CACJ,IAAI,CAACf,mBAAmB,EAAA,YAAA,CAAA,CAAAe,MAAA,CAAakP,QAAQ,gCAA6B,CACtF;AACH,IAAA;AACF,EAAA;AAEA;;;;AAIG;AACHoB,EAAAA,iBAAiBA,GAAwC;AAAA,IAAA,IAAvCpB,QAAA,GAAAlZ,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAAmB,IAAI,CAAC8c,eAAe;AACvD,IAAA,IAAI,CAACK,cAAc,CAACjE,QAAQ,CAAC;AAC/B,EAAA;AAEA;;;;;AAKG;EACHiE,cAAcA,CAACjE,QAAiB,EAAA;IAC9B,IAAIA,QAAQ,KAAK7Y,SAAS,EAAE;AAC1B,MAAA,IAAI,CAAC4c,uBAAuB,CAACD,IAAI,CAAC,CAAC,CAAC;AACtC,IAAA,CAAC,MAAM;AACL,MAAA,IAAI,CAACC,uBAAuB,CAAC/D,QAAQ,CAAC,GAAG,CAAC;AAC5C,IAAA;AACF,EAAA;AAEA;;;;;;AAMG;EACHwC,UAAUA,CAACxC,QAAiB,EAAe;AAAA,IAAA,IAAbkE,MAAM,GAAApd,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI;AACzC,IAAA,MAAM8c,eAAe,GAAG5D,QAAQ,KAAA,IAAA,IAARA,QAAQ,KAAA,MAAA,GAARA,QAAQ,GAAI,IAAI,CAACF,kBAAkB,EAAE;IAE7D,MAAMqE,eAAe,GAAG,IAAI,CAACH,iBAAiB,CAAC9R,GAAG,CAAC0R,eAAe,CAAC;AACnE,IAAA,IAAI,OAAOO,eAAe,KAAK,WAAW,EAAE;AAC1C,MAAA,OAAOA,eAAe;AACxB,IAAA;IACA,MAAMC,cAAc,GAAG,IAAI3Y,OAAO,CAAgB,CAAOC,OAAO,EAAEwK,MAAM,KAAIvI,SAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,aAAA;MAC1E,IAAI;AACF,QAAA,MAAMiS,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC+D,eAAe,CAAC;QAC9C,IAAI,CAAChE,MAAM,EAAE;UACX,MAAM,IAAI/V,SAAS,CAAA,2DAAA,CAAAiH,MAAA,CAC2C,IAAI,CAACf,mBAAmB,CAAE,CACvF;AACH,QAAA;AACA,QAAA,MAAMsU,eAAe,GAAGzE,MAAM,CAAC7H,QAAQ;AACvC,QAAA,MAAMuM,QAAQ,GAAG,MAAMnM,OAAO,CAACkM,eAAe,EAAE,IAAI,CAAC5G,kBAAkB,CAAC/O,WAAW,CAAC;AACpF,QAAA,MAAM6V,WAAW,GAAG,MAAM1N,SAAS,CAACyN,QAAQ,EAAED,eAAe,CAACrN,SAAS,CAACtP,IAAI,EAAE,QAAQ,CAAC;AACvF,QAAA,MAAM6a,aAAa,GAAkB;UACnC+B,QAAQ;AACR7B,UAAAA,SAAS,EAAE8B;SACZ;AACD,QAAA,IAAIL,MAAM,EAAE;AACV;UACA,MAAM,IAAI,CAACM,kBAAkB,CAACD,WAAW,EAAEX,eAAe,EAAErB,aAAa,CAAC;AAC5E,QAAA;QACA7W,OAAO,CAAC6W,aAAa,CAAC;MACxB,CAAC,CAAC,OAAO3b,CAAC,EAAE;QACVsP,MAAM,CAACtP,CAAC,CAAC;AACX,MAAA,CAAC,SAAS;AACR,QAAA,IAAI,CAACod,iBAAiB,CAACvW,MAAM,CAACmW,eAAe,CAAC;AAChD,MAAA;AACF,IAAA,CAAC,CAAA,CAAC;IACF,IAAI,CAACI,iBAAiB,CAAC5W,GAAG,CAACwW,eAAe,EAAEQ,cAAc,CAAC;AAC3D,IAAA,OAAOA,cAAc;AACvB,EAAA;AAEA;;;;;AAKG;EACGF,MAAMA,CAAAO,UAAA,EAAA;wDAAC1M,QAAmB,EAAA;AAAA,MAAA,IAAA2J,KAAA,GAAA,IAAA;AAAA,MAAA,IAAE1B,QAAQ,GAAAlZ,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAAG,CAAC;MAAA,OAAA,aAAA;AAC5C,QAAA,MAAM4a,KAAI,CAAC8C,kBAAkB,CAACzM,QAAQ,EAAEiI,QAAQ,CAAC;AACjD0B,QAAAA,KAAI,CAACuC,cAAc,CAACjE,QAAQ,CAAC;MAC/B,CAAC,EAAA;IAAA,CAAA,CAAA;AAAA,EAAA;AAED;;;;;AAKG;AACGwE,EAAAA,kBAAkBA,CAAAC,UAAA,EAAAhD,UAAA,EAAA;AACtB,IAAA,OAAA9T,SAAA,CAAA,IAAA,EAAA7G,SAAA,EAAA,MAAA,EAAA,UAAAiR,QAAmB,EACnBiI,QAAgB,EAAA;AAAA,MAAA,IAAA0E,MAAA,GAAA,IAAA;AAAA,MAAA,IAChBC,eAAA,GAAA7d,SAAA,CAAAU,MAAA,GAAA,CAAA,IAAAV,SAAA,CAAA,CAAA,CAAA,KAAAK,SAAA,GAAAL,SAAA,CAAA,CAAA,CAAA,GAAwC,IAAI;MAAA,OAAA,aAAA;AAE5C,QAAA,MAAM8Y,MAAM,GAAG,MAAM9H,UAAU,CAACC,QAAQ,EAAE2M,MAAI,CAACjH,kBAAkB,CAAC/O,WAAW,CAAC;AAC9E,QAAA,MAAMkW,QAAQ,GAAG5E,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG0E,MAAI,CAACb,aAAa,CAACrc,MAAM,GAAGkd,MAAI,CAACd,eAAe;AAC5FxY,QAAAA,YAAY,CAACzD,KAAK,CAAA,6BAAA,CAAAmJ,MAAA,CAA+BkP,QAAQ,CAAA,EAAI;UAC3D/I,KAAK,EAAEc,QAAQ,CAAC8M,MAAM;UACtB7N,SAAS,EAAEe,QAAQ,CAACf,SAAS;AAC7BtI,UAAAA,WAAW,EAAEgW,MAAI,CAACjH,kBAAkB,CAAC/O;AACtC,SAAA,CAAC;QACFgW,MAAI,CAAChC,SAAS,CAAC9C,MAAM,EAAEgF,QAAQ,EAAED,eAAe,CAAC;QACjD,IAAIC,QAAQ,IAAI,CAAC,EAAEF,MAAI,CAACd,eAAe,GAAGgB,QAAQ;MACpD,CAAC,EAAA;IAAA,CAAA,CAAA;AAAA,EAAA;AAEDlC,EAAAA,SAASA,CAAC9C,MAAc,EAAEI,QAAgB,EAA8C;AAAA,IAAA,IAA5C2E,sFAAwC,IAAI;AACtF,IAAA,IAAI,CAACd,aAAa,CAAC7D,QAAQ,GAAG,IAAI,CAAC6D,aAAa,CAACrc,MAAM,CAAC,GAAGoY,MAAM;AAEjE,IAAA,IAAI+E,eAAe,EAAE;AACnB,MAAA,IAAI,CAAC/R,IAAI,CAAC3C,eAAe,CAAC6U,YAAY,EAAEH,eAAe,EAAE,IAAI,CAAC5U,mBAAmB,EAAEiQ,QAAQ,CAAC;AAC9F,IAAA;AACF,EAAA;EAEM2C,kBAAkBA,CAAC5M,KAAa,EAAA;;MACpC,IAAI,CAAC6N,eAAe,GAAG7N,KAAK,GAAG,IAAI,CAAC8N,aAAa,CAACrc,MAAM;AACxD,MAAA,IAAI,CAACyc,cAAc,CAAClO,KAAK,CAAC;AAC5B,IAAA,CAAC,CAAA;AAAA,EAAA;AAED+J,EAAAA,kBAAkBA,GAAA;IAChB,OAAO,IAAI,CAAC8D,eAAe;AAC7B,EAAA;AAEA;;;;AAIG;EACH/D,SAASA,CAACG,QAAiB,EAAA;AACzB,IAAA,OAAO,IAAI,CAAC6D,aAAa,CAAC7D,QAAQ,KAAA,IAAA,IAARA,QAAQ,KAAA,MAAA,GAARA,QAAQ,GAAI,IAAI,CAAC4D,eAAe,CAAC;AAC7D,EAAA;AACD;;AC5LD,MAAMmB,mBAAmB,GAAmB,EAAE;AAC9C,MAAMC,eAAe,GAAuC,IAAIzY,GAAG,EAAE;AACrE,IAAI0Y,gBAAmD;AACvD,IAAIC,YAAY,GAAG,IAAI7Y,UAAU,EAAE;AAEnC,IAAI8Y,mBAAmB,GAAY,KAAK;AAExC,IAAIC,YAAY,GAAY,KAAK;AAEjC,IAAI1H,UAAkC;AAEtC,IAAID,kBAAkB,GAAuBjP,qBAAqB;AAElE,IAAIgP,MAAM,GAA4B,IAAIjR,GAAG,EAAE;AAE/CnB,YAAY,CAACpB,eAAe,CAAC,MAAM,CAAC;AAEpCqb,SAAS,GAAIC,EAAE,IAAI;AACjBJ,EAAAA,YAAY,CAACvY,GAAG,CAAC,MAAWgB,SAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,aAAA;IAC1B,MAAM;MAAE4X,IAAI;AAAEzK,MAAAA;KAAM,GAAsBwK,EAAE,CAACxK,IAAI;AAEjD,IAAA,QAAQyK,IAAI;AACV,MAAA,KAAK,MAAM;AACTna,QAAAA,YAAY,CAACtB,QAAQ,CAACgR,IAAI,CAAC0K,QAAQ,CAAC;AACpCpa,QAAAA,YAAY,CAACD,IAAI,CAAC,oBAAoB,CAAC;QACvCsS,kBAAkB,GAAG3C,IAAI,CAAC2C,kBAAkB;AAC5C2H,QAAAA,YAAY,GAAG,CAAC,CAACtK,IAAI,CAAC2C,kBAAkB,CAAChP,SAAS;AAClD;AACA,QAAA,MAAMgX,MAAM,GAAY;AACtBF,UAAAA,IAAI,EAAE,SAAS;AACfzK,UAAAA,IAAI,EAAE;AAAE4K,YAAAA,OAAO,EAAEP;AAAmB;SACrC;QACDQ,WAAW,CAACF,MAAM,CAAC;AACnB,QAAA;AACF,MAAA,KAAK,QAAQ;QACXG,oBAAoB,CAAC9K,IAAI,CAAC4K,OAAO,EAAE5K,IAAI,CAAC/K,mBAAmB,CAAC;AAC5D3E,QAAAA,YAAY,CAACD,IAAI,CAAA,kCAAA,CAAA2F,MAAA,CACoBgK,IAAI,CAAC/K,mBAAmB,EAAA,MAAA,CAAA,CAAAe,MAAA,CAAOgK,IAAI,CAAC4K,OAAO,CAAE,CACjF;AACD;AACAC,QAAAA,WAAW,CAACL,EAAE,CAACxK,IAAI,CAAC;AACpB,QAAA;AACF,MAAA,KAAK,QAAQ;QACX,IAAI+K,OAAO,GAAGC,eAAe,CAAChL,IAAI,CAAC/K,mBAAmB,EAAE+K,IAAI,CAACiD,OAAO,CAAC;QACrE8H,OAAO,CAACnH,cAAc,CACpB6G,IAAI,EACJzK,IAAI,CAACiL,cAAc,EACnBjL,IAAI,CAACkL,cAAc,EACnBlL,IAAI,CAACiD,OAAO,EACZjD,IAAI,CAACgE,OAAO,EACZhE,IAAI,CAACI,KAAK,CACX;AACD,QAAA;AACF,MAAA,KAAK,QAAQ;QACX,IAAI+K,UAAU,GAAGH,eAAe,CAAChL,IAAI,CAAC/K,mBAAmB,EAAE+K,IAAI,CAACiD,OAAO,CAAC;QACxEkI,UAAU,CAACvH,cAAc,CACvB6G,IAAI,EACJzK,IAAI,CAACiL,cAAc,EACnBjL,IAAI,CAACkL,cAAc,EACnBlL,IAAI,CAACiD,OAAO,EACZjD,IAAI,CAACgE,OAAO,EACZhE,IAAI,CAACI,KAAK,CACX;AACD,QAAA;AACF,MAAA,KAAK,QAAQ;AACX,QAAA,IAAIkK,YAAY,EAAE;UAChB,MAAMc,YAAY,CAACpL,IAAI,CAAC5M,GAAG,EAAE4M,IAAI,CAACkF,QAAQ,CAAC;AAC7C,QAAA,CAAC,MAAM,IAAIlF,IAAI,CAAC/K,mBAAmB,EAAE;AACnC3E,UAAAA,YAAY,CAACD,IAAI,CAAA,6BAAA,CAAA2F,MAAA,CACegK,IAAI,CAAC/K,mBAAmB,EAAA,SAAA,CAAA,CAAAe,MAAA,CAAUgK,IAAI,CAACkF,QAAQ,CAAE,CAChF;AACD,UAAA,MAAMmG,wBAAwB,CAACrL,IAAI,CAAC/K,mBAAmB,CAAC,CAACmU,MAAM,CAACpJ,IAAI,CAAC5M,GAAG,EAAE4M,IAAI,CAACkF,QAAQ,CAAC;AAC1F,QAAA,CAAC,MAAM;AACL5U,UAAAA,YAAY,CAACkE,KAAK,CAAC,iEAAiE,CAAC;AACvF,QAAA;AACA,QAAA;AACF,MAAA,KAAK,iBAAiB;QACpB8W,uBAAuB,CAACtL,IAAI,CAACiD,OAAO,EAAEjD,IAAI,CAAC/K,mBAAmB,CAAC;AAC/D,QAAA;AACF,MAAA,KAAK,aAAa;AAChB+V,QAAAA,eAAe,CAAChL,IAAI,CAAC/K,mBAAmB,EAAE+K,IAAI,CAACiD,OAAO,CAAC,CAACS,aAAa,CAAC1D,IAAI,CAACI,KAAK,CAAC;AACjF9P,QAAAA,YAAY,CAACD,IAAI,CAAC,eAAe,EAAE;UACjC4E,mBAAmB,EAAE+K,IAAI,CAAC/K,mBAAmB;UAC7CgO,OAAO,EAAEjD,IAAI,CAACiD,OAAO;UACrB7C,KAAK,EAAEJ,IAAI,CAACI;AACb,SAAA,CAAC;AACF,QAAA;AACF,MAAA,KAAK,WAAW;AACd;QACAsC,MAAM,GAAG1C,IAAI,CAAC5P,GAAG;AACjB6Z,QAAAA,mBAAmB,CAACsB,OAAO,CAAEC,EAAE,IAAI;UACjC,IAAIA,EAAE,CAAChI,sBAAsB,EAAE,KAAKxD,IAAI,CAAC/K,mBAAmB,EAAE;AAC5DuW,YAAAA,EAAE,CAAC7H,SAAS,CAAC3D,IAAI,CAAC5P,GAAG,CAAC;AACxB,UAAA;AACF,QAAA,CAAC,CAAC;AACF,QAAA;AACF,MAAA,KAAK,gBAAgB;QACnBqb,oBAAoB,CAACzL,IAAI,CAAC;AAC1B,QAAA;AACF,MAAA,KAAK,eAAe;AAClB0L,QAAAA,gBAAgB,CAAC1L,IAAI,CAAC2E,OAAO,CAAC;AAC9B,QAAA;AAGJ;AACF,EAAA,CAAC,CAAA,CAAC;AACJ,CAAC;AAED,SAAe8G,oBAAoBA,CAACzL,IAAmC,EAAA;;AACrE,IAAA,IAAIsK,YAAY,EAAE;AAChB,MAAA,MAAMqB,UAAU,GAAGC,mBAAmB,EAAE;AACxC,MAAA,MAAMD,UAAU,CAACjE,UAAU,CAAC1H,IAAI,CAACkF,QAAQ,CAAC;MAC1CyG,UAAU,CAACxC,cAAc,EAAE;AAC7B,IAAA,CAAC,MAAM,IAAInJ,IAAI,CAAC/K,mBAAmB,EAAE;AACnC,MAAA,MAAM0W,UAAU,GAAGN,wBAAwB,CAACrL,IAAI,CAAC/K,mBAAmB,CAAC;AACrE,MAAA,MAAM0W,UAAU,CAACjE,UAAU,CAAC1H,IAAI,CAACkF,QAAQ,CAAC;MAC1CyG,UAAU,CAACxC,cAAc,EAAE;AAC7B,IAAA,CAAC,MAAM;AACL7Y,MAAAA,YAAY,CAACkE,KAAK,CAChB,qFAAqF,CACtF;AACH,IAAA;AACF,EAAA,CAAC,CAAA;AAAA;AAED,SAASwW,eAAeA,CAAC/V,mBAA2B,EAAEgO,OAAe,EAAA;AACnE,EAAA,IAAI4I,QAAQ,GAAG5B,mBAAmB,CAAC6B,MAAM,CAAE1a,CAAC,IAAKA,CAAC,CAACqS,UAAU,EAAE,KAAKR,OAAO,CAAC;AAC5E,EAAA,IAAI4I,QAAQ,CAACnf,MAAM,GAAG,CAAC,EAAE;AACvB,IAAA,MAAMqf,SAAS,GAAGF,QAAQ,CACvBzb,GAAG,CAAEgB,CAAC,IAAI;MACT,OAAO;AAAE2R,QAAAA,WAAW,EAAE3R,CAAC,CAACoS,sBAAsB;OAAI;AACpD,IAAA,CAAC,CAAC,CACDhC,IAAI,CAAC,GAAG,CAAC;IACZlR,YAAY,CAACkE,KAAK,CAAA,+CAAA,CAAAwB,MAAA,CACgCiN,OAAO,EAAA,wBAAA,CAAA,CAAAjN,MAAA,CAAyBf,mBAAmB,EAAA,GAAA,CAAA,EACnG;AAAE+W,MAAAA,YAAY,EAAED;AAAS,KAAE,CAC5B;AACH,EAAA;AACA,EAAA,IAAIhB,OAAO,GAAGc,QAAQ,CAAC,CAAC,CAAC;EACzB,IAAI,CAACd,OAAO,EAAE;AACZza,IAAAA,YAAY,CAACD,IAAI,CAAC,0BAA0B,EAAE;MAAE4E,mBAAmB;AAAEgO,MAAAA;AAAO,KAAE,CAAC;IAC/E,IAAI,CAACN,kBAAkB,EAAE;MACvB,MAAM1O,KAAK,CAAC,6BAA6B,CAAC;AAC5C,IAAA;IACA8W,OAAO,GAAG,IAAIzI,YAAY,CAAC;MACzBrN,mBAAmB;AACnBsF,MAAAA,IAAI,EAAE8Q,wBAAwB,CAACpW,mBAAmB,CAAC;MACnD0N,kBAAkB;AAClBC,MAAAA;AACD,KAAA,CAAC;AACFmI,IAAAA,OAAO,CAACpH,SAAS,CAACjB,MAAM,CAAC;IACzBuJ,uBAAuB,CAAClB,OAAO,CAAC;AAChCd,IAAAA,mBAAmB,CAACjS,IAAI,CAAC+S,OAAO,CAAC;EACnC,CAAC,MAAM,IAAI9V,mBAAmB,KAAK8V,OAAO,CAACvH,sBAAsB,EAAE,EAAE;AACnE;IACAuH,OAAO,CAAC3H,cAAc,CAACnO,mBAAmB,EAAEoW,wBAAwB,CAACpW,mBAAmB,CAAC,CAAC;AAC5F,EAAA;AAEA,EAAA,OAAO8V,OAAO;AAChB;AAEA,SAASM,wBAAwBA,CAACpW,mBAA2B,EAAA;AAC3D,EAAA,IAAIqV,YAAY,EAAE;IAChB,OAAOsB,mBAAmB,EAAE;AAC9B,EAAA;AACA,EAAA,IAAIrR,IAAI,GAAG2P,eAAe,CAAC9S,GAAG,CAACnC,mBAAmB,CAAC;EACnD,IAAI,CAACsF,IAAI,EAAE;AACTA,IAAAA,IAAI,GAAG,IAAIsO,qBAAqB,CAAC5T,mBAAmB,EAAE0N,kBAAkB,CAAC;IACzEpI,IAAI,CAACjB,EAAE,CAACnE,eAAe,CAAC6U,YAAY,EAAEkC,iBAAiB,CAAC;AACxDhC,IAAAA,eAAe,CAAC5X,GAAG,CAAC2C,mBAAmB,EAAEsF,IAAI,CAAC;AAChD,EAAA;AACA,EAAA,OAAOA,IAAI;AACb;AAEA,SAASqR,mBAAmBA,GAAA;EAC1B,IAAI,CAACzB,gBAAgB,EAAE;AACrB7Z,IAAAA,YAAY,CAACzD,KAAK,CAAC,iCAAiC,CAAC;AACrDsd,IAAAA,gBAAgB,GAAG,IAAItB,qBAAqB,CAAC,YAAY,EAAElG,kBAAkB,CAAC;AAChF,EAAA;AACA,EAAA,OAAOwH,gBAAgB;AACzB;AAEA,SAASmB,uBAAuBA,CAACrI,OAAe,EAAEhO,mBAA2B,EAAA;EAC3E,MAAM4W,QAAQ,GAAG5B,mBAAmB,CAAC6B,MAAM,CACxC1a,CAAC,IAAKA,CAAC,CAACoS,sBAAsB,EAAE,KAAKvO,mBAAmB,IAAI7D,CAAC,CAACqS,UAAU,EAAE,KAAKR,OAAO,CACxF;AACD,EAAA,IAAI4I,QAAQ,CAACnf,MAAM,GAAG,CAAC,EAAE;AACvB4D,IAAAA,YAAY,CAACkE,KAAK,CAAC,0EAA0E,EAAE;MAC7FyO,OAAO;AACPhO,MAAAA;AACD,KAAA,CAAC;AACJ,EAAA;AACA,EAAA,MAAM8V,OAAO,GAAGc,QAAQ,CAAC,CAAC,CAAC;EAC3B,IAAI,CAACd,OAAO,EAAE;AACZza,IAAAA,YAAY,CAAC6F,IAAI,CAAC,wCAAwC,EAAE;MAAE8M,OAAO;AAAEhO,MAAAA;AAAmB,KAAE,CAAC;AAC/F,EAAA,CAAC,MAAM;IACL8V,OAAO,CAACzH,gBAAgB,EAAE;AAC5B,EAAA;AACF;AAEA,SAASwH,oBAAoBA,CAACqB,MAAe,EAAElX,mBAA2B,EAAA;AACxE3E,EAAAA,YAAY,CAACzD,KAAK,CAAA,+CAAA,CAAAmJ,MAAA,CAAiDf,mBAAmB,CAAA,EAAI;AACxFkX,IAAAA;AACD,GAAA,CAAC;AACFnK,EAAAA,oBAAoB,CAAC1P,GAAG,CAAC2C,mBAAmB,EAAEkX,MAAM,CAAC;AACvD;AAEA,SAAef,YAAYA,CAAChY,GAAc,EAAE6H,KAAc,EAAA;;AACxD3K,IAAAA,YAAY,CAACD,IAAI,CAAC,gBAAgB,EAAE;AAAE4K,MAAAA;AAAK,KAAE,CAAC;IAC9C,MAAM2Q,mBAAmB,EAAE,CAACxC,MAAM,CAAChW,GAAG,EAAE6H,KAAK,CAAC;AAChD,EAAA,CAAC,CAAA;AAAA;AAED,SAASgR,uBAAuBA,CAAClB,OAAqB,EAAA;EACpDA,OAAO,CAACzR,EAAE,CAACjE,YAAY,CAACpB,KAAK,EAAGO,KAAK,IAAI;AACvC,IAAA,MAAM4X,GAAG,GAAiB;AACxB3B,MAAAA,IAAI,EAAE,OAAO;AACbzK,MAAAA,IAAI,EAAE;AAAExL,QAAAA,KAAK,EAAE,IAAIP,KAAK,IAAA+B,MAAA,CAAInB,kBAAkB,CAACL,KAAK,CAACO,MAAM,CAAC,EAAA,IAAA,CAAA,CAAAiB,MAAA,CAAKxB,KAAK,CAACL,OAAO,CAAE;AAAC;KAClF;IACD0W,WAAW,CAACuB,GAAG,CAAC;AAClB,EAAA,CAAC,CAAC;AACJ;AAEA,SAASF,iBAAiBA,CACxBzE,aAA4B,EAC5BxS,mBAA2B,EAC3BiQ,QAAiB,EAAA;AAEjB,EAAA,MAAMkH,GAAG,GAAmB;AAC1B3B,IAAAA,IAAI,EAAA,YAAc;AAClBzK,IAAAA,IAAI,EAAE;MACJ/K,mBAAmB;MACnBiQ,QAAQ;AACRuC,MAAAA;AACD;GACF;EACDoD,WAAW,CAACuB,GAAG,CAAC;AAClB;AAEA,SAASV,gBAAgBA,CAAC/G,OAAmB,EAAA;AAC3C/B,EAAAA,UAAU,GAAG+B,OAAO;AACpBsF,EAAAA,mBAAmB,CAACsB,OAAO,CAAEna,CAAC,IAAI;AAChCA,IAAAA,CAAC,CAACsT,aAAa,CAACC,OAAO,CAAC;AAC1B,EAAA,CAAC,CAAC;AACJ;AAEA;AACA;AACA,IAAIrX,IAAI,CAAC+e,iBAAiB,EAAE;AAC1B/b,EAAAA,YAAY,CAACzD,KAAK,CAAC,uBAAuB,CAAC;AAC3C;AACAS,EAAAA,IAAI,CAACgf,cAAc,GAAIC,KAAwB,IAAI;AACjD;AACA,IAAA,MAAMC,WAAW,GAAGD,KAAK,CAACC,WAAW;AACrClc,IAAAA,YAAY,CAACzD,KAAK,CAAC,aAAa,EAAE2f,WAAW,CAAC;IAE9C,MAAM;MAAE/B,IAAI;MAAExV,mBAAmB;MAAEgO,OAAO;AAAE7C,MAAAA;KAAO,GACjDoM,WAAW,CAACC,OAAiC;AAC/C,IAAA,MAAM1B,OAAO,GAAGC,eAAe,CAAC/V,mBAAmB,EAAEgO,OAAO,CAAC;AAC7D3S,IAAAA,YAAY,CAACzD,KAAK,CAAC,WAAW,EAAE;AAAEuT,MAAAA;AAAK,KAAE,CAAC;AAC1C2K,IAAAA,OAAO,CAACnH,cAAc,CAAC6G,IAAI,EAAE+B,WAAW,CAAC1I,QAAQ,EAAE0I,WAAW,CAACzI,QAAQ,EAAEd,OAAO,EAAE,KAAK,EAAE7C,KAAK,CAAC;EACjG,CAAC;AACH", "x_google_ignoreList": [0, 2, 8]}