{"version": 3, "file": "index.cjs", "sources": ["../src/gen/livekit_metrics_pb.js", "../src/gen/livekit_models_pb.js", "../src/gen/livekit_agent_pb.js", "../src/gen/livekit_agent_dispatch_pb.js", "../src/gen/livekit_egress_pb.js", "../src/gen/livekit_ingress_pb.js", "../src/gen/livekit_room_pb.js", "../src/gen/livekit_rtc_pb.js", "../src/gen/livekit_sip_pb.js", "../src/gen/livekit_webhook_pb.js", "../src/gen/version.js"], "sourcesContent": ["// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_metrics.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3, Timestamp } from \"@bufbuild/protobuf\";\n\n/**\n * index from [0: MAX_LABEL_PREDEFINED_MAX_VALUE) are for predefined labels (`MetricLabel`)\n *\n * @generated from enum livekit.MetricLabel\n */\nexport const MetricLabel = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.MetricLabel\",\n  [\n    {no: 0, name: \"AGENTS_LLM_TTFT\"},\n    {no: 1, name: \"AGENTS_STT_TTFT\"},\n    {no: 2, name: \"AGENTS_TTS_TTFB\"},\n    {no: 3, name: \"CLIENT_VIDEO_SUBSCRIBER_FREEZE_COUNT\"},\n    {no: 4, name: \"CLIENT_VIDEO_SUBSCRIBER_TOTAL_FREEZE_DURATION\"},\n    {no: 5, name: \"CLIENT_VIDEO_SUBSCRIBER_PAUSE_COUNT\"},\n    {no: 6, name: \"CLIENT_VIDEO_SUBSCRIBER_TOTAL_PAUSES_DURATION\"},\n    {no: 7, name: \"CLIENT_AUDIO_SUBSCRIBER_CONCEALED_SAMPLES\"},\n    {no: 8, name: \"CLIENT_AUDIO_SUBSCRIBER_SILENT_CONCEALED_SAMPLES\"},\n    {no: 9, name: \"CLIENT_AUDIO_SUBSCRIBER_CONCEALMENT_EVENTS\"},\n    {no: 10, name: \"CLIENT_AUDIO_SUBSCRIBER_INTERRUPTION_COUNT\"},\n    {no: 11, name: \"CLIENT_AUDIO_SUBSCRIBER_TOTAL_INTERRUPTION_DURATION\"},\n    {no: 12, name: \"CLIENT_SUBSCRIBER_JITTER_BUFFER_DELAY\"},\n    {no: 13, name: \"CLIENT_SUBSCRIBER_JITTER_BUFFER_EMITTED_COUNT\"},\n    {no: 14, name: \"CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_BANDWIDTH\"},\n    {no: 15, name: \"CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_CPU\"},\n    {no: 16, name: \"CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_OTHER\"},\n    {no: 17, name: \"PUBLISHER_RTT\"},\n    {no: 18, name: \"SERVER_MESH_RTT\"},\n    {no: 19, name: \"SUBSCRIBER_RTT\"},\n    {no: 4096, name: \"METRIC_LABEL_PREDEFINED_MAX_VALUE\"},\n  ],\n);\n\n/**\n * @generated from message livekit.MetricsBatch\n */\nexport const MetricsBatch = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.MetricsBatch\",\n  () => [\n    { no: 1, name: \"timestamp_ms\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"normalized_timestamp\", kind: \"message\", T: Timestamp },\n    { no: 3, name: \"str_data\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"time_series\", kind: \"message\", T: TimeSeriesMetric, repeated: true },\n    { no: 5, name: \"events\", kind: \"message\", T: EventMetric, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.TimeSeriesMetric\n */\nexport const TimeSeriesMetric = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TimeSeriesMetric\",\n  () => [\n    { no: 1, name: \"label\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 2, name: \"participant_identity\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"track_sid\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"samples\", kind: \"message\", T: MetricSample, repeated: true },\n    { no: 5, name: \"rid\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.MetricSample\n */\nexport const MetricSample = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.MetricSample\",\n  () => [\n    { no: 1, name: \"timestamp_ms\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"normalized_timestamp\", kind: \"message\", T: Timestamp },\n    { no: 3, name: \"value\", kind: \"scalar\", T: 2 /* ScalarType.FLOAT */ },\n  ],\n);\n\n/**\n * @generated from message livekit.EventMetric\n */\nexport const EventMetric = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.EventMetric\",\n  () => [\n    { no: 1, name: \"label\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 2, name: \"participant_identity\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"track_sid\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"start_timestamp_ms\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 5, name: \"end_timestamp_ms\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */, opt: true },\n    { no: 6, name: \"normalized_start_timestamp\", kind: \"message\", T: Timestamp },\n    { no: 7, name: \"normalized_end_timestamp\", kind: \"message\", T: Timestamp, opt: true },\n    { no: 8, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"rid\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_models.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3, Timestamp } from \"@bufbuild/protobuf\";\nimport { MetricsBatch } from \"./livekit_metrics_pb.js\";\n\n/**\n * @generated from enum livekit.AudioCodec\n */\nexport const AudioCodec = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.AudioCodec\",\n  [\n    {no: 0, name: \"DEFAULT_AC\"},\n    {no: 1, name: \"OPUS\"},\n    {no: 2, name: \"AAC\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.VideoCodec\n */\nexport const VideoCodec = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.VideoCodec\",\n  [\n    {no: 0, name: \"DEFAULT_VC\"},\n    {no: 1, name: \"H264_BASELINE\"},\n    {no: 2, name: \"H264_MAIN\"},\n    {no: 3, name: \"H264_HIGH\"},\n    {no: 4, name: \"VP8\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.ImageCodec\n */\nexport const ImageCodec = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ImageCodec\",\n  [\n    {no: 0, name: \"IC_DEFAULT\"},\n    {no: 1, name: \"IC_JPEG\"},\n  ],\n);\n\n/**\n * Policy for publisher to handle subscribers that are unable to support the primary codec of a track\n *\n * @generated from enum livekit.BackupCodecPolicy\n */\nexport const BackupCodecPolicy = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.BackupCodecPolicy\",\n  [\n    {no: 0, name: \"PREFER_REGRESSION\"},\n    {no: 1, name: \"SIMULCAST\"},\n    {no: 2, name: \"REGRESSION\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.TrackType\n */\nexport const TrackType = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.TrackType\",\n  [\n    {no: 0, name: \"AUDIO\"},\n    {no: 1, name: \"VIDEO\"},\n    {no: 2, name: \"DATA\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.TrackSource\n */\nexport const TrackSource = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.TrackSource\",\n  [\n    {no: 0, name: \"UNKNOWN\"},\n    {no: 1, name: \"CAMERA\"},\n    {no: 2, name: \"MICROPHONE\"},\n    {no: 3, name: \"SCREEN_SHARE\"},\n    {no: 4, name: \"SCREEN_SHARE_AUDIO\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.VideoQuality\n */\nexport const VideoQuality = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.VideoQuality\",\n  [\n    {no: 0, name: \"LOW\"},\n    {no: 1, name: \"MEDIUM\"},\n    {no: 2, name: \"HIGH\"},\n    {no: 3, name: \"OFF\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.ConnectionQuality\n */\nexport const ConnectionQuality = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ConnectionQuality\",\n  [\n    {no: 0, name: \"POOR\"},\n    {no: 1, name: \"GOOD\"},\n    {no: 2, name: \"EXCELLENT\"},\n    {no: 3, name: \"LOST\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.ClientConfigSetting\n */\nexport const ClientConfigSetting = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ClientConfigSetting\",\n  [\n    {no: 0, name: \"UNSET\"},\n    {no: 1, name: \"DISABLED\"},\n    {no: 2, name: \"ENABLED\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.DisconnectReason\n */\nexport const DisconnectReason = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.DisconnectReason\",\n  [\n    {no: 0, name: \"UNKNOWN_REASON\"},\n    {no: 1, name: \"CLIENT_INITIATED\"},\n    {no: 2, name: \"DUPLICATE_IDENTITY\"},\n    {no: 3, name: \"SERVER_SHUTDOWN\"},\n    {no: 4, name: \"PARTICIPANT_REMOVED\"},\n    {no: 5, name: \"ROOM_DELETED\"},\n    {no: 6, name: \"STATE_MISMATCH\"},\n    {no: 7, name: \"JOIN_FAILURE\"},\n    {no: 8, name: \"MIGRATION\"},\n    {no: 9, name: \"SIGNAL_CLOSE\"},\n    {no: 10, name: \"ROOM_CLOSED\"},\n    {no: 11, name: \"USER_UNAVAILABLE\"},\n    {no: 12, name: \"USER_REJECTED\"},\n    {no: 13, name: \"SIP_TRUNK_FAILURE\"},\n    {no: 14, name: \"CONNECTION_TIMEOUT\"},\n    {no: 15, name: \"MEDIA_FAILURE\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.ReconnectReason\n */\nexport const ReconnectReason = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ReconnectReason\",\n  [\n    {no: 0, name: \"RR_UNKNOWN\"},\n    {no: 1, name: \"RR_SIGNAL_DISCONNECTED\"},\n    {no: 2, name: \"RR_PUBLISHER_FAILED\"},\n    {no: 3, name: \"RR_SUBSCRIBER_FAILED\"},\n    {no: 4, name: \"RR_SWITCH_CANDIDATE\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SubscriptionError\n */\nexport const SubscriptionError = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SubscriptionError\",\n  [\n    {no: 0, name: \"SE_UNKNOWN\"},\n    {no: 1, name: \"SE_CODEC_UNSUPPORTED\"},\n    {no: 2, name: \"SE_TRACK_NOTFOUND\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.AudioTrackFeature\n */\nexport const AudioTrackFeature = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.AudioTrackFeature\",\n  [\n    {no: 0, name: \"TF_STEREO\"},\n    {no: 1, name: \"TF_NO_DTX\"},\n    {no: 2, name: \"TF_AUTO_GAIN_CONTROL\"},\n    {no: 3, name: \"TF_ECHO_CANCELLATION\"},\n    {no: 4, name: \"TF_NOISE_SUPPRESSION\"},\n    {no: 5, name: \"TF_ENHANCED_NOISE_CANCELLATION\"},\n    {no: 6, name: \"TF_PRECONNECT_BUFFER\"},\n  ],\n);\n\n/**\n * @generated from message livekit.Pagination\n */\nexport const Pagination = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.Pagination\",\n  () => [\n    { no: 1, name: \"after_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"limit\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n  ],\n);\n\n/**\n * ListUpdate is used for updated APIs where 'repeated string' field is modified.\n *\n * @generated from message livekit.ListUpdate\n */\nexport const ListUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListUpdate\",\n  () => [\n    { no: 1, name: \"set\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.Room\n */\nexport const Room = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.Room\",\n  () => [\n    { no: 1, name: \"sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"empty_timeout\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 14, name: \"departure_timeout\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"max_participants\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 5, name: \"creation_time\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 15, name: \"creation_time_ms\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 6, name: \"turn_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"enabled_codecs\", kind: \"message\", T: Codec, repeated: true },\n    { no: 8, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"num_participants\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 11, name: \"num_publishers\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 10, name: \"active_recording\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 13, name: \"version\", kind: \"message\", T: TimedVersion },\n  ],\n);\n\n/**\n * @generated from message livekit.Codec\n */\nexport const Codec = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.Codec\",\n  () => [\n    { no: 1, name: \"mime\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"fmtp_line\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.PlayoutDelay\n */\nexport const PlayoutDelay = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.PlayoutDelay\",\n  () => [\n    { no: 1, name: \"enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 2, name: \"min\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"max\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ParticipantPermission\n */\nexport const ParticipantPermission = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ParticipantPermission\",\n  () => [\n    { no: 1, name: \"can_subscribe\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 2, name: \"can_publish\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 3, name: \"can_publish_data\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 9, name: \"can_publish_sources\", kind: \"enum\", T: proto3.getEnumType(TrackSource), repeated: true },\n    { no: 7, name: \"hidden\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 8, name: \"recorder\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 10, name: \"can_update_metadata\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 11, name: \"agent\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 12, name: \"can_subscribe_metrics\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ParticipantInfo\n */\nexport const ParticipantInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ParticipantInfo\",\n  () => [\n    { no: 1, name: \"sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"state\", kind: \"enum\", T: proto3.getEnumType(ParticipantInfo_State) },\n    { no: 4, name: \"tracks\", kind: \"message\", T: TrackInfo, repeated: true },\n    { no: 5, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"joined_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 17, name: \"joined_at_ms\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 9, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"version\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 11, name: \"permission\", kind: \"message\", T: ParticipantPermission },\n    { no: 12, name: \"region\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 13, name: \"is_publisher\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 14, name: \"kind\", kind: \"enum\", T: proto3.getEnumType(ParticipantInfo_Kind) },\n    { no: 15, name: \"attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 16, name: \"disconnect_reason\", kind: \"enum\", T: proto3.getEnumType(DisconnectReason) },\n    { no: 18, name: \"kind_details\", kind: \"enum\", T: proto3.getEnumType(ParticipantInfo_KindDetail), repeated: true },\n  ],\n);\n\n/**\n * @generated from enum livekit.ParticipantInfo.State\n */\nexport const ParticipantInfo_State = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ParticipantInfo.State\",\n  [\n    {no: 0, name: \"JOINING\"},\n    {no: 1, name: \"JOINED\"},\n    {no: 2, name: \"ACTIVE\"},\n    {no: 3, name: \"DISCONNECTED\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.ParticipantInfo.Kind\n */\nexport const ParticipantInfo_Kind = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ParticipantInfo.Kind\",\n  [\n    {no: 0, name: \"STANDARD\"},\n    {no: 1, name: \"INGRESS\"},\n    {no: 2, name: \"EGRESS\"},\n    {no: 3, name: \"SIP\"},\n    {no: 4, name: \"AGENT\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.ParticipantInfo.KindDetail\n */\nexport const ParticipantInfo_KindDetail = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ParticipantInfo.KindDetail\",\n  [\n    {no: 0, name: \"CLOUD_AGENT\"},\n    {no: 1, name: \"FORWARDED\"},\n  ],\n);\n\n/**\n * @generated from message livekit.Encryption\n */\nexport const Encryption = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.Encryption\",\n  [],\n);\n\n/**\n * @generated from enum livekit.Encryption.Type\n */\nexport const Encryption_Type = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.Encryption.Type\",\n  [\n    {no: 0, name: \"NONE\"},\n    {no: 1, name: \"GCM\"},\n    {no: 2, name: \"CUSTOM\"},\n  ],\n);\n\n/**\n * @generated from message livekit.SimulcastCodecInfo\n */\nexport const SimulcastCodecInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SimulcastCodecInfo\",\n  () => [\n    { no: 1, name: \"mime_type\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"mid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"cid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"layers\", kind: \"message\", T: VideoLayer, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.TrackInfo\n */\nexport const TrackInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TrackInfo\",\n  () => [\n    { no: 1, name: \"sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"type\", kind: \"enum\", T: proto3.getEnumType(TrackType) },\n    { no: 3, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"muted\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 5, name: \"width\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 6, name: \"height\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 7, name: \"simulcast\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 8, name: \"disable_dtx\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 9, name: \"source\", kind: \"enum\", T: proto3.getEnumType(TrackSource) },\n    { no: 10, name: \"layers\", kind: \"message\", T: VideoLayer, repeated: true },\n    { no: 11, name: \"mime_type\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 12, name: \"mid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 13, name: \"codecs\", kind: \"message\", T: SimulcastCodecInfo, repeated: true },\n    { no: 14, name: \"stereo\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 15, name: \"disable_red\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 16, name: \"encryption\", kind: \"enum\", T: proto3.getEnumType(Encryption_Type) },\n    { no: 17, name: \"stream\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 18, name: \"version\", kind: \"message\", T: TimedVersion },\n    { no: 19, name: \"audio_features\", kind: \"enum\", T: proto3.getEnumType(AudioTrackFeature), repeated: true },\n    { no: 20, name: \"backup_codec_policy\", kind: \"enum\", T: proto3.getEnumType(BackupCodecPolicy) },\n  ],\n);\n\n/**\n * provide information about available spatial layers\n *\n * @generated from message livekit.VideoLayer\n */\nexport const VideoLayer = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.VideoLayer\",\n  () => [\n    { no: 1, name: \"quality\", kind: \"enum\", T: proto3.getEnumType(VideoQuality) },\n    { no: 2, name: \"width\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"height\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"bitrate\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 5, name: \"ssrc\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 6, name: \"spatial_layer\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 7, name: \"rid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * new DataPacket API\n *\n * @generated from message livekit.DataPacket\n */\nexport const DataPacket = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataPacket\",\n  () => [\n    { no: 1, name: \"kind\", kind: \"enum\", T: proto3.getEnumType(DataPacket_Kind) },\n    { no: 4, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"destination_identities\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 2, name: \"user\", kind: \"message\", T: UserPacket, oneof: \"value\" },\n    { no: 3, name: \"speaker\", kind: \"message\", T: ActiveSpeakerUpdate, oneof: \"value\" },\n    { no: 6, name: \"sip_dtmf\", kind: \"message\", T: SipDTMF, oneof: \"value\" },\n    { no: 7, name: \"transcription\", kind: \"message\", T: Transcription, oneof: \"value\" },\n    { no: 8, name: \"metrics\", kind: \"message\", T: MetricsBatch, oneof: \"value\" },\n    { no: 9, name: \"chat_message\", kind: \"message\", T: ChatMessage, oneof: \"value\" },\n    { no: 10, name: \"rpc_request\", kind: \"message\", T: RpcRequest, oneof: \"value\" },\n    { no: 11, name: \"rpc_ack\", kind: \"message\", T: RpcAck, oneof: \"value\" },\n    { no: 12, name: \"rpc_response\", kind: \"message\", T: RpcResponse, oneof: \"value\" },\n    { no: 13, name: \"stream_header\", kind: \"message\", T: DataStream_Header, oneof: \"value\" },\n    { no: 14, name: \"stream_chunk\", kind: \"message\", T: DataStream_Chunk, oneof: \"value\" },\n    { no: 15, name: \"stream_trailer\", kind: \"message\", T: DataStream_Trailer, oneof: \"value\" },\n    { no: 16, name: \"sequence\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 17, name: \"participant_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from enum livekit.DataPacket.Kind\n */\nexport const DataPacket_Kind = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.DataPacket.Kind\",\n  [\n    {no: 0, name: \"RELIABLE\"},\n    {no: 1, name: \"LOSSY\"},\n  ],\n);\n\n/**\n * @generated from message livekit.ActiveSpeakerUpdate\n */\nexport const ActiveSpeakerUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ActiveSpeakerUpdate\",\n  () => [\n    { no: 1, name: \"speakers\", kind: \"message\", T: SpeakerInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.SpeakerInfo\n */\nexport const SpeakerInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SpeakerInfo\",\n  () => [\n    { no: 1, name: \"sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"level\", kind: \"scalar\", T: 2 /* ScalarType.FLOAT */ },\n    { no: 3, name: \"active\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.UserPacket\n */\nexport const UserPacket = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UserPacket\",\n  () => [\n    { no: 1, name: \"participant_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"payload\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */ },\n    { no: 3, name: \"destination_sids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 6, name: \"destination_identities\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"topic\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 8, name: \"id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 9, name: \"start_time\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */, opt: true },\n    { no: 10, name: \"end_time\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */, opt: true },\n    { no: 11, name: \"nonce\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SipDTMF\n */\nexport const SipDTMF = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SipDTMF\",\n  () => [\n    { no: 3, name: \"code\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"digit\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.Transcription\n */\nexport const Transcription = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.Transcription\",\n  () => [\n    { no: 2, name: \"transcribed_participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"track_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"segments\", kind: \"message\", T: TranscriptionSegment, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.TranscriptionSegment\n */\nexport const TranscriptionSegment = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TranscriptionSegment\",\n  () => [\n    { no: 1, name: \"id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"text\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"start_time\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 4, name: \"end_time\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 5, name: \"final\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 6, name: \"language\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ChatMessage\n */\nexport const ChatMessage = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ChatMessage\",\n  () => [\n    { no: 1, name: \"id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 3, name: \"edit_timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */, opt: true },\n    { no: 4, name: \"message\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"deleted\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 6, name: \"generated\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RpcRequest\n */\nexport const RpcRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RpcRequest\",\n  () => [\n    { no: 1, name: \"id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"method\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"payload\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"response_timeout_ms\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 5, name: \"version\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RpcAck\n */\nexport const RpcAck = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RpcAck\",\n  () => [\n    { no: 1, name: \"request_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RpcResponse\n */\nexport const RpcResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RpcResponse\",\n  () => [\n    { no: 1, name: \"request_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"payload\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, oneof: \"value\" },\n    { no: 3, name: \"error\", kind: \"message\", T: RpcError, oneof: \"value\" },\n  ],\n);\n\n/**\n * @generated from message livekit.RpcError\n */\nexport const RpcError = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RpcError\",\n  () => [\n    { no: 1, name: \"code\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 2, name: \"message\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"data\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ParticipantTracks\n */\nexport const ParticipantTracks = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ParticipantTracks\",\n  () => [\n    { no: 1, name: \"participant_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"track_sids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * details about the server\n *\n * @generated from message livekit.ServerInfo\n */\nexport const ServerInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ServerInfo\",\n  () => [\n    { no: 1, name: \"edition\", kind: \"enum\", T: proto3.getEnumType(ServerInfo_Edition) },\n    { no: 2, name: \"version\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"protocol\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 4, name: \"region\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"node_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"debug_info\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"agent_protocol\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n  ],\n);\n\n/**\n * @generated from enum livekit.ServerInfo.Edition\n */\nexport const ServerInfo_Edition = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ServerInfo.Edition\",\n  [\n    {no: 0, name: \"Standard\"},\n    {no: 1, name: \"Cloud\"},\n  ],\n);\n\n/**\n * details about the client\n *\n * @generated from message livekit.ClientInfo\n */\nexport const ClientInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ClientInfo\",\n  () => [\n    { no: 1, name: \"sdk\", kind: \"enum\", T: proto3.getEnumType(ClientInfo_SDK) },\n    { no: 2, name: \"version\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"protocol\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 4, name: \"os\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"os_version\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"device_model\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"browser\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"browser_version\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"address\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"network\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 11, name: \"other_sdks\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from enum livekit.ClientInfo.SDK\n */\nexport const ClientInfo_SDK = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ClientInfo.SDK\",\n  [\n    {no: 0, name: \"UNKNOWN\"},\n    {no: 1, name: \"JS\"},\n    {no: 2, name: \"SWIFT\"},\n    {no: 3, name: \"ANDROID\"},\n    {no: 4, name: \"FLUTTER\"},\n    {no: 5, name: \"GO\"},\n    {no: 6, name: \"UNITY\"},\n    {no: 7, name: \"REACT_NATIVE\"},\n    {no: 8, name: \"RUST\"},\n    {no: 9, name: \"PYTHON\"},\n    {no: 10, name: \"CPP\"},\n    {no: 11, name: \"UNITY_WEB\"},\n    {no: 12, name: \"NODE\"},\n    {no: 13, name: \"UNREAL\"},\n    {no: 14, name: \"ESP32\"},\n  ],\n);\n\n/**\n * server provided client configuration\n *\n * @generated from message livekit.ClientConfiguration\n */\nexport const ClientConfiguration = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ClientConfiguration\",\n  () => [\n    { no: 1, name: \"video\", kind: \"message\", T: VideoConfiguration },\n    { no: 2, name: \"screen\", kind: \"message\", T: VideoConfiguration },\n    { no: 3, name: \"resume_connection\", kind: \"enum\", T: proto3.getEnumType(ClientConfigSetting) },\n    { no: 4, name: \"disabled_codecs\", kind: \"message\", T: DisabledCodecs },\n    { no: 5, name: \"force_relay\", kind: \"enum\", T: proto3.getEnumType(ClientConfigSetting) },\n  ],\n);\n\n/**\n * @generated from message livekit.VideoConfiguration\n */\nexport const VideoConfiguration = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.VideoConfiguration\",\n  () => [\n    { no: 1, name: \"hardware_encoder\", kind: \"enum\", T: proto3.getEnumType(ClientConfigSetting) },\n  ],\n);\n\n/**\n * @generated from message livekit.DisabledCodecs\n */\nexport const DisabledCodecs = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DisabledCodecs\",\n  () => [\n    { no: 1, name: \"codecs\", kind: \"message\", T: Codec, repeated: true },\n    { no: 2, name: \"publish\", kind: \"message\", T: Codec, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.RTPDrift\n */\nexport const RTPDrift = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RTPDrift\",\n  () => [\n    { no: 1, name: \"start_time\", kind: \"message\", T: Timestamp },\n    { no: 2, name: \"end_time\", kind: \"message\", T: Timestamp },\n    { no: 3, name: \"duration\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 4, name: \"start_timestamp\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 5, name: \"end_timestamp\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 6, name: \"rtp_clock_ticks\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 7, name: \"drift_samples\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 8, name: \"drift_ms\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 9, name: \"clock_rate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RTPStats\n */\nexport const RTPStats = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RTPStats\",\n  () => [\n    { no: 1, name: \"start_time\", kind: \"message\", T: Timestamp },\n    { no: 2, name: \"end_time\", kind: \"message\", T: Timestamp },\n    { no: 3, name: \"duration\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 4, name: \"packets\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 5, name: \"packet_rate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 6, name: \"bytes\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 39, name: \"header_bytes\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 7, name: \"bitrate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 8, name: \"packets_lost\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 9, name: \"packet_loss_rate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 10, name: \"packet_loss_percentage\", kind: \"scalar\", T: 2 /* ScalarType.FLOAT */ },\n    { no: 11, name: \"packets_duplicate\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 12, name: \"packet_duplicate_rate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 13, name: \"bytes_duplicate\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 40, name: \"header_bytes_duplicate\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 14, name: \"bitrate_duplicate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 15, name: \"packets_padding\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 16, name: \"packet_padding_rate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 17, name: \"bytes_padding\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 41, name: \"header_bytes_padding\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 18, name: \"bitrate_padding\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 19, name: \"packets_out_of_order\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 20, name: \"frames\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 21, name: \"frame_rate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 22, name: \"jitter_current\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 23, name: \"jitter_max\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 24, name: \"gap_histogram\", kind: \"map\", K: 5 /* ScalarType.INT32 */, V: {kind: \"scalar\", T: 13 /* ScalarType.UINT32 */} },\n    { no: 25, name: \"nacks\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 37, name: \"nack_acks\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 26, name: \"nack_misses\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 38, name: \"nack_repeated\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 27, name: \"plis\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 28, name: \"last_pli\", kind: \"message\", T: Timestamp },\n    { no: 29, name: \"firs\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 30, name: \"last_fir\", kind: \"message\", T: Timestamp },\n    { no: 31, name: \"rtt_current\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 32, name: \"rtt_max\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 33, name: \"key_frames\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 34, name: \"last_key_frame\", kind: \"message\", T: Timestamp },\n    { no: 35, name: \"layer_lock_plis\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 36, name: \"last_layer_lock_pli\", kind: \"message\", T: Timestamp },\n    { no: 44, name: \"packet_drift\", kind: \"message\", T: RTPDrift },\n    { no: 45, name: \"ntp_report_drift\", kind: \"message\", T: RTPDrift },\n    { no: 46, name: \"rebased_report_drift\", kind: \"message\", T: RTPDrift },\n    { no: 47, name: \"received_report_drift\", kind: \"message\", T: RTPDrift },\n  ],\n);\n\n/**\n * @generated from message livekit.RTCPSenderReportState\n */\nexport const RTCPSenderReportState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RTCPSenderReportState\",\n  () => [\n    { no: 1, name: \"rtp_timestamp\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 2, name: \"rtp_timestamp_ext\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 3, name: \"ntp_timestamp\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 4, name: \"at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 5, name: \"at_adjusted\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 6, name: \"packets\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 7, name: \"octets\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RTPForwarderState\n */\nexport const RTPForwarderState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RTPForwarderState\",\n  () => [\n    { no: 1, name: \"started\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 2, name: \"reference_layer_spatial\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 3, name: \"pre_start_time\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 4, name: \"ext_first_timestamp\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 5, name: \"dummy_start_timestamp_offset\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 6, name: \"rtp_munger\", kind: \"message\", T: RTPMungerState },\n    { no: 7, name: \"vp8_munger\", kind: \"message\", T: VP8MungerState, oneof: \"codec_munger\" },\n    { no: 8, name: \"sender_report_state\", kind: \"message\", T: RTCPSenderReportState, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.RTPMungerState\n */\nexport const RTPMungerState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RTPMungerState\",\n  () => [\n    { no: 1, name: \"ext_last_sequence_number\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 2, name: \"ext_second_last_sequence_number\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 3, name: \"ext_last_timestamp\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 4, name: \"ext_second_last_timestamp\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 5, name: \"last_marker\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 6, name: \"second_last_marker\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.VP8MungerState\n */\nexport const VP8MungerState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.VP8MungerState\",\n  () => [\n    { no: 1, name: \"ext_last_picture_id\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 2, name: \"picture_id_used\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 3, name: \"last_tl0_pic_idx\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"tl0_pic_idx_used\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 5, name: \"tid_used\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 6, name: \"last_key_idx\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 7, name: \"key_idx_used\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.TimedVersion\n */\nexport const TimedVersion = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TimedVersion\",\n  () => [\n    { no: 1, name: \"unix_micro\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"ticks\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.DataStream\n */\nexport const DataStream = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataStream\",\n  [],\n);\n\n/**\n * enum for operation types (specific to TextHeader)\n *\n * @generated from enum livekit.DataStream.OperationType\n */\nexport const DataStream_OperationType = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.DataStream.OperationType\",\n  [\n    {no: 0, name: \"CREATE\"},\n    {no: 1, name: \"UPDATE\"},\n    {no: 2, name: \"DELETE\"},\n    {no: 3, name: \"REACTION\"},\n  ],\n);\n\n/**\n * header properties specific to text streams\n *\n * @generated from message livekit.DataStream.TextHeader\n */\nexport const DataStream_TextHeader = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataStream.TextHeader\",\n  () => [\n    { no: 1, name: \"operation_type\", kind: \"enum\", T: proto3.getEnumType(DataStream_OperationType) },\n    { no: 2, name: \"version\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 3, name: \"reply_to_stream_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"attached_stream_ids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 5, name: \"generated\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n  {localName: \"DataStream_TextHeader\"},\n);\n\n/**\n * header properties specific to byte or file streams\n *\n * @generated from message livekit.DataStream.ByteHeader\n */\nexport const DataStream_ByteHeader = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataStream.ByteHeader\",\n  () => [\n    { no: 1, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n  {localName: \"DataStream_ByteHeader\"},\n);\n\n/**\n * main DataStream.Header that contains a oneof for specific headers\n *\n * @generated from message livekit.DataStream.Header\n */\nexport const DataStream_Header = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataStream.Header\",\n  () => [\n    { no: 1, name: \"stream_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 3, name: \"topic\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"mime_type\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"total_length\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */, opt: true },\n    { no: 7, name: \"encryption_type\", kind: \"enum\", T: proto3.getEnumType(Encryption_Type) },\n    { no: 8, name: \"attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 9, name: \"text_header\", kind: \"message\", T: DataStream_TextHeader, oneof: \"content_header\" },\n    { no: 10, name: \"byte_header\", kind: \"message\", T: DataStream_ByteHeader, oneof: \"content_header\" },\n  ],\n  {localName: \"DataStream_Header\"},\n);\n\n/**\n * @generated from message livekit.DataStream.Chunk\n */\nexport const DataStream_Chunk = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataStream.Chunk\",\n  () => [\n    { no: 1, name: \"stream_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"chunk_index\", kind: \"scalar\", T: 4 /* ScalarType.UINT64 */ },\n    { no: 3, name: \"content\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */ },\n    { no: 4, name: \"version\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 5, name: \"iv\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */, opt: true },\n  ],\n  {localName: \"DataStream_Chunk\"},\n);\n\n/**\n * @generated from message livekit.DataStream.Trailer\n */\nexport const DataStream_Trailer = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataStream.Trailer\",\n  () => [\n    { no: 1, name: \"stream_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"reason\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n  ],\n  {localName: \"DataStream_Trailer\"},\n);\n\n/**\n * @generated from message livekit.WebhookConfig\n */\nexport const WebhookConfig = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.WebhookConfig\",\n  () => [\n    { no: 1, name: \"url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"signing_key\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//   http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_agent.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3 } from \"@bufbuild/protobuf\";\nimport { ParticipantInfo, ParticipantPermission, Room, ServerInfo } from \"./livekit_models_pb.js\";\n\n/**\n * @generated from enum livekit.JobType\n */\nexport const JobType = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.JobType\",\n  [\n    {no: 0, name: \"JT_<PERSON>O<PERSON>\"},\n    {no: 1, name: \"JT_PUBLISHER\"},\n    {no: 2, name: \"JT_PARTICIPANT\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.WorkerStatus\n */\nexport const WorkerStatus = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.WorkerStatus\",\n  [\n    {no: 0, name: \"WS_AVAILABLE\"},\n    {no: 1, name: \"WS_FULL\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.JobStatus\n */\nexport const JobStatus = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.JobStatus\",\n  [\n    {no: 0, name: \"JS_PENDING\"},\n    {no: 1, name: \"JS_RUNNING\"},\n    {no: 2, name: \"JS_SUCCESS\"},\n    {no: 3, name: \"JS_FAILED\"},\n  ],\n);\n\n/**\n * @generated from message livekit.Job\n */\nexport const Job = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.Job\",\n  () => [\n    { no: 1, name: \"id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"dispatch_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"type\", kind: \"enum\", T: proto3.getEnumType(JobType) },\n    { no: 3, name: \"room\", kind: \"message\", T: Room },\n    { no: 4, name: \"participant\", kind: \"message\", T: ParticipantInfo, opt: true },\n    { no: 5, name: \"namespace\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"agent_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"state\", kind: \"message\", T: JobState },\n  ],\n);\n\n/**\n * @generated from message livekit.JobState\n */\nexport const JobState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.JobState\",\n  () => [\n    { no: 1, name: \"status\", kind: \"enum\", T: proto3.getEnumType(JobStatus) },\n    { no: 2, name: \"error\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"started_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 4, name: \"ended_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 5, name: \"updated_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 6, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"worker_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * from Worker to Server\n *\n * @generated from message livekit.WorkerMessage\n */\nexport const WorkerMessage = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.WorkerMessage\",\n  () => [\n    { no: 1, name: \"register\", kind: \"message\", T: RegisterWorkerRequest, oneof: \"message\" },\n    { no: 2, name: \"availability\", kind: \"message\", T: AvailabilityResponse, oneof: \"message\" },\n    { no: 3, name: \"update_worker\", kind: \"message\", T: UpdateWorkerStatus, oneof: \"message\" },\n    { no: 4, name: \"update_job\", kind: \"message\", T: UpdateJobStatus, oneof: \"message\" },\n    { no: 5, name: \"ping\", kind: \"message\", T: WorkerPing, oneof: \"message\" },\n    { no: 6, name: \"simulate_job\", kind: \"message\", T: SimulateJobRequest, oneof: \"message\" },\n    { no: 7, name: \"migrate_job\", kind: \"message\", T: MigrateJobRequest, oneof: \"message\" },\n  ],\n);\n\n/**\n * from Server to Worker\n *\n * @generated from message livekit.ServerMessage\n */\nexport const ServerMessage = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ServerMessage\",\n  () => [\n    { no: 1, name: \"register\", kind: \"message\", T: RegisterWorkerResponse, oneof: \"message\" },\n    { no: 2, name: \"availability\", kind: \"message\", T: AvailabilityRequest, oneof: \"message\" },\n    { no: 3, name: \"assignment\", kind: \"message\", T: JobAssignment, oneof: \"message\" },\n    { no: 5, name: \"termination\", kind: \"message\", T: JobTermination, oneof: \"message\" },\n    { no: 4, name: \"pong\", kind: \"message\", T: WorkerPong, oneof: \"message\" },\n  ],\n);\n\n/**\n * @generated from message livekit.SimulateJobRequest\n */\nexport const SimulateJobRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SimulateJobRequest\",\n  () => [\n    { no: 1, name: \"type\", kind: \"enum\", T: proto3.getEnumType(JobType) },\n    { no: 2, name: \"room\", kind: \"message\", T: Room },\n    { no: 3, name: \"participant\", kind: \"message\", T: ParticipantInfo },\n  ],\n);\n\n/**\n * @generated from message livekit.WorkerPing\n */\nexport const WorkerPing = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.WorkerPing\",\n  () => [\n    { no: 1, name: \"timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.WorkerPong\n */\nexport const WorkerPong = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.WorkerPong\",\n  () => [\n    { no: 1, name: \"last_timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RegisterWorkerRequest\n */\nexport const RegisterWorkerRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RegisterWorkerRequest\",\n  () => [\n    { no: 1, name: \"type\", kind: \"enum\", T: proto3.getEnumType(JobType) },\n    { no: 8, name: \"agent_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"version\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"ping_interval\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 6, name: \"namespace\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 7, name: \"allowed_permissions\", kind: \"message\", T: ParticipantPermission },\n  ],\n);\n\n/**\n * @generated from message livekit.RegisterWorkerResponse\n */\nexport const RegisterWorkerResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RegisterWorkerResponse\",\n  () => [\n    { no: 1, name: \"worker_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"server_info\", kind: \"message\", T: ServerInfo },\n  ],\n);\n\n/**\n * @generated from message livekit.MigrateJobRequest\n */\nexport const MigrateJobRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.MigrateJobRequest\",\n  () => [\n    { no: 2, name: \"job_ids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.AvailabilityRequest\n */\nexport const AvailabilityRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AvailabilityRequest\",\n  () => [\n    { no: 1, name: \"job\", kind: \"message\", T: Job },\n    { no: 2, name: \"resuming\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.AvailabilityResponse\n */\nexport const AvailabilityResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AvailabilityResponse\",\n  () => [\n    { no: 1, name: \"job_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"available\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 3, name: \"supports_resume\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 8, name: \"terminate\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 4, name: \"participant_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"participant_metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"participant_attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateJobStatus\n */\nexport const UpdateJobStatus = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateJobStatus\",\n  () => [\n    { no: 1, name: \"job_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"status\", kind: \"enum\", T: proto3.getEnumType(JobStatus) },\n    { no: 3, name: \"error\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateWorkerStatus\n */\nexport const UpdateWorkerStatus = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateWorkerStatus\",\n  () => [\n    { no: 1, name: \"status\", kind: \"enum\", T: proto3.getEnumType(WorkerStatus), opt: true },\n    { no: 3, name: \"load\", kind: \"scalar\", T: 2 /* ScalarType.FLOAT */ },\n    { no: 4, name: \"job_count\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.JobAssignment\n */\nexport const JobAssignment = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.JobAssignment\",\n  () => [\n    { no: 1, name: \"job\", kind: \"message\", T: Job },\n    { no: 2, name: \"url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 3, name: \"token\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.JobTermination\n */\nexport const JobTermination = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.JobTermination\",\n  () => [\n    { no: 1, name: \"job_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//   http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_agent_dispatch.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3 } from \"@bufbuild/protobuf\";\nimport { Job } from \"./livekit_agent_pb.js\";\n\n/**\n * @generated from message livekit.CreateAgentDispatchRequest\n */\nexport const CreateAgentDispatchRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.CreateAgentDispatchRequest\",\n  () => [\n    { no: 1, name: \"agent_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RoomAgentDispatch\n */\nexport const RoomAgentDispatch = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RoomAgentDispatch\",\n  () => [\n    { no: 1, name: \"agent_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.DeleteAgentDispatchRequest\n */\nexport const DeleteAgentDispatchRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DeleteAgentDispatchRequest\",\n  () => [\n    { no: 1, name: \"dispatch_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ListAgentDispatchRequest\n */\nexport const ListAgentDispatchRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListAgentDispatchRequest\",\n  () => [\n    { no: 1, name: \"dispatch_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ListAgentDispatchResponse\n */\nexport const ListAgentDispatchResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListAgentDispatchResponse\",\n  () => [\n    { no: 1, name: \"agent_dispatches\", kind: \"message\", T: AgentDispatch, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.AgentDispatch\n */\nexport const AgentDispatch = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AgentDispatch\",\n  () => [\n    { no: 1, name: \"id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"agent_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"state\", kind: \"message\", T: AgentDispatchState },\n  ],\n);\n\n/**\n * @generated from message livekit.AgentDispatchState\n */\nexport const AgentDispatchState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AgentDispatchState\",\n  () => [\n    { no: 1, name: \"jobs\", kind: \"message\", T: Job, repeated: true },\n    { no: 2, name: \"created_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 3, name: \"deleted_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n  ],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_egress.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3 } from \"@bufbuild/protobuf\";\nimport { AudioCodec, ImageCodec, VideoCodec, WebhookConfig } from \"./livekit_models_pb.js\";\n\n/**\n * @generated from enum livekit.EncodedFileType\n */\nexport const EncodedFileType = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.EncodedFileType\",\n  [\n    {no: 0, name: \"DEFAULT_FILETYPE\"},\n    {no: 1, name: \"MP4\"},\n    {no: 2, name: \"OGG\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SegmentedFileProtocol\n */\nexport const SegmentedFileProtocol = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SegmentedFileProtocol\",\n  [\n    {no: 0, name: \"DEFAULT_SEGMENTED_FILE_PROTOCOL\"},\n    {no: 1, name: \"HLS_PROTOCOL\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SegmentedFileSuffix\n */\nexport const SegmentedFileSuffix = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SegmentedFileSuffix\",\n  [\n    {no: 0, name: \"INDEX\"},\n    {no: 1, name: \"TIMESTAMP\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.ImageFileSuffix\n */\nexport const ImageFileSuffix = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.ImageFileSuffix\",\n  [\n    {no: 0, name: \"IMAGE_SUFFIX_INDEX\"},\n    {no: 1, name: \"IMAGE_SUFFIX_TIMESTAMP\"},\n    {no: 2, name: \"IMAGE_SUFFIX_NONE_OVERWRITE\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.StreamProtocol\n */\nexport const StreamProtocol = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.StreamProtocol\",\n  [\n    {no: 0, name: \"DEFAULT_PROTOCOL\"},\n    {no: 1, name: \"RTMP\"},\n    {no: 2, name: \"SRT\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.AudioMixing\n */\nexport const AudioMixing = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.AudioMixing\",\n  [\n    {no: 0, name: \"DEFAULT_MIXING\"},\n    {no: 1, name: \"DUAL_CHANNEL_AGENT\"},\n    {no: 2, name: \"DUAL_CHANNEL_ALTERNATE\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.EncodingOptionsPreset\n */\nexport const EncodingOptionsPreset = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.EncodingOptionsPreset\",\n  [\n    {no: 0, name: \"H264_720P_30\"},\n    {no: 1, name: \"H264_720P_60\"},\n    {no: 2, name: \"H264_1080P_30\"},\n    {no: 3, name: \"H264_1080P_60\"},\n    {no: 4, name: \"PORTRAIT_H264_720P_30\"},\n    {no: 5, name: \"PORTRAIT_H264_720P_60\"},\n    {no: 6, name: \"PORTRAIT_H264_1080P_30\"},\n    {no: 7, name: \"PORTRAIT_H264_1080P_60\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.EgressStatus\n */\nexport const EgressStatus = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.EgressStatus\",\n  [\n    {no: 0, name: \"EGRESS_STARTING\"},\n    {no: 1, name: \"EGRESS_ACTIVE\"},\n    {no: 2, name: \"EGRESS_ENDING\"},\n    {no: 3, name: \"EGRESS_COMPLETE\"},\n    {no: 4, name: \"EGRESS_FAILED\"},\n    {no: 5, name: \"EGRESS_ABORTED\"},\n    {no: 6, name: \"EGRESS_LIMIT_REACHED\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.EgressSourceType\n */\nexport const EgressSourceType = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.EgressSourceType\",\n  [\n    {no: 0, name: \"EGRESS_SOURCE_TYPE_WEB\", localName: \"WEB\"},\n    {no: 1, name: \"EGRESS_SOURCE_TYPE_SDK\", localName: \"SDK\"},\n  ],\n);\n\n/**\n * composite using a web browser\n *\n * @generated from message livekit.RoomCompositeEgressRequest\n */\nexport const RoomCompositeEgressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RoomCompositeEgressRequest\",\n  () => [\n    { no: 1, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"layout\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"audio_only\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 15, name: \"audio_mixing\", kind: \"enum\", T: proto3.getEnumType(AudioMixing) },\n    { no: 4, name: \"video_only\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 5, name: \"custom_base_url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"file\", kind: \"message\", T: EncodedFileOutput, oneof: \"output\" },\n    { no: 7, name: \"stream\", kind: \"message\", T: StreamOutput, oneof: \"output\" },\n    { no: 10, name: \"segments\", kind: \"message\", T: SegmentedFileOutput, oneof: \"output\" },\n    { no: 8, name: \"preset\", kind: \"enum\", T: proto3.getEnumType(EncodingOptionsPreset), oneof: \"options\" },\n    { no: 9, name: \"advanced\", kind: \"message\", T: EncodingOptions, oneof: \"options\" },\n    { no: 11, name: \"file_outputs\", kind: \"message\", T: EncodedFileOutput, repeated: true },\n    { no: 12, name: \"stream_outputs\", kind: \"message\", T: StreamOutput, repeated: true },\n    { no: 13, name: \"segment_outputs\", kind: \"message\", T: SegmentedFileOutput, repeated: true },\n    { no: 14, name: \"image_outputs\", kind: \"message\", T: ImageOutput, repeated: true },\n    { no: 16, name: \"webhooks\", kind: \"message\", T: WebhookConfig, repeated: true },\n  ],\n);\n\n/**\n * record any website\n *\n * @generated from message livekit.WebEgressRequest\n */\nexport const WebEgressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.WebEgressRequest\",\n  () => [\n    { no: 1, name: \"url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"audio_only\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 3, name: \"video_only\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 12, name: \"await_start_signal\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 4, name: \"file\", kind: \"message\", T: EncodedFileOutput, oneof: \"output\" },\n    { no: 5, name: \"stream\", kind: \"message\", T: StreamOutput, oneof: \"output\" },\n    { no: 6, name: \"segments\", kind: \"message\", T: SegmentedFileOutput, oneof: \"output\" },\n    { no: 7, name: \"preset\", kind: \"enum\", T: proto3.getEnumType(EncodingOptionsPreset), oneof: \"options\" },\n    { no: 8, name: \"advanced\", kind: \"message\", T: EncodingOptions, oneof: \"options\" },\n    { no: 9, name: \"file_outputs\", kind: \"message\", T: EncodedFileOutput, repeated: true },\n    { no: 10, name: \"stream_outputs\", kind: \"message\", T: StreamOutput, repeated: true },\n    { no: 11, name: \"segment_outputs\", kind: \"message\", T: SegmentedFileOutput, repeated: true },\n    { no: 13, name: \"image_outputs\", kind: \"message\", T: ImageOutput, repeated: true },\n    { no: 14, name: \"webhooks\", kind: \"message\", T: WebhookConfig, repeated: true },\n  ],\n);\n\n/**\n * record audio and video from a single participant\n *\n * @generated from message livekit.ParticipantEgressRequest\n */\nexport const ParticipantEgressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ParticipantEgressRequest\",\n  () => [\n    { no: 1, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"screen_share\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 4, name: \"preset\", kind: \"enum\", T: proto3.getEnumType(EncodingOptionsPreset), oneof: \"options\" },\n    { no: 5, name: \"advanced\", kind: \"message\", T: EncodingOptions, oneof: \"options\" },\n    { no: 6, name: \"file_outputs\", kind: \"message\", T: EncodedFileOutput, repeated: true },\n    { no: 7, name: \"stream_outputs\", kind: \"message\", T: StreamOutput, repeated: true },\n    { no: 8, name: \"segment_outputs\", kind: \"message\", T: SegmentedFileOutput, repeated: true },\n    { no: 9, name: \"image_outputs\", kind: \"message\", T: ImageOutput, repeated: true },\n    { no: 10, name: \"webhooks\", kind: \"message\", T: WebhookConfig, repeated: true },\n  ],\n);\n\n/**\n * containerize up to one audio and one video track\n *\n * @generated from message livekit.TrackCompositeEgressRequest\n */\nexport const TrackCompositeEgressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TrackCompositeEgressRequest\",\n  () => [\n    { no: 1, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"audio_track_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"video_track_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"file\", kind: \"message\", T: EncodedFileOutput, oneof: \"output\" },\n    { no: 5, name: \"stream\", kind: \"message\", T: StreamOutput, oneof: \"output\" },\n    { no: 8, name: \"segments\", kind: \"message\", T: SegmentedFileOutput, oneof: \"output\" },\n    { no: 6, name: \"preset\", kind: \"enum\", T: proto3.getEnumType(EncodingOptionsPreset), oneof: \"options\" },\n    { no: 7, name: \"advanced\", kind: \"message\", T: EncodingOptions, oneof: \"options\" },\n    { no: 11, name: \"file_outputs\", kind: \"message\", T: EncodedFileOutput, repeated: true },\n    { no: 12, name: \"stream_outputs\", kind: \"message\", T: StreamOutput, repeated: true },\n    { no: 13, name: \"segment_outputs\", kind: \"message\", T: SegmentedFileOutput, repeated: true },\n    { no: 14, name: \"image_outputs\", kind: \"message\", T: ImageOutput, repeated: true },\n    { no: 15, name: \"webhooks\", kind: \"message\", T: WebhookConfig, repeated: true },\n  ],\n);\n\n/**\n * record tracks individually, without transcoding\n *\n * @generated from message livekit.TrackEgressRequest\n */\nexport const TrackEgressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TrackEgressRequest\",\n  () => [\n    { no: 1, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"track_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"file\", kind: \"message\", T: DirectFileOutput, oneof: \"output\" },\n    { no: 4, name: \"websocket_url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, oneof: \"output\" },\n    { no: 5, name: \"webhooks\", kind: \"message\", T: WebhookConfig, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.EncodedFileOutput\n */\nexport const EncodedFileOutput = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.EncodedFileOutput\",\n  () => [\n    { no: 1, name: \"file_type\", kind: \"enum\", T: proto3.getEnumType(EncodedFileType) },\n    { no: 2, name: \"filepath\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"disable_manifest\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 3, name: \"s3\", kind: \"message\", T: S3Upload, oneof: \"output\" },\n    { no: 4, name: \"gcp\", kind: \"message\", T: GCPUpload, oneof: \"output\" },\n    { no: 5, name: \"azure\", kind: \"message\", T: AzureBlobUpload, oneof: \"output\" },\n    { no: 7, name: \"aliOSS\", kind: \"message\", T: AliOSSUpload, oneof: \"output\" },\n  ],\n);\n\n/**\n * Used to generate HLS segments or other kind of segmented output\n *\n * @generated from message livekit.SegmentedFileOutput\n */\nexport const SegmentedFileOutput = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SegmentedFileOutput\",\n  () => [\n    { no: 1, name: \"protocol\", kind: \"enum\", T: proto3.getEnumType(SegmentedFileProtocol) },\n    { no: 2, name: \"filename_prefix\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"playlist_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 11, name: \"live_playlist_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"segment_duration\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 10, name: \"filename_suffix\", kind: \"enum\", T: proto3.getEnumType(SegmentedFileSuffix) },\n    { no: 8, name: \"disable_manifest\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 5, name: \"s3\", kind: \"message\", T: S3Upload, oneof: \"output\" },\n    { no: 6, name: \"gcp\", kind: \"message\", T: GCPUpload, oneof: \"output\" },\n    { no: 7, name: \"azure\", kind: \"message\", T: AzureBlobUpload, oneof: \"output\" },\n    { no: 9, name: \"aliOSS\", kind: \"message\", T: AliOSSUpload, oneof: \"output\" },\n  ],\n);\n\n/**\n * @generated from message livekit.DirectFileOutput\n */\nexport const DirectFileOutput = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DirectFileOutput\",\n  () => [\n    { no: 1, name: \"filepath\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"disable_manifest\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 2, name: \"s3\", kind: \"message\", T: S3Upload, oneof: \"output\" },\n    { no: 3, name: \"gcp\", kind: \"message\", T: GCPUpload, oneof: \"output\" },\n    { no: 4, name: \"azure\", kind: \"message\", T: AzureBlobUpload, oneof: \"output\" },\n    { no: 6, name: \"aliOSS\", kind: \"message\", T: AliOSSUpload, oneof: \"output\" },\n  ],\n);\n\n/**\n * @generated from message livekit.ImageOutput\n */\nexport const ImageOutput = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ImageOutput\",\n  () => [\n    { no: 1, name: \"capture_interval\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 2, name: \"width\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 3, name: \"height\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 4, name: \"filename_prefix\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"filename_suffix\", kind: \"enum\", T: proto3.getEnumType(ImageFileSuffix) },\n    { no: 6, name: \"image_codec\", kind: \"enum\", T: proto3.getEnumType(ImageCodec) },\n    { no: 7, name: \"disable_manifest\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 8, name: \"s3\", kind: \"message\", T: S3Upload, oneof: \"output\" },\n    { no: 9, name: \"gcp\", kind: \"message\", T: GCPUpload, oneof: \"output\" },\n    { no: 10, name: \"azure\", kind: \"message\", T: AzureBlobUpload, oneof: \"output\" },\n    { no: 11, name: \"aliOSS\", kind: \"message\", T: AliOSSUpload, oneof: \"output\" },\n  ],\n);\n\n/**\n * @generated from message livekit.S3Upload\n */\nexport const S3Upload = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.S3Upload\",\n  () => [\n    { no: 1, name: \"access_key\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"secret\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 11, name: \"session_token\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"region\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"endpoint\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"bucket\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"force_path_style\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 7, name: \"metadata\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 8, name: \"tagging\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"content_disposition\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"proxy\", kind: \"message\", T: ProxyConfig },\n  ],\n);\n\n/**\n * @generated from message livekit.GCPUpload\n */\nexport const GCPUpload = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.GCPUpload\",\n  () => [\n    { no: 1, name: \"credentials\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"bucket\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"proxy\", kind: \"message\", T: ProxyConfig },\n  ],\n);\n\n/**\n * @generated from message livekit.AzureBlobUpload\n */\nexport const AzureBlobUpload = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AzureBlobUpload\",\n  () => [\n    { no: 1, name: \"account_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"account_key\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"container_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.AliOSSUpload\n */\nexport const AliOSSUpload = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AliOSSUpload\",\n  () => [\n    { no: 1, name: \"access_key\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"secret\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"region\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"endpoint\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"bucket\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ProxyConfig\n */\nexport const ProxyConfig = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ProxyConfig\",\n  () => [\n    { no: 1, name: \"url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.StreamOutput\n */\nexport const StreamOutput = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.StreamOutput\",\n  () => [\n    { no: 1, name: \"protocol\", kind: \"enum\", T: proto3.getEnumType(StreamProtocol) },\n    { no: 2, name: \"urls\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.EncodingOptions\n */\nexport const EncodingOptions = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.EncodingOptions\",\n  () => [\n    { no: 1, name: \"width\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 2, name: \"height\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 3, name: \"depth\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 4, name: \"framerate\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 5, name: \"audio_codec\", kind: \"enum\", T: proto3.getEnumType(AudioCodec) },\n    { no: 6, name: \"audio_bitrate\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 11, name: \"audio_quality\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 7, name: \"audio_frequency\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 8, name: \"video_codec\", kind: \"enum\", T: proto3.getEnumType(VideoCodec) },\n    { no: 9, name: \"video_bitrate\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 12, name: \"video_quality\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 10, name: \"key_frame_interval\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateLayoutRequest\n */\nexport const UpdateLayoutRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateLayoutRequest\",\n  () => [\n    { no: 1, name: \"egress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"layout\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateStreamRequest\n */\nexport const UpdateStreamRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateStreamRequest\",\n  () => [\n    { no: 1, name: \"egress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"add_output_urls\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 3, name: \"remove_output_urls\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.ListEgressRequest\n */\nexport const ListEgressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListEgressRequest\",\n  () => [\n    { no: 1, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"egress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"active\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ListEgressResponse\n */\nexport const ListEgressResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListEgressResponse\",\n  () => [\n    { no: 1, name: \"items\", kind: \"message\", T: EgressInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.StopEgressRequest\n */\nexport const StopEgressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.StopEgressRequest\",\n  () => [\n    { no: 1, name: \"egress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.EgressInfo\n */\nexport const EgressInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.EgressInfo\",\n  () => [\n    { no: 1, name: \"egress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"room_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 13, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 26, name: \"source_type\", kind: \"enum\", T: proto3.getEnumType(EgressSourceType) },\n    { no: 3, name: \"status\", kind: \"enum\", T: proto3.getEnumType(EgressStatus) },\n    { no: 10, name: \"started_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 11, name: \"ended_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 18, name: \"updated_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 21, name: \"details\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"error\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 22, name: \"error_code\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 4, name: \"room_composite\", kind: \"message\", T: RoomCompositeEgressRequest, oneof: \"request\" },\n    { no: 14, name: \"web\", kind: \"message\", T: WebEgressRequest, oneof: \"request\" },\n    { no: 19, name: \"participant\", kind: \"message\", T: ParticipantEgressRequest, oneof: \"request\" },\n    { no: 5, name: \"track_composite\", kind: \"message\", T: TrackCompositeEgressRequest, oneof: \"request\" },\n    { no: 6, name: \"track\", kind: \"message\", T: TrackEgressRequest, oneof: \"request\" },\n    { no: 7, name: \"stream\", kind: \"message\", T: StreamInfoList, oneof: \"result\" },\n    { no: 8, name: \"file\", kind: \"message\", T: FileInfo, oneof: \"result\" },\n    { no: 12, name: \"segments\", kind: \"message\", T: SegmentsInfo, oneof: \"result\" },\n    { no: 15, name: \"stream_results\", kind: \"message\", T: StreamInfo, repeated: true },\n    { no: 16, name: \"file_results\", kind: \"message\", T: FileInfo, repeated: true },\n    { no: 17, name: \"segment_results\", kind: \"message\", T: SegmentsInfo, repeated: true },\n    { no: 20, name: \"image_results\", kind: \"message\", T: ImagesInfo, repeated: true },\n    { no: 23, name: \"manifest_location\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 25, name: \"backup_storage_used\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.StreamInfoList\n * @deprecated\n */\nexport const StreamInfoList = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.StreamInfoList\",\n  () => [\n    { no: 1, name: \"info\", kind: \"message\", T: StreamInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.StreamInfo\n */\nexport const StreamInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.StreamInfo\",\n  () => [\n    { no: 1, name: \"url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"started_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 3, name: \"ended_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 4, name: \"duration\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 5, name: \"status\", kind: \"enum\", T: proto3.getEnumType(StreamInfo_Status) },\n    { no: 6, name: \"error\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from enum livekit.StreamInfo.Status\n */\nexport const StreamInfo_Status = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.StreamInfo.Status\",\n  [\n    {no: 0, name: \"ACTIVE\"},\n    {no: 1, name: \"FINISHED\"},\n    {no: 2, name: \"FAILED\"},\n  ],\n);\n\n/**\n * @generated from message livekit.FileInfo\n */\nexport const FileInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.FileInfo\",\n  () => [\n    { no: 1, name: \"filename\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"started_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 3, name: \"ended_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 6, name: \"duration\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 4, name: \"size\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 5, name: \"location\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SegmentsInfo\n */\nexport const SegmentsInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SegmentsInfo\",\n  () => [\n    { no: 1, name: \"playlist_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"live_playlist_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"duration\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 3, name: \"size\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 4, name: \"playlist_location\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"live_playlist_location\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"segment_count\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 6, name: \"started_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 7, name: \"ended_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ImagesInfo\n */\nexport const ImagesInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ImagesInfo\",\n  () => [\n    { no: 4, name: \"filename_prefix\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 1, name: \"image_count\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"started_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 3, name: \"ended_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.AutoParticipantEgress\n */\nexport const AutoParticipantEgress = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AutoParticipantEgress\",\n  () => [\n    { no: 1, name: \"preset\", kind: \"enum\", T: proto3.getEnumType(EncodingOptionsPreset), oneof: \"options\" },\n    { no: 2, name: \"advanced\", kind: \"message\", T: EncodingOptions, oneof: \"options\" },\n    { no: 3, name: \"file_outputs\", kind: \"message\", T: EncodedFileOutput, repeated: true },\n    { no: 4, name: \"segment_outputs\", kind: \"message\", T: SegmentedFileOutput, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.AutoTrackEgress\n */\nexport const AutoTrackEgress = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AutoTrackEgress\",\n  () => [\n    { no: 1, name: \"filepath\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"disable_manifest\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 2, name: \"s3\", kind: \"message\", T: S3Upload, oneof: \"output\" },\n    { no: 3, name: \"gcp\", kind: \"message\", T: GCPUpload, oneof: \"output\" },\n    { no: 4, name: \"azure\", kind: \"message\", T: AzureBlobUpload, oneof: \"output\" },\n    { no: 6, name: \"aliOSS\", kind: \"message\", T: AliOSSUpload, oneof: \"output\" },\n  ],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_ingress.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3 } from \"@bufbuild/protobuf\";\nimport { AudioCodec, TrackInfo, TrackSource, VideoCodec, VideoLayer } from \"./livekit_models_pb.js\";\n\n/**\n * @generated from enum livekit.IngressInput\n */\nexport const IngressInput = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.IngressInput\",\n  [\n    {no: 0, name: \"RTMP_INPUT\"},\n    {no: 1, name: \"WHIP_INPUT\"},\n    {no: 2, name: \"URL_INPUT\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.IngressAudioEncodingPreset\n */\nexport const IngressAudioEncodingPreset = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.IngressAudioEncodingPreset\",\n  [\n    {no: 0, name: \"OPUS_STEREO_96KBPS\"},\n    {no: 1, name: \"OPUS_MONO_64KBS\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.IngressVideoEncodingPreset\n */\nexport const IngressVideoEncodingPreset = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.IngressVideoEncodingPreset\",\n  [\n    {no: 0, name: \"H264_720P_30FPS_3_LAYERS\"},\n    {no: 1, name: \"H264_1080P_30FPS_3_LAYERS\"},\n    {no: 2, name: \"H264_540P_25FPS_2_LAYERS\"},\n    {no: 3, name: \"H264_720P_30FPS_1_LAYER\"},\n    {no: 4, name: \"H264_1080P_30FPS_1_LAYER\"},\n    {no: 5, name: \"H264_720P_30FPS_3_LAYERS_HIGH_MOTION\"},\n    {no: 6, name: \"H264_1080P_30FPS_3_LAYERS_HIGH_MOTION\"},\n    {no: 7, name: \"H264_540P_25FPS_2_LAYERS_HIGH_MOTION\"},\n    {no: 8, name: \"H264_720P_30FPS_1_LAYER_HIGH_MOTION\"},\n    {no: 9, name: \"H264_1080P_30FPS_1_LAYER_HIGH_MOTION\"},\n  ],\n);\n\n/**\n * @generated from message livekit.CreateIngressRequest\n */\nexport const CreateIngressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.CreateIngressRequest\",\n  () => [\n    { no: 1, name: \"input_type\", kind: \"enum\", T: proto3.getEnumType(IngressInput) },\n    { no: 9, name: \"url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"participant_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"participant_metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"bypass_transcoding\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 11, name: \"enable_transcoding\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, opt: true },\n    { no: 6, name: \"audio\", kind: \"message\", T: IngressAudioOptions },\n    { no: 7, name: \"video\", kind: \"message\", T: IngressVideoOptions },\n    { no: 12, name: \"enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, opt: true },\n  ],\n);\n\n/**\n * @generated from message livekit.IngressAudioOptions\n */\nexport const IngressAudioOptions = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.IngressAudioOptions\",\n  () => [\n    { no: 1, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"source\", kind: \"enum\", T: proto3.getEnumType(TrackSource) },\n    { no: 3, name: \"preset\", kind: \"enum\", T: proto3.getEnumType(IngressAudioEncodingPreset), oneof: \"encoding_options\" },\n    { no: 4, name: \"options\", kind: \"message\", T: IngressAudioEncodingOptions, oneof: \"encoding_options\" },\n  ],\n);\n\n/**\n * @generated from message livekit.IngressVideoOptions\n */\nexport const IngressVideoOptions = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.IngressVideoOptions\",\n  () => [\n    { no: 1, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"source\", kind: \"enum\", T: proto3.getEnumType(TrackSource) },\n    { no: 3, name: \"preset\", kind: \"enum\", T: proto3.getEnumType(IngressVideoEncodingPreset), oneof: \"encoding_options\" },\n    { no: 4, name: \"options\", kind: \"message\", T: IngressVideoEncodingOptions, oneof: \"encoding_options\" },\n  ],\n);\n\n/**\n * @generated from message livekit.IngressAudioEncodingOptions\n */\nexport const IngressAudioEncodingOptions = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.IngressAudioEncodingOptions\",\n  () => [\n    { no: 1, name: \"audio_codec\", kind: \"enum\", T: proto3.getEnumType(AudioCodec) },\n    { no: 2, name: \"bitrate\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"disable_dtx\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 4, name: \"channels\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.IngressVideoEncodingOptions\n */\nexport const IngressVideoEncodingOptions = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.IngressVideoEncodingOptions\",\n  () => [\n    { no: 1, name: \"video_codec\", kind: \"enum\", T: proto3.getEnumType(VideoCodec) },\n    { no: 2, name: \"frame_rate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 3, name: \"layers\", kind: \"message\", T: VideoLayer, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.IngressInfo\n */\nexport const IngressInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.IngressInfo\",\n  () => [\n    { no: 1, name: \"ingress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"stream_key\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"input_type\", kind: \"enum\", T: proto3.getEnumType(IngressInput) },\n    { no: 13, name: \"bypass_transcoding\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 15, name: \"enable_transcoding\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, opt: true },\n    { no: 6, name: \"audio\", kind: \"message\", T: IngressAudioOptions },\n    { no: 7, name: \"video\", kind: \"message\", T: IngressVideoOptions },\n    { no: 8, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"participant_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 14, name: \"participant_metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 11, name: \"reusable\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 12, name: \"state\", kind: \"message\", T: IngressState },\n    { no: 16, name: \"enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, opt: true },\n  ],\n);\n\n/**\n * @generated from message livekit.IngressState\n */\nexport const IngressState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.IngressState\",\n  () => [\n    { no: 1, name: \"status\", kind: \"enum\", T: proto3.getEnumType(IngressState_Status) },\n    { no: 2, name: \"error\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"video\", kind: \"message\", T: InputVideoState },\n    { no: 4, name: \"audio\", kind: \"message\", T: InputAudioState },\n    { no: 5, name: \"room_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"started_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 8, name: \"ended_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 10, name: \"updated_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 9, name: \"resource_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"tracks\", kind: \"message\", T: TrackInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from enum livekit.IngressState.Status\n */\nexport const IngressState_Status = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.IngressState.Status\",\n  [\n    {no: 0, name: \"ENDPOINT_INACTIVE\"},\n    {no: 1, name: \"ENDPOINT_BUFFERING\"},\n    {no: 2, name: \"ENDPOINT_PUBLISHING\"},\n    {no: 3, name: \"ENDPOINT_ERROR\"},\n    {no: 4, name: \"ENDPOINT_COMPLETE\"},\n  ],\n);\n\n/**\n * @generated from message livekit.InputVideoState\n */\nexport const InputVideoState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.InputVideoState\",\n  () => [\n    { no: 1, name: \"mime_type\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"average_bitrate\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"width\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"height\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 5, name: \"framerate\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n  ],\n);\n\n/**\n * @generated from message livekit.InputAudioState\n */\nexport const InputAudioState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.InputAudioState\",\n  () => [\n    { no: 1, name: \"mime_type\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"average_bitrate\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"channels\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"sample_rate\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateIngressRequest\n */\nexport const UpdateIngressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateIngressRequest\",\n  () => [\n    { no: 1, name: \"ingress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"participant_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"participant_metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"bypass_transcoding\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, opt: true },\n    { no: 10, name: \"enable_transcoding\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, opt: true },\n    { no: 6, name: \"audio\", kind: \"message\", T: IngressAudioOptions },\n    { no: 7, name: \"video\", kind: \"message\", T: IngressVideoOptions },\n    { no: 11, name: \"enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, opt: true },\n  ],\n);\n\n/**\n * @generated from message livekit.ListIngressRequest\n */\nexport const ListIngressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListIngressRequest\",\n  () => [\n    { no: 1, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"ingress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ListIngressResponse\n */\nexport const ListIngressResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListIngressResponse\",\n  () => [\n    { no: 1, name: \"items\", kind: \"message\", T: IngressInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.DeleteIngressRequest\n */\nexport const DeleteIngressRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DeleteIngressRequest\",\n  () => [\n    { no: 1, name: \"ingress_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_room.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3 } from \"@bufbuild/protobuf\";\nimport { RoomAgentDispatch } from \"./livekit_agent_dispatch_pb.js\";\nimport { AutoParticipantEgress, AutoTrackEgress, RoomCompositeEgressRequest } from \"./livekit_egress_pb.js\";\nimport { DataPacket_Kind, ParticipantInfo, ParticipantPermission, ParticipantTracks, Room, TrackInfo } from \"./livekit_models_pb.js\";\n\n/**\n * @generated from message livekit.CreateRoomRequest\n */\nexport const CreateRoomRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.CreateRoomRequest\",\n  () => [\n    { no: 1, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 12, name: \"room_preset\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"empty_timeout\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 10, name: \"departure_timeout\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"max_participants\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"node_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"egress\", kind: \"message\", T: RoomEgress },\n    { no: 7, name: \"min_playout_delay\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 8, name: \"max_playout_delay\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 9, name: \"sync_streams\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 13, name: \"replay_enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 14, name: \"agents\", kind: \"message\", T: RoomAgentDispatch, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.RoomEgress\n */\nexport const RoomEgress = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RoomEgress\",\n  () => [\n    { no: 1, name: \"room\", kind: \"message\", T: RoomCompositeEgressRequest },\n    { no: 3, name: \"participant\", kind: \"message\", T: AutoParticipantEgress },\n    { no: 2, name: \"tracks\", kind: \"message\", T: AutoTrackEgress },\n  ],\n);\n\n/**\n * @generated from message livekit.RoomAgent\n */\nexport const RoomAgent = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RoomAgent\",\n  () => [\n    { no: 1, name: \"dispatches\", kind: \"message\", T: RoomAgentDispatch, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.ListRoomsRequest\n */\nexport const ListRoomsRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListRoomsRequest\",\n  () => [\n    { no: 1, name: \"names\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.ListRoomsResponse\n */\nexport const ListRoomsResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListRoomsResponse\",\n  () => [\n    { no: 1, name: \"rooms\", kind: \"message\", T: Room, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.DeleteRoomRequest\n */\nexport const DeleteRoomRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DeleteRoomRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.DeleteRoomResponse\n */\nexport const DeleteRoomResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DeleteRoomResponse\",\n  [],\n);\n\n/**\n * @generated from message livekit.ListParticipantsRequest\n */\nexport const ListParticipantsRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListParticipantsRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ListParticipantsResponse\n */\nexport const ListParticipantsResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListParticipantsResponse\",\n  () => [\n    { no: 1, name: \"participants\", kind: \"message\", T: ParticipantInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.RoomParticipantIdentity\n */\nexport const RoomParticipantIdentity = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RoomParticipantIdentity\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RemoveParticipantResponse\n */\nexport const RemoveParticipantResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RemoveParticipantResponse\",\n  [],\n);\n\n/**\n * @generated from message livekit.MuteRoomTrackRequest\n */\nexport const MuteRoomTrackRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.MuteRoomTrackRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"muted\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.MuteRoomTrackResponse\n */\nexport const MuteRoomTrackResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.MuteRoomTrackResponse\",\n  () => [\n    { no: 1, name: \"track\", kind: \"message\", T: TrackInfo },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateParticipantRequest\n */\nexport const UpdateParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateParticipantRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"permission\", kind: \"message\", T: ParticipantPermission },\n    { no: 5, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateSubscriptionsRequest\n */\nexport const UpdateSubscriptionsRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateSubscriptionsRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"track_sids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"subscribe\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 5, name: \"participant_tracks\", kind: \"message\", T: ParticipantTracks, repeated: true },\n  ],\n);\n\n/**\n * empty for now\n *\n * @generated from message livekit.UpdateSubscriptionsResponse\n */\nexport const UpdateSubscriptionsResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateSubscriptionsResponse\",\n  [],\n);\n\n/**\n * @generated from message livekit.SendDataRequest\n */\nexport const SendDataRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SendDataRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"data\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */ },\n    { no: 3, name: \"kind\", kind: \"enum\", T: proto3.getEnumType(DataPacket_Kind) },\n    { no: 4, name: \"destination_sids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 6, name: \"destination_identities\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 5, name: \"topic\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 7, name: \"nonce\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SendDataResponse\n */\nexport const SendDataResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SendDataResponse\",\n  [],\n);\n\n/**\n * @generated from message livekit.UpdateRoomMetadataRequest\n */\nexport const UpdateRoomMetadataRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateRoomMetadataRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RoomConfiguration\n */\nexport const RoomConfiguration = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RoomConfiguration\",\n  () => [\n    { no: 1, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"empty_timeout\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"departure_timeout\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"max_participants\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 5, name: \"egress\", kind: \"message\", T: RoomEgress },\n    { no: 7, name: \"min_playout_delay\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 8, name: \"max_playout_delay\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 9, name: \"sync_streams\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 10, name: \"agents\", kind: \"message\", T: RoomAgentDispatch, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.ForwardParticipantRequest\n */\nexport const ForwardParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ForwardParticipantRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"destination_room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ForwardParticipantResponse\n */\nexport const ForwardParticipantResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ForwardParticipantResponse\",\n  [],\n);\n\n/**\n * @generated from message livekit.MoveParticipantRequest\n */\nexport const MoveParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.MoveParticipantRequest\",\n  () => [\n    { no: 1, name: \"room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"destination_room\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.MoveParticipantResponse\n */\nexport const MoveParticipantResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.MoveParticipantResponse\",\n  [],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_rtc.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3 } from \"@bufbuild/protobuf\";\nimport { AudioTrackFeature, BackupCodecPolicy, ClientConfiguration, Codec, ConnectionQuality, DisconnectReason, Encryption_Type, ParticipantInfo, ParticipantTracks, Room, ServerInfo, SpeakerInfo, SubscriptionError, TrackInfo, TrackSource, TrackType, VideoLayer, VideoQuality } from \"./livekit_models_pb.js\";\n\n/**\n * @generated from enum livekit.SignalTarget\n */\nexport const SignalTarget = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SignalTarget\",\n  [\n    {no: 0, name: \"PUBLISHER\"},\n    {no: 1, name: \"SUBSCRIBER\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.StreamState\n */\nexport const StreamState = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.StreamState\",\n  [\n    {no: 0, name: \"ACTIVE\"},\n    {no: 1, name: \"PAUSED\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.CandidateProtocol\n */\nexport const CandidateProtocol = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.CandidateProtocol\",\n  [\n    {no: 0, name: \"UDP\"},\n    {no: 1, name: \"TCP\"},\n    {no: 2, name: \"TLS\"},\n  ],\n);\n\n/**\n * @generated from message livekit.SignalRequest\n */\nexport const SignalRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SignalRequest\",\n  () => [\n    { no: 1, name: \"offer\", kind: \"message\", T: SessionDescription, oneof: \"message\" },\n    { no: 2, name: \"answer\", kind: \"message\", T: SessionDescription, oneof: \"message\" },\n    { no: 3, name: \"trickle\", kind: \"message\", T: TrickleRequest, oneof: \"message\" },\n    { no: 4, name: \"add_track\", kind: \"message\", T: AddTrackRequest, oneof: \"message\" },\n    { no: 5, name: \"mute\", kind: \"message\", T: MuteTrackRequest, oneof: \"message\" },\n    { no: 6, name: \"subscription\", kind: \"message\", T: UpdateSubscription, oneof: \"message\" },\n    { no: 7, name: \"track_setting\", kind: \"message\", T: UpdateTrackSettings, oneof: \"message\" },\n    { no: 8, name: \"leave\", kind: \"message\", T: LeaveRequest, oneof: \"message\" },\n    { no: 10, name: \"update_layers\", kind: \"message\", T: UpdateVideoLayers, oneof: \"message\" },\n    { no: 11, name: \"subscription_permission\", kind: \"message\", T: SubscriptionPermission, oneof: \"message\" },\n    { no: 12, name: \"sync_state\", kind: \"message\", T: SyncState, oneof: \"message\" },\n    { no: 13, name: \"simulate\", kind: \"message\", T: SimulateScenario, oneof: \"message\" },\n    { no: 14, name: \"ping\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */, oneof: \"message\" },\n    { no: 15, name: \"update_metadata\", kind: \"message\", T: UpdateParticipantMetadata, oneof: \"message\" },\n    { no: 16, name: \"ping_req\", kind: \"message\", T: Ping, oneof: \"message\" },\n    { no: 17, name: \"update_audio_track\", kind: \"message\", T: UpdateLocalAudioTrack, oneof: \"message\" },\n    { no: 18, name: \"update_video_track\", kind: \"message\", T: UpdateLocalVideoTrack, oneof: \"message\" },\n  ],\n);\n\n/**\n * @generated from message livekit.SignalResponse\n */\nexport const SignalResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SignalResponse\",\n  () => [\n    { no: 1, name: \"join\", kind: \"message\", T: JoinResponse, oneof: \"message\" },\n    { no: 2, name: \"answer\", kind: \"message\", T: SessionDescription, oneof: \"message\" },\n    { no: 3, name: \"offer\", kind: \"message\", T: SessionDescription, oneof: \"message\" },\n    { no: 4, name: \"trickle\", kind: \"message\", T: TrickleRequest, oneof: \"message\" },\n    { no: 5, name: \"update\", kind: \"message\", T: ParticipantUpdate, oneof: \"message\" },\n    { no: 6, name: \"track_published\", kind: \"message\", T: TrackPublishedResponse, oneof: \"message\" },\n    { no: 8, name: \"leave\", kind: \"message\", T: LeaveRequest, oneof: \"message\" },\n    { no: 9, name: \"mute\", kind: \"message\", T: MuteTrackRequest, oneof: \"message\" },\n    { no: 10, name: \"speakers_changed\", kind: \"message\", T: SpeakersChanged, oneof: \"message\" },\n    { no: 11, name: \"room_update\", kind: \"message\", T: RoomUpdate, oneof: \"message\" },\n    { no: 12, name: \"connection_quality\", kind: \"message\", T: ConnectionQualityUpdate, oneof: \"message\" },\n    { no: 13, name: \"stream_state_update\", kind: \"message\", T: StreamStateUpdate, oneof: \"message\" },\n    { no: 14, name: \"subscribed_quality_update\", kind: \"message\", T: SubscribedQualityUpdate, oneof: \"message\" },\n    { no: 15, name: \"subscription_permission_update\", kind: \"message\", T: SubscriptionPermissionUpdate, oneof: \"message\" },\n    { no: 16, name: \"refresh_token\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, oneof: \"message\" },\n    { no: 17, name: \"track_unpublished\", kind: \"message\", T: TrackUnpublishedResponse, oneof: \"message\" },\n    { no: 18, name: \"pong\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */, oneof: \"message\" },\n    { no: 19, name: \"reconnect\", kind: \"message\", T: ReconnectResponse, oneof: \"message\" },\n    { no: 20, name: \"pong_resp\", kind: \"message\", T: Pong, oneof: \"message\" },\n    { no: 21, name: \"subscription_response\", kind: \"message\", T: SubscriptionResponse, oneof: \"message\" },\n    { no: 22, name: \"request_response\", kind: \"message\", T: RequestResponse, oneof: \"message\" },\n    { no: 23, name: \"track_subscribed\", kind: \"message\", T: TrackSubscribed, oneof: \"message\" },\n    { no: 24, name: \"room_moved\", kind: \"message\", T: RoomMovedResponse, oneof: \"message\" },\n  ],\n);\n\n/**\n * @generated from message livekit.SimulcastCodec\n */\nexport const SimulcastCodec = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SimulcastCodec\",\n  () => [\n    { no: 1, name: \"codec\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"cid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.AddTrackRequest\n */\nexport const AddTrackRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.AddTrackRequest\",\n  () => [\n    { no: 1, name: \"cid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"type\", kind: \"enum\", T: proto3.getEnumType(TrackType) },\n    { no: 4, name: \"width\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 5, name: \"height\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 6, name: \"muted\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 7, name: \"disable_dtx\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 8, name: \"source\", kind: \"enum\", T: proto3.getEnumType(TrackSource) },\n    { no: 9, name: \"layers\", kind: \"message\", T: VideoLayer, repeated: true },\n    { no: 10, name: \"simulcast_codecs\", kind: \"message\", T: SimulcastCodec, repeated: true },\n    { no: 11, name: \"sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 12, name: \"stereo\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 13, name: \"disable_red\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 14, name: \"encryption\", kind: \"enum\", T: proto3.getEnumType(Encryption_Type) },\n    { no: 15, name: \"stream\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 16, name: \"backup_codec_policy\", kind: \"enum\", T: proto3.getEnumType(BackupCodecPolicy) },\n    { no: 17, name: \"audio_features\", kind: \"enum\", T: proto3.getEnumType(AudioTrackFeature), repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.TrickleRequest\n */\nexport const TrickleRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TrickleRequest\",\n  () => [\n    { no: 1, name: \"candidateInit\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"target\", kind: \"enum\", T: proto3.getEnumType(SignalTarget) },\n    { no: 3, name: \"final\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.MuteTrackRequest\n */\nexport const MuteTrackRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.MuteTrackRequest\",\n  () => [\n    { no: 1, name: \"sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"muted\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.JoinResponse\n */\nexport const JoinResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.JoinResponse\",\n  () => [\n    { no: 1, name: \"room\", kind: \"message\", T: Room },\n    { no: 2, name: \"participant\", kind: \"message\", T: ParticipantInfo },\n    { no: 3, name: \"other_participants\", kind: \"message\", T: ParticipantInfo, repeated: true },\n    { no: 4, name: \"server_version\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"ice_servers\", kind: \"message\", T: ICEServer, repeated: true },\n    { no: 6, name: \"subscriber_primary\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 7, name: \"alternative_url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"client_configuration\", kind: \"message\", T: ClientConfiguration },\n    { no: 9, name: \"server_region\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"ping_timeout\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 11, name: \"ping_interval\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 12, name: \"server_info\", kind: \"message\", T: ServerInfo },\n    { no: 13, name: \"sif_trailer\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */ },\n    { no: 14, name: \"enabled_publish_codecs\", kind: \"message\", T: Codec, repeated: true },\n    { no: 15, name: \"fast_publish\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ReconnectResponse\n */\nexport const ReconnectResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ReconnectResponse\",\n  () => [\n    { no: 1, name: \"ice_servers\", kind: \"message\", T: ICEServer, repeated: true },\n    { no: 2, name: \"client_configuration\", kind: \"message\", T: ClientConfiguration },\n    { no: 3, name: \"server_info\", kind: \"message\", T: ServerInfo },\n    { no: 4, name: \"last_message_seq\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.TrackPublishedResponse\n */\nexport const TrackPublishedResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TrackPublishedResponse\",\n  () => [\n    { no: 1, name: \"cid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"track\", kind: \"message\", T: TrackInfo },\n  ],\n);\n\n/**\n * @generated from message livekit.TrackUnpublishedResponse\n */\nexport const TrackUnpublishedResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TrackUnpublishedResponse\",\n  () => [\n    { no: 1, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SessionDescription\n */\nexport const SessionDescription = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SessionDescription\",\n  () => [\n    { no: 1, name: \"type\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"sdp\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"id\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ParticipantUpdate\n */\nexport const ParticipantUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ParticipantUpdate\",\n  () => [\n    { no: 1, name: \"participants\", kind: \"message\", T: ParticipantInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateSubscription\n */\nexport const UpdateSubscription = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateSubscription\",\n  () => [\n    { no: 1, name: \"track_sids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 2, name: \"subscribe\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 3, name: \"participant_tracks\", kind: \"message\", T: ParticipantTracks, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateTrackSettings\n */\nexport const UpdateTrackSettings = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateTrackSettings\",\n  () => [\n    { no: 1, name: \"track_sids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 3, name: \"disabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 4, name: \"quality\", kind: \"enum\", T: proto3.getEnumType(VideoQuality) },\n    { no: 5, name: \"width\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 6, name: \"height\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 7, name: \"fps\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 8, name: \"priority\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateLocalAudioTrack\n */\nexport const UpdateLocalAudioTrack = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateLocalAudioTrack\",\n  () => [\n    { no: 1, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"features\", kind: \"enum\", T: proto3.getEnumType(AudioTrackFeature), repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateLocalVideoTrack\n */\nexport const UpdateLocalVideoTrack = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateLocalVideoTrack\",\n  () => [\n    { no: 1, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"width\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"height\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.LeaveRequest\n */\nexport const LeaveRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.LeaveRequest\",\n  () => [\n    { no: 1, name: \"can_reconnect\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 2, name: \"reason\", kind: \"enum\", T: proto3.getEnumType(DisconnectReason) },\n    { no: 3, name: \"action\", kind: \"enum\", T: proto3.getEnumType(LeaveRequest_Action) },\n    { no: 4, name: \"regions\", kind: \"message\", T: RegionSettings },\n  ],\n);\n\n/**\n * indicates action clients should take on receiving this message\n *\n * @generated from enum livekit.LeaveRequest.Action\n */\nexport const LeaveRequest_Action = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.LeaveRequest.Action\",\n  [\n    {no: 0, name: \"DISCONNECT\"},\n    {no: 1, name: \"RESUME\"},\n    {no: 2, name: \"RECONNECT\"},\n  ],\n);\n\n/**\n * message to indicate published video track dimensions are changing\n *\n * @generated from message livekit.UpdateVideoLayers\n * @deprecated\n */\nexport const UpdateVideoLayers = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateVideoLayers\",\n  () => [\n    { no: 1, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"layers\", kind: \"message\", T: VideoLayer, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateParticipantMetadata\n */\nexport const UpdateParticipantMetadata = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateParticipantMetadata\",\n  () => [\n    { no: 1, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 4, name: \"request_id\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ICEServer\n */\nexport const ICEServer = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ICEServer\",\n  () => [\n    { no: 1, name: \"urls\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 2, name: \"username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"credential\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SpeakersChanged\n */\nexport const SpeakersChanged = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SpeakersChanged\",\n  () => [\n    { no: 1, name: \"speakers\", kind: \"message\", T: SpeakerInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.RoomUpdate\n */\nexport const RoomUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RoomUpdate\",\n  () => [\n    { no: 1, name: \"room\", kind: \"message\", T: Room },\n  ],\n);\n\n/**\n * @generated from message livekit.ConnectionQualityInfo\n */\nexport const ConnectionQualityInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ConnectionQualityInfo\",\n  () => [\n    { no: 1, name: \"participant_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"quality\", kind: \"enum\", T: proto3.getEnumType(ConnectionQuality) },\n    { no: 3, name: \"score\", kind: \"scalar\", T: 2 /* ScalarType.FLOAT */ },\n  ],\n);\n\n/**\n * @generated from message livekit.ConnectionQualityUpdate\n */\nexport const ConnectionQualityUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ConnectionQualityUpdate\",\n  () => [\n    { no: 1, name: \"updates\", kind: \"message\", T: ConnectionQualityInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.StreamStateInfo\n */\nexport const StreamStateInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.StreamStateInfo\",\n  () => [\n    { no: 1, name: \"participant_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"state\", kind: \"enum\", T: proto3.getEnumType(StreamState) },\n  ],\n);\n\n/**\n * @generated from message livekit.StreamStateUpdate\n */\nexport const StreamStateUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.StreamStateUpdate\",\n  () => [\n    { no: 1, name: \"stream_states\", kind: \"message\", T: StreamStateInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.SubscribedQuality\n */\nexport const SubscribedQuality = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SubscribedQuality\",\n  () => [\n    { no: 1, name: \"quality\", kind: \"enum\", T: proto3.getEnumType(VideoQuality) },\n    { no: 2, name: \"enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SubscribedCodec\n */\nexport const SubscribedCodec = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SubscribedCodec\",\n  () => [\n    { no: 1, name: \"codec\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"qualities\", kind: \"message\", T: SubscribedQuality, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.SubscribedQualityUpdate\n */\nexport const SubscribedQualityUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SubscribedQualityUpdate\",\n  () => [\n    { no: 1, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"subscribed_qualities\", kind: \"message\", T: SubscribedQuality, repeated: true },\n    { no: 3, name: \"subscribed_codecs\", kind: \"message\", T: SubscribedCodec, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.TrackPermission\n */\nexport const TrackPermission = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TrackPermission\",\n  () => [\n    { no: 1, name: \"participant_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"all_tracks\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 3, name: \"track_sids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SubscriptionPermission\n */\nexport const SubscriptionPermission = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SubscriptionPermission\",\n  () => [\n    { no: 1, name: \"all_participants\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 2, name: \"track_permissions\", kind: \"message\", T: TrackPermission, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.SubscriptionPermissionUpdate\n */\nexport const SubscriptionPermissionUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SubscriptionPermissionUpdate\",\n  () => [\n    { no: 1, name: \"participant_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"allowed\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RoomMovedResponse\n */\nexport const RoomMovedResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RoomMovedResponse\",\n  () => [\n    { no: 1, name: \"room\", kind: \"message\", T: Room },\n    { no: 2, name: \"token\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"participant\", kind: \"message\", T: ParticipantInfo },\n    { no: 4, name: \"other_participants\", kind: \"message\", T: ParticipantInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.SyncState\n */\nexport const SyncState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SyncState\",\n  () => [\n    { no: 1, name: \"answer\", kind: \"message\", T: SessionDescription },\n    { no: 2, name: \"subscription\", kind: \"message\", T: UpdateSubscription },\n    { no: 3, name: \"publish_tracks\", kind: \"message\", T: TrackPublishedResponse, repeated: true },\n    { no: 4, name: \"data_channels\", kind: \"message\", T: DataChannelInfo, repeated: true },\n    { no: 5, name: \"offer\", kind: \"message\", T: SessionDescription },\n    { no: 6, name: \"track_sids_disabled\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 7, name: \"datachannel_receive_states\", kind: \"message\", T: DataChannelReceiveState, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.DataChannelReceiveState\n */\nexport const DataChannelReceiveState = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataChannelReceiveState\",\n  () => [\n    { no: 1, name: \"publisher_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"last_seq\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.DataChannelInfo\n */\nexport const DataChannelInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DataChannelInfo\",\n  () => [\n    { no: 1, name: \"label\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"id\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"target\", kind: \"enum\", T: proto3.getEnumType(SignalTarget) },\n  ],\n);\n\n/**\n * @generated from message livekit.SimulateScenario\n */\nexport const SimulateScenario = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SimulateScenario\",\n  () => [\n    { no: 1, name: \"speaker_update\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */, oneof: \"scenario\" },\n    { no: 2, name: \"node_failure\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, oneof: \"scenario\" },\n    { no: 3, name: \"migration\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, oneof: \"scenario\" },\n    { no: 4, name: \"server_leave\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, oneof: \"scenario\" },\n    { no: 5, name: \"switch_candidate_protocol\", kind: \"enum\", T: proto3.getEnumType(CandidateProtocol), oneof: \"scenario\" },\n    { no: 6, name: \"subscriber_bandwidth\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */, oneof: \"scenario\" },\n    { no: 7, name: \"disconnect_signal_on_resume\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, oneof: \"scenario\" },\n    { no: 8, name: \"disconnect_signal_on_resume_no_messages\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, oneof: \"scenario\" },\n    { no: 9, name: \"leave_request_full_reconnect\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */, oneof: \"scenario\" },\n  ],\n);\n\n/**\n * @generated from message livekit.Ping\n */\nexport const Ping = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.Ping\",\n  () => [\n    { no: 1, name: \"timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"rtt\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.Pong\n */\nexport const Pong = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.Pong\",\n  () => [\n    { no: 1, name: \"last_ping_timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"timestamp\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.RegionSettings\n */\nexport const RegionSettings = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RegionSettings\",\n  () => [\n    { no: 1, name: \"regions\", kind: \"message\", T: RegionInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.RegionInfo\n */\nexport const RegionInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RegionInfo\",\n  () => [\n    { no: 1, name: \"region\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"distance\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SubscriptionResponse\n */\nexport const SubscriptionResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SubscriptionResponse\",\n  () => [\n    { no: 1, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"err\", kind: \"enum\", T: proto3.getEnumType(SubscriptionError) },\n  ],\n);\n\n/**\n * @generated from message livekit.RequestResponse\n */\nexport const RequestResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.RequestResponse\",\n  () => [\n    { no: 1, name: \"request_id\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 2, name: \"reason\", kind: \"enum\", T: proto3.getEnumType(RequestResponse_Reason) },\n    { no: 3, name: \"message\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from enum livekit.RequestResponse.Reason\n */\nexport const RequestResponse_Reason = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.RequestResponse.Reason\",\n  [\n    {no: 0, name: \"OK\"},\n    {no: 1, name: \"NOT_FOUND\"},\n    {no: 2, name: \"NOT_ALLOWED\"},\n    {no: 3, name: \"LIMIT_EXCEEDED\"},\n  ],\n);\n\n/**\n * @generated from message livekit.TrackSubscribed\n */\nexport const TrackSubscribed = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TrackSubscribed\",\n  () => [\n    { no: 1, name: \"track_sid\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_sip.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { Duration, proto3 } from \"@bufbuild/protobuf\";\nimport { DisconnectReason, ListUpdate, Pagination } from \"./livekit_models_pb.js\";\nimport { RoomConfiguration } from \"./livekit_room_pb.js\";\n\n/**\n * @generated from enum livekit.SIPStatusCode\n */\nexport const SIPStatusCode = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPStatusCode\",\n  [\n    {no: 0, name: \"SIP_STATUS_UNKNOWN\"},\n    {no: 100, name: \"SIP_STATUS_TRYING\"},\n    {no: 180, name: \"SIP_STATUS_RINGING\"},\n    {no: 181, name: \"SIP_STATUS_CALL_IS_FORWARDED\"},\n    {no: 182, name: \"SIP_STATUS_QUEUED\"},\n    {no: 183, name: \"SIP_STATUS_SESSION_PROGRESS\"},\n    {no: 200, name: \"SIP_STATUS_OK\"},\n    {no: 202, name: \"SIP_STATUS_ACCEPTED\"},\n    {no: 301, name: \"SIP_STATUS_MOVED_PERMANENTLY\"},\n    {no: 302, name: \"SIP_STATUS_MOVED_TEMPORARILY\"},\n    {no: 305, name: \"SIP_STATUS_USE_PROXY\"},\n    {no: 400, name: \"SIP_STATUS_BAD_REQUEST\"},\n    {no: 401, name: \"SIP_STATUS_UNAUTHORIZED\"},\n    {no: 402, name: \"SIP_STATUS_PAYMENT_REQUIRED\"},\n    {no: 403, name: \"SIP_STATUS_FORBIDDEN\"},\n    {no: 404, name: \"SIP_STATUS_NOTFOUND\"},\n    {no: 405, name: \"SIP_STATUS_METHOD_NOT_ALLOWED\"},\n    {no: 406, name: \"SIP_STATUS_NOT_ACCEPTABLE\"},\n    {no: 407, name: \"SIP_STATUS_PROXY_AUTH_REQUIRED\"},\n    {no: 408, name: \"SIP_STATUS_REQUEST_TIMEOUT\"},\n    {no: 409, name: \"SIP_STATUS_CONFLICT\"},\n    {no: 410, name: \"SIP_STATUS_GONE\"},\n    {no: 413, name: \"SIP_STATUS_REQUEST_ENTITY_TOO_LARGE\"},\n    {no: 414, name: \"SIP_STATUS_REQUEST_URI_TOO_LONG\"},\n    {no: 415, name: \"SIP_STATUS_UNSUPPORTED_MEDIA_TYPE\"},\n    {no: 416, name: \"SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE\"},\n    {no: 420, name: \"SIP_STATUS_BAD_EXTENSION\"},\n    {no: 421, name: \"SIP_STATUS_EXTENSION_REQUIRED\"},\n    {no: 423, name: \"SIP_STATUS_INTERVAL_TOO_BRIEF\"},\n    {no: 480, name: \"SIP_STATUS_TEMPORARILY_UNAVAILABLE\"},\n    {no: 481, name: \"SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS\"},\n    {no: 482, name: \"SIP_STATUS_LOOP_DETECTED\"},\n    {no: 483, name: \"SIP_STATUS_TOO_MANY_HOPS\"},\n    {no: 484, name: \"SIP_STATUS_ADDRESS_INCOMPLETE\"},\n    {no: 485, name: \"SIP_STATUS_AMBIGUOUS\"},\n    {no: 486, name: \"SIP_STATUS_BUSY_HERE\"},\n    {no: 487, name: \"SIP_STATUS_REQUEST_TERMINATED\"},\n    {no: 488, name: \"SIP_STATUS_NOT_ACCEPTABLE_HERE\"},\n    {no: 500, name: \"SIP_STATUS_INTERNAL_SERVER_ERROR\"},\n    {no: 501, name: \"SIP_STATUS_NOT_IMPLEMENTED\"},\n    {no: 502, name: \"SIP_STATUS_BAD_GATEWAY\"},\n    {no: 503, name: \"SIP_STATUS_SERVICE_UNAVAILABLE\"},\n    {no: 504, name: \"SIP_STATUS_GATEWAY_TIMEOUT\"},\n    {no: 505, name: \"SIP_STATUS_VERSION_NOT_SUPPORTED\"},\n    {no: 513, name: \"SIP_STATUS_MESSAGE_TOO_LARGE\"},\n    {no: 600, name: \"SIP_STATUS_GLOBAL_BUSY_EVERYWHERE\"},\n    {no: 603, name: \"SIP_STATUS_GLOBAL_DECLINE\"},\n    {no: 604, name: \"SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE\"},\n    {no: 606, name: \"SIP_STATUS_GLOBAL_NOT_ACCEPTABLE\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SIPTransport\n */\nexport const SIPTransport = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPTransport\",\n  [\n    {no: 0, name: \"SIP_TRANSPORT_AUTO\"},\n    {no: 1, name: \"SIP_TRANSPORT_UDP\"},\n    {no: 2, name: \"SIP_TRANSPORT_TCP\"},\n    {no: 3, name: \"SIP_TRANSPORT_TLS\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SIPHeaderOptions\n */\nexport const SIPHeaderOptions = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPHeaderOptions\",\n  [\n    {no: 0, name: \"SIP_NO_HEADERS\"},\n    {no: 1, name: \"SIP_X_HEADERS\"},\n    {no: 2, name: \"SIP_ALL_HEADERS\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SIPMediaEncryption\n */\nexport const SIPMediaEncryption = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPMediaEncryption\",\n  [\n    {no: 0, name: \"SIP_MEDIA_ENCRYPT_DISABLE\"},\n    {no: 1, name: \"SIP_MEDIA_ENCRYPT_ALLOW\"},\n    {no: 2, name: \"SIP_MEDIA_ENCRYPT_REQUIRE\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SIPCallStatus\n */\nexport const SIPCallStatus = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPCallStatus\",\n  [\n    {no: 0, name: \"SCS_CALL_INCOMING\"},\n    {no: 1, name: \"SCS_PARTICIPANT_JOINED\"},\n    {no: 2, name: \"SCS_ACTIVE\"},\n    {no: 3, name: \"SCS_DISCONNECTED\"},\n    {no: 4, name: \"SCS_ERROR\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SIPTransferStatus\n */\nexport const SIPTransferStatus = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPTransferStatus\",\n  [\n    {no: 0, name: \"STS_TRANSFER_ONGOING\"},\n    {no: 1, name: \"STS_TRANSFER_FAILED\"},\n    {no: 2, name: \"STS_TRANSFER_SUCCESSFUL\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SIPFeature\n */\nexport const SIPFeature = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPFeature\",\n  [\n    {no: 0, name: \"NONE\"},\n    {no: 1, name: \"KRISP_ENABLED\"},\n  ],\n);\n\n/**\n * @generated from enum livekit.SIPCallDirection\n */\nexport const SIPCallDirection = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPCallDirection\",\n  [\n    {no: 0, name: \"SCD_UNKNOWN\"},\n    {no: 1, name: \"SCD_INBOUND\"},\n    {no: 2, name: \"SCD_OUTBOUND\"},\n  ],\n);\n\n/**\n * SIPStatus is returned as an error detail in CreateSIPParticipant.\n *\n * @generated from message livekit.SIPStatus\n */\nexport const SIPStatus = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPStatus\",\n  () => [\n    { no: 1, name: \"code\", kind: \"enum\", T: proto3.getEnumType(SIPStatusCode) },\n    { no: 2, name: \"status\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.CreateSIPTrunkRequest\n * @deprecated\n */\nexport const CreateSIPTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.CreateSIPTrunkRequest\",\n  () => [\n    { no: 1, name: \"inbound_addresses\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 2, name: \"outbound_address\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"outbound_number\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"inbound_numbers_regex\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 9, name: \"inbound_numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 5, name: \"inbound_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"inbound_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"outbound_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"outbound_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 11, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPTrunkInfo\n * @deprecated\n */\nexport const SIPTrunkInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPTrunkInfo\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 14, name: \"kind\", kind: \"enum\", T: proto3.getEnumType(SIPTrunkInfo_TrunkKind) },\n    { no: 2, name: \"inbound_addresses\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 3, name: \"outbound_address\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"outbound_number\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 13, name: \"transport\", kind: \"enum\", T: proto3.getEnumType(SIPTransport) },\n    { no: 5, name: \"inbound_numbers_regex\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 10, name: \"inbound_numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 6, name: \"inbound_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"inbound_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"outbound_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"outbound_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 11, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 12, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from enum livekit.SIPTrunkInfo.TrunkKind\n */\nexport const SIPTrunkInfo_TrunkKind = /*@__PURE__*/ proto3.makeEnum(\n  \"livekit.SIPTrunkInfo.TrunkKind\",\n  [\n    {no: 0, name: \"TRUNK_LEGACY\"},\n    {no: 1, name: \"TRUNK_INBOUND\"},\n    {no: 2, name: \"TRUNK_OUTBOUND\"},\n  ],\n);\n\n/**\n * @generated from message livekit.CreateSIPInboundTrunkRequest\n */\nexport const CreateSIPInboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.CreateSIPInboundTrunkRequest\",\n  () => [\n    { no: 1, name: \"trunk\", kind: \"message\", T: SIPInboundTrunkInfo },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateSIPInboundTrunkRequest\n */\nexport const UpdateSIPInboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateSIPInboundTrunkRequest\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"replace\", kind: \"message\", T: SIPInboundTrunkInfo, oneof: \"action\" },\n    { no: 3, name: \"update\", kind: \"message\", T: SIPInboundTrunkUpdate, oneof: \"action\" },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPInboundTrunkInfo\n */\nexport const SIPInboundTrunkInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPInboundTrunkInfo\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 5, name: \"allowed_addresses\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 6, name: \"allowed_numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 7, name: \"auth_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"auth_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"headers\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 10, name: \"headers_to_attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 14, name: \"attributes_to_headers\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 15, name: \"include_headers\", kind: \"enum\", T: proto3.getEnumType(SIPHeaderOptions) },\n    { no: 11, name: \"ringing_timeout\", kind: \"message\", T: Duration },\n    { no: 12, name: \"max_call_duration\", kind: \"message\", T: Duration },\n    { no: 13, name: \"krisp_enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 16, name: \"media_encryption\", kind: \"enum\", T: proto3.getEnumType(SIPMediaEncryption) },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPInboundTrunkUpdate\n */\nexport const SIPInboundTrunkUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPInboundTrunkUpdate\",\n  () => [\n    { no: 1, name: \"numbers\", kind: \"message\", T: ListUpdate },\n    { no: 2, name: \"allowed_addresses\", kind: \"message\", T: ListUpdate },\n    { no: 3, name: \"allowed_numbers\", kind: \"message\", T: ListUpdate },\n    { no: 4, name: \"auth_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 5, name: \"auth_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 6, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 7, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 8, name: \"media_encryption\", kind: \"enum\", T: proto3.getEnumType(SIPMediaEncryption), opt: true },\n  ],\n);\n\n/**\n * @generated from message livekit.CreateSIPOutboundTrunkRequest\n */\nexport const CreateSIPOutboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.CreateSIPOutboundTrunkRequest\",\n  () => [\n    { no: 1, name: \"trunk\", kind: \"message\", T: SIPOutboundTrunkInfo },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateSIPOutboundTrunkRequest\n */\nexport const UpdateSIPOutboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateSIPOutboundTrunkRequest\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"replace\", kind: \"message\", T: SIPOutboundTrunkInfo, oneof: \"action\" },\n    { no: 3, name: \"update\", kind: \"message\", T: SIPOutboundTrunkUpdate, oneof: \"action\" },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPOutboundTrunkInfo\n */\nexport const SIPOutboundTrunkInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPOutboundTrunkInfo\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"address\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 14, name: \"destination_country\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"transport\", kind: \"enum\", T: proto3.getEnumType(SIPTransport) },\n    { no: 6, name: \"numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 7, name: \"auth_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"auth_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"headers\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 10, name: \"headers_to_attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 11, name: \"attributes_to_headers\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 12, name: \"include_headers\", kind: \"enum\", T: proto3.getEnumType(SIPHeaderOptions) },\n    { no: 13, name: \"media_encryption\", kind: \"enum\", T: proto3.getEnumType(SIPMediaEncryption) },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPOutboundTrunkUpdate\n */\nexport const SIPOutboundTrunkUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPOutboundTrunkUpdate\",\n  () => [\n    { no: 1, name: \"address\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 2, name: \"transport\", kind: \"enum\", T: proto3.getEnumType(SIPTransport), opt: true },\n    { no: 9, name: \"destination_country\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 3, name: \"numbers\", kind: \"message\", T: ListUpdate },\n    { no: 4, name: \"auth_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 5, name: \"auth_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 6, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 7, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 8, name: \"media_encryption\", kind: \"enum\", T: proto3.getEnumType(SIPMediaEncryption), opt: true },\n  ],\n);\n\n/**\n * @generated from message livekit.GetSIPInboundTrunkRequest\n */\nexport const GetSIPInboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.GetSIPInboundTrunkRequest\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.GetSIPInboundTrunkResponse\n */\nexport const GetSIPInboundTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.GetSIPInboundTrunkResponse\",\n  () => [\n    { no: 1, name: \"trunk\", kind: \"message\", T: SIPInboundTrunkInfo },\n  ],\n);\n\n/**\n * @generated from message livekit.GetSIPOutboundTrunkRequest\n */\nexport const GetSIPOutboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.GetSIPOutboundTrunkRequest\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.GetSIPOutboundTrunkResponse\n */\nexport const GetSIPOutboundTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.GetSIPOutboundTrunkResponse\",\n  () => [\n    { no: 1, name: \"trunk\", kind: \"message\", T: SIPOutboundTrunkInfo },\n  ],\n);\n\n/**\n * @generated from message livekit.ListSIPTrunkRequest\n * @deprecated\n */\nexport const ListSIPTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListSIPTrunkRequest\",\n  () => [\n    { no: 1, name: \"page\", kind: \"message\", T: Pagination },\n  ],\n);\n\n/**\n * @generated from message livekit.ListSIPTrunkResponse\n * @deprecated\n */\nexport const ListSIPTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListSIPTrunkResponse\",\n  () => [\n    { no: 1, name: \"items\", kind: \"message\", T: SIPTrunkInfo, repeated: true },\n  ],\n);\n\n/**\n * ListSIPInboundTrunkRequest lists inbound trunks for given filters. If no filters are set, all trunks are listed.\n *\n * @generated from message livekit.ListSIPInboundTrunkRequest\n */\nexport const ListSIPInboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListSIPInboundTrunkRequest\",\n  () => [\n    { no: 3, name: \"page\", kind: \"message\", T: Pagination },\n    { no: 1, name: \"trunk_ids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 2, name: \"numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.ListSIPInboundTrunkResponse\n */\nexport const ListSIPInboundTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListSIPInboundTrunkResponse\",\n  () => [\n    { no: 1, name: \"items\", kind: \"message\", T: SIPInboundTrunkInfo, repeated: true },\n  ],\n);\n\n/**\n * ListSIPOutboundTrunkRequest lists outbound trunks for given filters. If no filters are set, all trunks are listed.\n *\n * @generated from message livekit.ListSIPOutboundTrunkRequest\n */\nexport const ListSIPOutboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListSIPOutboundTrunkRequest\",\n  () => [\n    { no: 3, name: \"page\", kind: \"message\", T: Pagination },\n    { no: 1, name: \"trunk_ids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 2, name: \"numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.ListSIPOutboundTrunkResponse\n */\nexport const ListSIPOutboundTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListSIPOutboundTrunkResponse\",\n  () => [\n    { no: 1, name: \"items\", kind: \"message\", T: SIPOutboundTrunkInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.DeleteSIPTrunkRequest\n */\nexport const DeleteSIPTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DeleteSIPTrunkRequest\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPDispatchRuleDirect\n */\nexport const SIPDispatchRuleDirect = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPDispatchRuleDirect\",\n  () => [\n    { no: 1, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"pin\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPDispatchRuleIndividual\n */\nexport const SIPDispatchRuleIndividual = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPDispatchRuleIndividual\",\n  () => [\n    { no: 1, name: \"room_prefix\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"pin\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPDispatchRuleCallee\n */\nexport const SIPDispatchRuleCallee = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPDispatchRuleCallee\",\n  () => [\n    { no: 1, name: \"room_prefix\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"pin\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"randomize\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPDispatchRule\n */\nexport const SIPDispatchRule = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPDispatchRule\",\n  () => [\n    { no: 1, name: \"dispatch_rule_direct\", kind: \"message\", T: SIPDispatchRuleDirect, oneof: \"rule\" },\n    { no: 2, name: \"dispatch_rule_individual\", kind: \"message\", T: SIPDispatchRuleIndividual, oneof: \"rule\" },\n    { no: 3, name: \"dispatch_rule_callee\", kind: \"message\", T: SIPDispatchRuleCallee, oneof: \"rule\" },\n  ],\n);\n\n/**\n * @generated from message livekit.CreateSIPDispatchRuleRequest\n */\nexport const CreateSIPDispatchRuleRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.CreateSIPDispatchRuleRequest\",\n  () => [\n    { no: 10, name: \"dispatch_rule\", kind: \"message\", T: SIPDispatchRuleInfo },\n    { no: 1, name: \"rule\", kind: \"message\", T: SIPDispatchRule },\n    { no: 2, name: \"trunk_ids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 3, name: \"hide_phone_number\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 6, name: \"inbound_numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 8, name: \"room_preset\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"room_config\", kind: \"message\", T: RoomConfiguration },\n  ],\n);\n\n/**\n * @generated from message livekit.UpdateSIPDispatchRuleRequest\n */\nexport const UpdateSIPDispatchRuleRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.UpdateSIPDispatchRuleRequest\",\n  () => [\n    { no: 1, name: \"sip_dispatch_rule_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"replace\", kind: \"message\", T: SIPDispatchRuleInfo, oneof: \"action\" },\n    { no: 3, name: \"update\", kind: \"message\", T: SIPDispatchRuleUpdate, oneof: \"action\" },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPDispatchRuleInfo\n */\nexport const SIPDispatchRuleInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPDispatchRuleInfo\",\n  () => [\n    { no: 1, name: \"sip_dispatch_rule_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"rule\", kind: \"message\", T: SIPDispatchRule },\n    { no: 3, name: \"trunk_ids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"hide_phone_number\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 7, name: \"inbound_numbers\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 5, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 9, name: \"room_preset\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"room_config\", kind: \"message\", T: RoomConfiguration },\n    { no: 11, name: \"krisp_enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 12, name: \"media_encryption\", kind: \"enum\", T: proto3.getEnumType(SIPMediaEncryption) },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPDispatchRuleUpdate\n */\nexport const SIPDispatchRuleUpdate = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPDispatchRuleUpdate\",\n  () => [\n    { no: 1, name: \"trunk_ids\", kind: \"message\", T: ListUpdate },\n    { no: 2, name: \"rule\", kind: \"message\", T: SIPDispatchRule },\n    { no: 3, name: \"name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 4, name: \"metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, opt: true },\n    { no: 5, name: \"attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 6, name: \"media_encryption\", kind: \"enum\", T: proto3.getEnumType(SIPMediaEncryption), opt: true },\n  ],\n);\n\n/**\n * ListSIPDispatchRuleRequest lists dispatch rules for given filters. If no filters are set, all rules are listed.\n *\n * @generated from message livekit.ListSIPDispatchRuleRequest\n */\nexport const ListSIPDispatchRuleRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListSIPDispatchRuleRequest\",\n  () => [\n    { no: 3, name: \"page\", kind: \"message\", T: Pagination },\n    { no: 1, name: \"dispatch_rule_ids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 2, name: \"trunk_ids\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.ListSIPDispatchRuleResponse\n */\nexport const ListSIPDispatchRuleResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.ListSIPDispatchRuleResponse\",\n  () => [\n    { no: 1, name: \"items\", kind: \"message\", T: SIPDispatchRuleInfo, repeated: true },\n  ],\n);\n\n/**\n * @generated from message livekit.DeleteSIPDispatchRuleRequest\n */\nexport const DeleteSIPDispatchRuleRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.DeleteSIPDispatchRuleRequest\",\n  () => [\n    { no: 1, name: \"sip_dispatch_rule_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPOutboundConfig\n */\nexport const SIPOutboundConfig = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPOutboundConfig\",\n  () => [\n    { no: 1, name: \"hostname\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"destination_country\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"transport\", kind: \"enum\", T: proto3.getEnumType(SIPTransport) },\n    { no: 3, name: \"auth_username\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"auth_password\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"headers_to_attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 6, name: \"attributes_to_headers\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n  ],\n);\n\n/**\n * A SIP Participant is a singular SIP session connected to a LiveKit room via\n * a SIP Trunk into a SIP DispatchRule\n *\n * @generated from message livekit.CreateSIPParticipantRequest\n */\nexport const CreateSIPParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.CreateSIPParticipantRequest\",\n  () => [\n    { no: 1, name: \"sip_trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 20, name: \"trunk\", kind: \"message\", T: SIPOutboundConfig },\n    { no: 2, name: \"sip_call_to\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 15, name: \"sip_number\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"participant_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"participant_metadata\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"participant_attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 5, name: \"dtmf\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"play_ringtone\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 13, name: \"play_dialtone\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 10, name: \"hide_phone_number\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 16, name: \"headers\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 17, name: \"include_headers\", kind: \"enum\", T: proto3.getEnumType(SIPHeaderOptions) },\n    { no: 11, name: \"ringing_timeout\", kind: \"message\", T: Duration },\n    { no: 12, name: \"max_call_duration\", kind: \"message\", T: Duration },\n    { no: 14, name: \"krisp_enabled\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 18, name: \"media_encryption\", kind: \"enum\", T: proto3.getEnumType(SIPMediaEncryption) },\n    { no: 19, name: \"wait_until_answered\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPParticipantInfo\n */\nexport const SIPParticipantInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPParticipantInfo\",\n  () => [\n    { no: 1, name: \"participant_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"sip_call_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.TransferSIPParticipantRequest\n */\nexport const TransferSIPParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.TransferSIPParticipantRequest\",\n  () => [\n    { no: 1, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"transfer_to\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"play_dialtone\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 5, name: \"headers\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 6, name: \"ringing_timeout\", kind: \"message\", T: Duration },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPCallInfo\n */\nexport const SIPCallInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPCallInfo\",\n  () => [\n    { no: 1, name: \"call_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"trunk_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 16, name: \"dispatch_rule_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 17, name: \"region\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"room_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"room_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"participant_identity\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 18, name: \"participant_attributes\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 6, name: \"from_uri\", kind: \"message\", T: SIPUri },\n    { no: 7, name: \"to_uri\", kind: \"message\", T: SIPUri },\n    { no: 9, name: \"created_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 10, name: \"started_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 11, name: \"ended_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 14, name: \"enabled_features\", kind: \"enum\", T: proto3.getEnumType(SIPFeature), repeated: true },\n    { no: 15, name: \"call_direction\", kind: \"enum\", T: proto3.getEnumType(SIPCallDirection) },\n    { no: 8, name: \"call_status\", kind: \"enum\", T: proto3.getEnumType(SIPCallStatus) },\n    { no: 22, name: \"created_at_ns\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 23, name: \"started_at_ns\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 24, name: \"ended_at_ns\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 12, name: \"disconnect_reason\", kind: \"enum\", T: proto3.getEnumType(DisconnectReason) },\n    { no: 13, name: \"error\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 19, name: \"call_status_code\", kind: \"message\", T: SIPStatus },\n    { no: 20, name: \"audio_codec\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 21, name: \"media_encryption\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPTransferInfo\n */\nexport const SIPTransferInfo = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPTransferInfo\",\n  () => [\n    { no: 1, name: \"transfer_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"call_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"transfer_to\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"transfer_initiated_at_ns\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 5, name: \"transfer_completed_at_ns\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 6, name: \"transfer_status\", kind: \"enum\", T: proto3.getEnumType(SIPTransferStatus) },\n    { no: 7, name: \"error\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"transfer_status_code\", kind: \"message\", T: SIPStatus },\n  ],\n);\n\n/**\n * @generated from message livekit.SIPUri\n */\nexport const SIPUri = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.SIPUri\",\n  () => [\n    { no: 1, name: \"user\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"host\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"ip\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"port\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 5, name: \"transport\", kind: \"enum\", T: proto3.getEnumType(SIPTransport) },\n  ],\n);\n\n", "// Copyright 2023 LiveKit, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n// @generated by protoc-gen-es v1.10.1 with parameter \"target=dts+js\"\n// @generated from file livekit_webhook.proto (package livekit, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3 } from \"@bufbuild/protobuf\";\nimport { ParticipantInfo, Room, TrackInfo } from \"./livekit_models_pb.js\";\nimport { EgressInfo } from \"./livekit_egress_pb.js\";\nimport { IngressInfo } from \"./livekit_ingress_pb.js\";\n\n/**\n * @generated from message livekit.WebhookEvent\n */\nexport const WebhookEvent = /*@__PURE__*/ proto3.makeMessageType(\n  \"livekit.WebhookEvent\",\n  () => [\n    { no: 1, name: \"event\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"room\", kind: \"message\", T: Room },\n    { no: 3, name: \"participant\", kind: \"message\", T: ParticipantInfo },\n    { no: 9, name: \"egress_info\", kind: \"message\", T: EgressInfo },\n    { no: 10, name: \"ingress_info\", kind: \"message\", T: IngressInfo },\n    { no: 8, name: \"track\", kind: \"message\", T: TrackInfo },\n    { no: 6, name: \"id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"created_at\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 11, name: \"num_dropped\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n  ],\n);\n\n", "// Generated by genversion.\nexport const version = '1.39.3';\n"], "names": ["proto3", "Timestamp", "Duration"], "mappings": ";;;;AAYO,MAAM,8BAAmCA,eAAA,CAAA,QAAA;AAAA,EAC9C,qBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,IAC/B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,IAC/B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,IAC/B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sCAAsC,EAAA;AAAA,IACpD,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,+CAA+C,EAAA;AAAA,IAC7D,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,qCAAqC,EAAA;AAAA,IACnD,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,+CAA+C,EAAA;AAAA,IAC7D,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,2CAA2C,EAAA;AAAA,IACzD,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,kDAAkD,EAAA;AAAA,IAChE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,4CAA4C,EAAA;AAAA,IAC1D,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,4CAA4C,EAAA;AAAA,IAC3D,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,qDAAqD,EAAA;AAAA,IACpE,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,uCAAuC,EAAA;AAAA,IACtD,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,+CAA+C,EAAA;AAAA,IAC9D,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,8DAA8D,EAAA;AAAA,IAC7E,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,wDAAwD,EAAA;AAAA,IACvE,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,0DAA0D,EAAA;AAAA,IACzE,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC9B,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,IAChC,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,IAC/B,EAAC,EAAA,EAAI,IAAM,EAAA,IAAA,EAAM,mCAAmC,EAAA;AAAA,GACtD;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC3E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,IAAM,EAAA,SAAA,EAAW,GAAGC,kBAAU,EAAA;AAAA,IACrE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,gBAAkB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,mCAAwCD,eAAA,CAAA,eAAA;AAAA,EACnD,0BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACrF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC1E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC3E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,IAAM,EAAA,SAAA,EAAW,GAAGC,kBAAU,EAAA;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,8BAAmCD,eAAA,CAAA,eAAA;AAAA,EAC9C,qBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACrF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,kBAAA,EAAoB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA0B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC1F,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,8BAA8B,IAAM,EAAA,SAAA,EAAW,GAAGC,kBAAU,EAAA;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,0BAAA,EAA4B,MAAM,SAAW,EAAA,CAAA,EAAGA,kBAAW,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GACtE;AACF;;ACtEO,MAAM,6BAAkCD,eAAA,CAAA,QAAA;AAAA,EAC7C,oBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,GACrB;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,QAAA;AAAA,EAC7C,oBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC7B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,GACrB;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,QAAA;AAAA,EAC7C,oBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,GACzB;AACF,EAAA;AAOO,MAAM,oCAAyCA,eAAA,CAAA,QAAA;AAAA,EACpD,2BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,IACjC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,GAC5B;AACF,EAAA;AAKO,MAAM,4BAAiCA,eAAA,CAAA,QAAA;AAAA,EAC5C,mBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,IACrB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,IACrB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,GACtB;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,QAAA;AAAA,EAC9C,qBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,IACvB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,GACpC;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,QAAA;AAAA,EAC/C,sBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,IACnB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,GACrB;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,QAAA;AAAA,EACpD,2BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,GACtB;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,QAAA;AAAA,EACtD,6BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,IACrB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,UAAU,EAAA;AAAA,IACxB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,GACzB;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,QAAA;AAAA,EACnD,0BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,IAC9B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,kBAAkB,EAAA;AAAA,IAChC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IAClC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,IAC/B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,qBAAqB,EAAA;AAAA,IACnC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,IAC9B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,aAAa,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,kBAAkB,EAAA;AAAA,IACjC,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC9B,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,IAClC,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IACnC,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,GAChC;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,QAAA;AAAA,EAClD,yBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,EAAA;AAAA,IACtC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,qBAAqB,EAAA;AAAA,IACnC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACpC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,qBAAqB,EAAA;AAAA,GACrC;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,QAAA;AAAA,EACpD,2BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACpC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,GACnC;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,QAAA;AAAA,EACpD,2BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACpC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACpC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACpC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gCAAgC,EAAA;AAAA,IAC9C,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,GACtC;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GACtE;AACF,EAAA;AAOO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACrF;AACF,EAAA;AAKO,MAAM,uBAA4BA,eAAA,CAAA,eAAA;AAAA,EACvC,cAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACnF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,KAAO,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC/E,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,SAAA,EAAW,GAAG,YAAa,EAAA;AAAA,GAC9D;AACF,EAAA;AAKO,MAAM,wBAA6BA,eAAA,CAAA,eAAA;AAAA,EACxC,eAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,qBAAuB,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,WAAW,CAAA,EAAG,UAAU,IAAK,EAAA;AAAA,IACvG;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,uBAAA;AAAA,MAAyB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,qBAAqB,CAAE,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACzE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,SAAA,EAAW,GAAG,qBAAsB,EAAA;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC3E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,oBAAoB,CAAE,EAAA;AAAA,IAClF,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IAC3H,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,mBAAA,EAAqB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,gBAAgB,CAAE,EAAA;AAAA,IAC3F,EAAE,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,cAAgB,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,0BAA0B,CAAA,EAAG,UAAU,IAAK,EAAA;AAAA,GAClH;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,QAAA;AAAA,EACxD,+BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,IACvB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,GAC9B;AACF,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,QAAA;AAAA,EACvD,8BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,UAAU,EAAA;AAAA,IACxB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,IACvB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,IACnB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,GACvB;AACF,EAAA;AAKO,MAAM,6CAAkDA,eAAA,CAAA,QAAA;AAAA,EAC7D,oCAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,aAAa,EAAA;AAAA,IAC3B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,GAC3B;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,EAAC;AACH,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,QAAA;AAAA,EAClD,yBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,IACnB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,GACxB;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,4BAAiCA,eAAA,CAAA,eAAA;AAAA,EAC5C,mBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,SAAS,CAAE,EAAA;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACzE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,WAAW,CAAE,EAAA;AAAA,IAC1E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC1E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,YAAA,EAAc,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,eAAe,CAAE,EAAA;AAAA,IACnF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,SAAA,EAAW,GAAG,YAAa,EAAA;AAAA,IAC5D,EAAE,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,gBAAkB,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,iBAAiB,CAAA,EAAG,UAAU,IAAK,EAAA;AAAA,IACzG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,qBAAA,EAAuB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,iBAAiB,CAAE,EAAA;AAAA,GAChG;AACF,EAAA;AAOO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACrE;AACF,EAAA;AAOO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,eAAe,CAAE,EAAA;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,wBAAA,EAA0B,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IACtE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IAClF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,OAAS,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IACvE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,aAAe,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IAClF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,MAAQ,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IACtE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IAChF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IACvF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,gBAAkB,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IACrF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IACzF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAClF;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,QAAA;AAAA,EAClD,yBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,UAAU,EAAA;AAAA,IACxB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,GACvB;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,eAAA;AAAA,EAC9C,qBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA0B;AAAA,IACvE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,kBAAA,EAAoB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAChG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,wBAAA,EAA0B,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAChF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,IAAA,EAAM,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACrF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA0B;AAAA,GACxE;AACF,EAAA;AAKO,MAAM,0BAA+BA,eAAA,CAAA,eAAA;AAAA,EAC1C,iBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,gCAAqCA,eAAA,CAAA,eAAA;AAAA,EAChD,uBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kCAAA;AAAA,MAAoC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChG;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,oBAAsB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,eAAA;AAAA,EAC9C,qBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,gBAAA,EAAkB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA0B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACxF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,yBAA8BA,eAAA,CAAA,eAAA;AAAA,EACzC,gBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC5E;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,eAAA;AAAA,EAC9C,qBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,IACvF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,QAAU,EAAA,KAAA,EAAO,OAAQ,EAAA;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,2BAAgCA,eAAA,CAAA,eAAA;AAAA,EAC3C,kBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC5F;AACF,EAAA;AAOO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,kBAAkB,CAAE,EAAA;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GAC/E;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,QAAA;AAAA,EACrD,4BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,UAAU,EAAA;AAAA,IACxB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,GACvB;AACF,EAAA;AAOO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,cAAc,CAAE,EAAA;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,QAAA;AAAA,EACjD,wBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,IACvB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,IAAI,EAAA;AAAA,IAClB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,IACrB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,IACvB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,IACvB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,IAAI,EAAA;AAAA,IAClB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,IACrB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACrB,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACvB,EAAC,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,GACxB;AACF,EAAA;AAOO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,kBAAmB,EAAA;AAAA,IAC/D,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,UAAU,IAAM,EAAA,SAAA,EAAW,GAAG,kBAAmB,EAAA;AAAA,IAChE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,mBAAA,EAAqB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,mBAAmB,CAAE,EAAA;AAAA,IAC7F,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,IAAM,EAAA,SAAA,EAAW,GAAG,cAAe,EAAA;AAAA,IACrE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,mBAAmB,CAAE,EAAA;AAAA,GACzF;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,kBAAA,EAAoB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,mBAAmB,CAAE,EAAA;AAAA,GAC9F;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,KAAO,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,KAAO,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,2BAAgCA,eAAA,CAAA,eAAA;AAAA,EAC3C,kBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,SAAA,EAAW,GAAGC,kBAAU,EAAA;AAAA,IAC3D,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,YAAY,IAAM,EAAA,SAAA,EAAW,GAAGA,kBAAU,EAAA;AAAA,IACzD;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC5E;AACF,EAAA;AAKO,MAAM,2BAAgCD,eAAA,CAAA,eAAA;AAAA,EAC3C,kBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,SAAA,EAAW,GAAGC,kBAAU,EAAA;AAAA,IAC3D,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,YAAY,IAAM,EAAA,SAAA,EAAW,GAAGA,kBAAU,EAAA;AAAA,IACzD;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,wBAAA;AAAA,MAA0B,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACtF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACnF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,uBAAA;AAAA,MAAyB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,wBAAA;AAAA,MAA0B,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,iBAAiB,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA0B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA4B,EAAA;AAAA,IAC9H;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,YAAY,IAAM,EAAA,SAAA,EAAW,GAAGA,kBAAU,EAAA;AAAA,IAC1D;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,YAAY,IAAM,EAAA,SAAA,EAAW,GAAGA,kBAAU,EAAA;AAAA,IAC1D;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC5E,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,kBAAkB,IAAM,EAAA,SAAA,EAAW,GAAGA,kBAAU,EAAA;AAAA,IAChE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,uBAAuB,IAAM,EAAA,SAAA,EAAW,GAAGA,kBAAU,EAAA;AAAA,IACrE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,gBAAgB,IAAM,EAAA,SAAA,EAAW,GAAG,QAAS,EAAA;AAAA,IAC7D,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,oBAAoB,IAAM,EAAA,SAAA,EAAW,GAAG,QAAS,EAAA;AAAA,IACjE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,wBAAwB,IAAM,EAAA,SAAA,EAAW,GAAG,QAAS,EAAA;AAAA,IACrE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,yBAAyB,IAAM,EAAA,SAAA,EAAW,GAAG,QAAS,EAAA;AAAA,GACxE;AACF,EAAA;AAKO,MAAM,wCAA6CD,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACjE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACxE;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,yBAAA;AAAA,MAA2B,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACtF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,8BAAA;AAAA,MAAgC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5F,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,SAAA,EAAW,GAAG,cAAe,EAAA;AAAA,IAChE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,SAAW,EAAA,CAAA,EAAG,cAAgB,EAAA,KAAA,EAAO,cAAe,EAAA;AAAA,IACvF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,qBAAA,EAAuB,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAClG;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,0BAAA;AAAA,MAA4B,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iCAAA;AAAA,MAAmC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,2BAAA;AAAA,MAA6B,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GAClF;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GAC5E;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,EAAC;AACH,EAAA;AAOO,MAAM,2CAAgDA,eAAA,CAAA,QAAA;AAAA,EAC3D,kCAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,UAAU,EAAA;AAAA,GAC1B;AACF,EAAA;AAOO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,gBAAA,EAAkB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,wBAAwB,CAAE,EAAA;AAAA,IAC/F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,qBAAA,EAAuB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACnG;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACzE;AAAA,EACA,EAAC,WAAW,uBAAuB,EAAA;AACrC,EAAA;AAOO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACtE;AAAA,EACA,EAAC,WAAW,uBAAuB,EAAA;AACrC,EAAA;AAOO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACvF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,eAAe,CAAE,EAAA;AAAA,IACvF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IAC1H,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,gBAAiB,EAAA;AAAA,IACjG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,gBAAiB,EAAA;AAAA,GACpG;AAAA,EACA,EAAC,WAAW,mBAAmB,EAAA;AACjC,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,eAAA;AAAA,EACnD,0BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACtE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,IAAA,EAAM,MAAM,QAAU,EAAA,CAAA,EAAG,EAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,GAC/E;AAAA,EACA,EAAC,WAAW,kBAAkB,EAAA;AAChC,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,GAC5H;AAAA,EACA,EAAC,WAAW,oBAAoB,EAAA;AAClC,EAAA;AAKO,MAAM,gCAAqCA,eAAA,CAAA,eAAA;AAAA,EAChD,uBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC7E;AACF;;AC38BO,MAAM,0BAA+BA,eAAA,CAAA,QAAA;AAAA,EAC1C,iBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,IACvB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,GAChC;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,QAAA;AAAA,EAC/C,sBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,SAAS,EAAA;AAAA,GACzB;AACF,EAAA;AAKO,MAAM,4BAAiCA,eAAA,CAAA,QAAA;AAAA,EAC5C,mBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,GAC3B;AACF,EAAA;AAKO,MAAM,sBAA2BA,eAAA,CAAA,eAAA;AAAA,EACtC,aAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,OAAO,CAAE,EAAA;AAAA,IACpE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,IAAK,EAAA;AAAA,IAChD,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,QAAS,EAAA;AAAA,GACvD;AACF,EAAA;AAKO,MAAM,2BAAgCA,eAAA,CAAA,eAAA;AAAA,EAC3C,kBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,SAAS,CAAE,EAAA;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC3E;AACF,EAAA;AAOO,MAAM,gCAAqCA,eAAA,CAAA,eAAA;AAAA,EAChD,uBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACvF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,oBAAsB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC1F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACzF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,GACxF;AACF,EAAA;AAOO,MAAM,gCAAqCA,eAAA,CAAA,eAAA;AAAA,EAChD,uBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,sBAAwB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACzF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,SAAW,EAAA,CAAA,EAAG,aAAe,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,cAAgB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,OAAO,CAAE,EAAA;AAAA,IACpE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,IAAK,EAAA;AAAA,IAChD,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,GACpE;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,OAAO,CAAE,EAAA;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACpF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,uBAAuB,IAAM,EAAA,SAAA,EAAW,GAAG,qBAAsB,EAAA;AAAA,GAClF;AACF,EAAA;AAKO,MAAM,yCAA8CA,eAAA,CAAA,eAAA;AAAA,EACzD,gCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,GAC/D;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACzF;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,OAAO,IAAM,EAAA,SAAA,EAAW,GAAG,GAAI,EAAA;AAAA,IAC9C;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACxE;AACF,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,0BAA0B,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,GACxI;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,SAAS,CAAE,EAAA;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAU,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,YAAY,CAAA,EAAG,KAAK,IAAK,EAAA;AAAA,IACtF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GAC5E;AACF,EAAA;AAKO,MAAM,gCAAqCA,eAAA,CAAA,eAAA;AAAA,EAChD,uBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,OAAO,IAAM,EAAA,SAAA,EAAW,GAAG,GAAI,EAAA;AAAA,IAC9C,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACxE;AACF;;AClPO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,2CAAgDA,eAAA,CAAA,eAAA;AAAA,EAC3D,kCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,4CAAiDA,eAAA,CAAA,eAAA;AAAA,EAC5D,mCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,kBAAA,EAAoB,MAAM,SAAW,EAAA,CAAA,EAAG,aAAe,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACvF;AACF,EAAA;AAKO,MAAM,gCAAqCA,eAAA,CAAA,eAAA;AAAA,EAChD,uBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,kBAAmB,EAAA;AAAA,GACjE;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,GAAK,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC/D;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GAC3E;AACF;;AC5EO,MAAM,kCAAuCA,eAAA,CAAA,QAAA;AAAA,EAClD,yBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,kBAAkB,EAAA;AAAA,IAChC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,IACnB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,GACrB;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,QAAA;AAAA,EACxD,+BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iCAAiC,EAAA;AAAA,IAC/C,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,GAC9B;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,QAAA;AAAA,EACtD,6BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,OAAO,EAAA;AAAA,IACrB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,GAC3B;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,QAAA;AAAA,EAClD,yBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IAClC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,EAAA;AAAA,IACtC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,6BAA6B,EAAA;AAAA,GAC7C;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,QAAA;AAAA,EACjD,wBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,kBAAkB,EAAA;AAAA,IAChC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,GACrB;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,QAAA;AAAA,EAC9C,qBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,IAC9B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IAClC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,EAAA;AAAA,GACxC;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,QAAA;AAAA,EACxD,+BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC7B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC7B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,uBAAuB,EAAA;AAAA,IACrC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,uBAAuB,EAAA;AAAA,IACrC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,EAAA;AAAA,IACtC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,EAAA;AAAA,GACxC;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,QAAA;AAAA,EAC/C,sBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,IAC/B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC7B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC7B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,IAC/B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC7B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,IAC9B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,GACtC;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,QAAA;AAAA,EACnD,0BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,wBAAA,EAA0B,WAAW,KAAK,EAAA;AAAA,IACxD,EAAC,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,wBAAA,EAA0B,WAAW,KAAK,EAAA;AAAA,GAC1D;AACF,EAAA;AAOO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACxE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,cAAA,EAAgB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,WAAW,CAAE,EAAA;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC3E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACrF,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAU,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,qBAAqB,CAAA,EAAG,OAAO,SAAU,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACtF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC3F,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,aAAe,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAChF;AACF,EAAA;AAOO,MAAM,mCAAwCA,eAAA,CAAA,eAAA;AAAA,EACnD,0BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACpF,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAU,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,qBAAqB,CAAA,EAAG,OAAO,SAAU,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACrF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC3F,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,aAAe,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAChF;AACF,EAAA;AAOO,MAAM,2CAAgDA,eAAA,CAAA,eAAA;AAAA,EAC3D,kCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC1E,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAU,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,qBAAqB,CAAA,EAAG,OAAO,SAAU,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACrF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAClF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC1F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAChF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,aAAe,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAChF;AACF,EAAA;AAOO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACpF,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAU,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,qBAAqB,CAAA,EAAG,OAAO,SAAU,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACtF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC3F,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,aAAe,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAChF;AACF,EAAA;AAOO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,gBAAkB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC9F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,aAAe,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC/E;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,eAAe,CAAE,EAAA;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,IAAA,EAAM,MAAM,SAAW,EAAA,CAAA,EAAG,QAAU,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACrE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,GAC7E;AACF,EAAA;AAOO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,qBAAqB,CAAE,EAAA;AAAA,IACtF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,mBAAmB,CAAE,EAAA;AAAA,IAC5F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,IAAA,EAAM,MAAM,SAAW,EAAA,CAAA,EAAG,QAAU,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACrE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,eAAA;AAAA,EACnD,0BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,IAAA,EAAM,MAAM,SAAW,EAAA,CAAA,EAAG,QAAU,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACrE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,eAAA;AAAA,EAC9C,qBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,eAAe,CAAE,EAAA;AAAA,IACvF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,UAAU,CAAE,EAAA;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,IAAA,EAAM,MAAM,SAAW,EAAA,CAAA,EAAG,QAAU,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACrE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,GAC9E;AACF,EAAA;AAKO,MAAM,2BAAgCA,eAAA,CAAA,eAAA;AAAA,EAC3C,kBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,YAAY,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACxH;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnF,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,WAAY,EAAA;AAAA,GAC3D;AACF,EAAA;AAKO,MAAM,4BAAiCA,eAAA,CAAA,eAAA;AAAA,EAC5C,mBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,WAAY,EAAA;AAAA,GAC1D;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAChF;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACxE;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,eAAA;AAAA,EAC9C,qBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,cAAc,CAAE,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,UAAU,CAAE,EAAA;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,UAAU,CAAE,EAAA;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACrF;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACxE;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC/F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,oBAAA,EAAsB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACpG;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,aAAA,EAAe,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,gBAAgB,CAAE,EAAA;AAAA,IACrF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC1E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,0BAA4B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAClG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,KAAA,EAAO,MAAM,SAAW,EAAA,CAAA,EAAG,gBAAkB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,wBAA0B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC9F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,2BAA6B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACpG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,cAAgB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,QAAU,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACrE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,QAAU,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC7E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACpF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACpF;AACF,EAAA;AAMO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACxE;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,iBAAiB,CAAE,EAAA;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,QAAA;AAAA,EACpD,2BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,UAAU,EAAA;AAAA,IACxB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,GACxB;AACF,EAAA;AAKO,MAAM,2BAAgCA,eAAA,CAAA,eAAA;AAAA,EAC3C,kBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,wBAAA;AAAA,MAA0B,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAU,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,qBAAqB,CAAA,EAAG,OAAO,SAAU,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACrF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC5F;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,IAAA,EAAM,MAAM,SAAW,EAAA,CAAA,EAAG,QAAU,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACrE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,GAC7E;AACF;;ACtlBO,MAAM,+BAAoCA,eAAA,CAAA,QAAA;AAAA,EAC/C,sBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,GAC3B;AACF,EAAA;AAKO,MAAM,6CAAkDA,eAAA,CAAA,QAAA;AAAA,EAC7D,oCAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IAClC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,GACjC;AACF,EAAA;AAKO,MAAM,6CAAkDA,eAAA,CAAA,QAAA;AAAA,EAC7D,oCAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,0BAA0B,EAAA;AAAA,IACxC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,2BAA2B,EAAA;AAAA,IACzC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,0BAA0B,EAAA;AAAA,IACxC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,yBAAyB,EAAA;AAAA,IACvC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,0BAA0B,EAAA;AAAA,IACxC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sCAAsC,EAAA;AAAA,IACpD,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,uCAAuC,EAAA;AAAA,IACrD,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sCAAsC,EAAA;AAAA,IACpD,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,qCAAqC,EAAA;AAAA,IACnD,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sCAAsC,EAAA;AAAA,GACtD;AACF,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAChF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,oBAAA,EAAsB,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC5F,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IAChE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IAChE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,GACnF;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,WAAW,CAAE,EAAA;AAAA,IAC1E,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAU,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,0BAA0B,CAAA,EAAG,OAAO,kBAAmB,EAAA;AAAA,IACpH,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,2BAA6B,EAAA,KAAA,EAAO,kBAAmB,EAAA;AAAA,GACvG;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,WAAW,CAAE,EAAA;AAAA,IAC1E,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAU,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,0BAA0B,CAAA,EAAG,OAAO,kBAAmB,EAAA;AAAA,IACpH,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,2BAA6B,EAAA,KAAA,EAAO,kBAAmB,EAAA;AAAA,GACvG;AACF,EAAA;AAKO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,UAAU,CAAE,EAAA;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,UAAU,CAAE,EAAA;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,eAAA;AAAA,EAC9C,qBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACjF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,oBAAA,EAAsB,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC5F,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IAChE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IAChE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACvE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,YAAa,EAAA;AAAA,IAC1D,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,GACnF;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,mBAAmB,CAAE,EAAA;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,IAC5D,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,IAC5D;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,QAAA;AAAA,EACtD,6BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,IACjC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IAClC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,qBAAqB,EAAA;AAAA,IACnC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,IAC9B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,GACnC;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GAC9E;AACF,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,oBAAA,EAAsB,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC3F,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,oBAAA,EAAsB,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC5F,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IAChE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IAChE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,GACnF;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC5E;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC5E;AACF;;ACnPO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACnF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,UAAU,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACxD;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC7E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAClF;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,0BAA2B,EAAA;AAAA,IACtE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,qBAAsB,EAAA;AAAA,IACxE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,UAAU,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,GAC/D;AACF,EAAA;AAKO,MAAM,4BAAiCA,eAAA,CAAA,eAAA;AAAA,EAC5C,mBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACrF;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,eAAA;AAAA,EACnD,0BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACvF;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACnE;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,EAAC;AACH,EAAA;AAKO,MAAM,0CAA+CA,eAAA,CAAA,eAAA;AAAA,EAC1D,iCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,2CAAgDA,eAAA,CAAA,eAAA;AAAA,EAC3D,kCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACrF;AACF,EAAA;AAKO,MAAM,0CAA+CA,eAAA,CAAA,eAAA;AAAA,EAC1D,iCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,4CAAiDA,eAAA,CAAA,eAAA;AAAA,EAC5D,mCAAA;AAAA,EACA,EAAC;AACH,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACrE;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,SAAU,EAAA;AAAA,GACxD;AACF,EAAA;AAKO,MAAM,2CAAgDA,eAAA,CAAA,eAAA;AAAA,EAC3D,kCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,SAAA,EAAW,GAAG,qBAAsB,EAAA;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,GAC5H;AACF,EAAA;AAKO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC1F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACvE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,oBAAA,EAAsB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC7F;AACF,EAAA;AAOO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,EAAC;AACH,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA0B;AAAA,IACpE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,eAAe,CAAE,EAAA;AAAA,IAC5E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,kBAAA,EAAoB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAChG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,wBAAA,EAA0B,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA0B;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,eAAA;AAAA,EACnD,0BAAA;AAAA,EACA,EAAC;AACH,EAAA;AAKO,MAAM,4CAAiDA,eAAA,CAAA,eAAA;AAAA,EAC5D,mCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACjF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,UAAU,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACxD;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC1E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAClF;AACF,EAAA;AAKO,MAAM,4CAAiDA,eAAA,CAAA,eAAA;AAAA,EAC5D,mCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAClF;AACF,EAAA;AAKO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,EAAC;AACH,EAAA;AAKO,MAAM,yCAA8CA,eAAA,CAAA,eAAA;AAAA,EACzD,gCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAClF;AACF,EAAA;AAKO,MAAM,0CAA+CA,eAAA,CAAA,eAAA;AAAA,EAC1D,iCAAA;AAAA,EACA,EAAC;AACH;;ACjRO,MAAM,+BAAoCA,eAAA,CAAA,QAAA;AAAA,EAC/C,sBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,GAC5B;AACF,EAAA;AAKO,MAAM,8BAAmCA,eAAA,CAAA,QAAA;AAAA,EAC9C,qBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,GACxB;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,QAAA;AAAA,EACpD,2BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,IACnB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,IACnB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,KAAK,EAAA;AAAA,GACrB;AACF,EAAA;AAKO,MAAM,gCAAqCA,eAAA,CAAA,eAAA;AAAA,EAChD,uBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAClF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,cAAgB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAClF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,gBAAkB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC1F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC3E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACzF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,yBAAA,EAA2B,MAAM,SAAW,EAAA,CAAA,EAAG,sBAAwB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACxG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,YAAA,EAAc,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,gBAAkB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,MAAA,EAAQ,MAAM,QAAU,EAAA,CAAA,EAAG,CAA0B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACtF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,yBAA2B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACnG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACvE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,oBAAA,EAAsB,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAClG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,oBAAA,EAAsB,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,GACpG;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC1E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAClF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,kBAAoB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,cAAgB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACjF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,SAAW,EAAA,CAAA,EAAG,sBAAwB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC/F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,SAAW,EAAA,CAAA,EAAG,gBAAkB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,kBAAA,EAAoB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC1F,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAChF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,oBAAA,EAAsB,MAAM,SAAW,EAAA,CAAA,EAAG,uBAAyB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACpG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,qBAAA,EAAuB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC/F,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,2BAAA,EAA6B,MAAM,SAAW,EAAA,CAAA,EAAG,uBAAyB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC3G,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,gCAAA,EAAkC,MAAM,SAAW,EAAA,CAAA,EAAG,4BAA8B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACrH,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,eAAA,EAAiB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAChG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,mBAAA,EAAqB,MAAM,SAAW,EAAA,CAAA,EAAG,wBAA0B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACpG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,MAAA,EAAQ,MAAM,QAAU,EAAA,CAAA,EAAG,CAA0B,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACtF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,WAAA,EAAa,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACrF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,WAAA,EAAa,MAAM,SAAW,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACxE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,uBAAA,EAAyB,MAAM,SAAW,EAAA,CAAA,EAAG,oBAAsB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IACpG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,kBAAA,EAAoB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC1F,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,kBAAA,EAAoB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,IAC1F,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,YAAA,EAAc,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,KAAA,EAAO,SAAU,EAAA;AAAA,GACxF;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACrE;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,SAAS,CAAE,EAAA;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACzE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,WAAW,CAAE,EAAA;AAAA,IAC1E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACxE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,kBAAA,EAAoB,MAAM,SAAW,EAAA,CAAA,EAAG,cAAgB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACvF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC1E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,YAAA,EAAc,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,eAAe,CAAE,EAAA;AAAA,IACnF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,qBAAA,EAAuB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,iBAAiB,CAAE,EAAA;AAAA,IAC9F,EAAE,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,gBAAkB,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,iBAAiB,CAAA,EAAG,UAAU,IAAK,EAAA;AAAA,GAC3G;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACrE;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,eAAA;AAAA,EACnD,0BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACrE;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,IAAK,EAAA;AAAA,IAChD,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,IAClE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,oBAAA,EAAsB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACzF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,oBAAA;AAAA,MAAsB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC7E,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IAC9D;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,wBAAA,EAA0B,MAAM,SAAW,EAAA,CAAA,EAAG,KAAO,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,MAAM,SAAW,EAAA,CAAA,EAAG,SAAW,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC5E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IAC/E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IAC7D;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GACnF;AACF,EAAA;AAKO,MAAM,yCAA8CA,eAAA,CAAA,eAAA;AAAA,EACzD,gCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,SAAU,EAAA;AAAA,GACxD;AACF,EAAA;AAKO,MAAM,2CAAgDA,eAAA,CAAA,eAAA;AAAA,EAC3D,kCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GACrE;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACrF;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC1F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACvE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,oBAAA,EAAsB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC7F;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC1F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACtE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,UAAY,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,iBAAiB,CAAA,EAAG,UAAU,IAAK,EAAA;AAAA,GACpG;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,gBAAgB,CAAE,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,mBAAmB,CAAE,EAAA;AAAA,IAClF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,SAAA,EAAW,GAAG,cAAe,EAAA;AAAA,GAC/D;AACF,EAAA;AAOO,MAAM,sCAA2CA,eAAA,CAAA,QAAA;AAAA,EACtD,6BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,EAAA;AAAA,IACtB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,GAC3B;AACF,EAAA;AAQO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,4CAAiDA,eAAA,CAAA,eAAA;AAAA,EAC5D,mCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IAC1H;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,4BAAiCA,eAAA,CAAA,eAAA;AAAA,EAC5C,mBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC5E;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,SAAW,EAAA,CAAA,EAAG,WAAa,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,IAAK,EAAA;AAAA,GAClD;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,iBAAiB,CAAE,EAAA;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,0CAA+CA,eAAA,CAAA,eAAA;AAAA,EAC1D,iCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,WAAW,CAAE,EAAA;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACpF;AACF,EAAA;AAKO,MAAM,0CAA+CA,eAAA,CAAA,eAAA;AAAA,EAC1D,iCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,sBAAA,EAAwB,MAAM,SAAW,EAAA,CAAA,EAAG,iBAAmB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC7F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,mBAAA,EAAqB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC1F;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,YAAA,EAAc,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC1F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,yCAA8CA,eAAA,CAAA,eAAA;AAAA,EACzD,gCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,mBAAA,EAAqB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC1F;AACF,EAAA;AAKO,MAAM,+CAAoDA,eAAA,CAAA,eAAA;AAAA,EAC/D,sCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,IAAK,EAAA;AAAA,IAChD;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,IAClE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,oBAAA,EAAsB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC3F;AACF,EAAA;AAKO,MAAM,4BAAiCA,eAAA,CAAA,eAAA;AAAA,EAC5C,mBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,UAAU,IAAM,EAAA,SAAA,EAAW,GAAG,kBAAmB,EAAA;AAAA,IAChE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,IAAM,EAAA,SAAA,EAAW,GAAG,kBAAmB,EAAA;AAAA,IACtE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,gBAAA,EAAkB,MAAM,SAAW,EAAA,CAAA,EAAG,sBAAwB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC5F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,SAAW,EAAA,CAAA,EAAG,eAAiB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACpF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,kBAAmB,EAAA;AAAA,IAC/D,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,qBAAA,EAAuB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACnG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,4BAAA,EAA8B,MAAM,SAAW,EAAA,CAAA,EAAG,uBAAyB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC3G;AACF,EAAA;AAKO,MAAM,0CAA+CA,eAAA,CAAA,eAAA;AAAA,EAC1D,iCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACnE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,eAAA;AAAA,EACnD,0BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,gBAAA,EAAkB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA0B,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA,IAChG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA,IAC7F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA,IAC1F,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,cAAA,EAAgB,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA,IAC7F,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,2BAA6B,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,iBAAiB,CAAA,EAAG,OAAO,UAAW,EAAA;AAAA,IACtH,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,sBAAA,EAAwB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA0B,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA,IACtG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,6BAAA,EAA+B,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA,IAC5G,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,yCAAA,EAA2C,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA,IACxH,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,8BAAA,EAAgC,MAAM,QAAU,EAAA,CAAA,EAAG,CAAyB,EAAA,KAAA,EAAO,UAAW,EAAA;AAAA,GAC/G;AACF,EAAA;AAKO,MAAM,uBAA4BA,eAAA,CAAA,eAAA;AAAA,EACvC,cAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GACpE;AACF,EAAA;AAKO,MAAM,uBAA4BA,eAAA,CAAA,eAAA;AAAA,EACvC,cAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAClF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GAC1E;AACF,EAAA;AAKO,MAAM,iCAAsCA,eAAA,CAAA,eAAA;AAAA,EACjD,wBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,UAAY,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,eAAA;AAAA,EAC7C,oBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,KAAA,EAAO,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,iBAAiB,CAAE,EAAA;AAAA,GAC/E;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IAC3E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,sBAAsB,CAAE,EAAA;AAAA,IACrF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,yCAA8CA,eAAA,CAAA,QAAA;AAAA,EACzD,gCAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,IAAI,EAAA;AAAA,IAClB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,IACzB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,aAAa,EAAA;AAAA,IAC3B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,GAChC;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC3E;AACF;;AC7nBO,MAAM,gCAAqCA,eAAA,CAAA,QAAA;AAAA,EAChD,uBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IAClC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,IACnC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IACpC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,8BAA8B,EAAA;AAAA,IAC9C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,IACnC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,6BAA6B,EAAA;AAAA,IAC7C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC/B,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,qBAAqB,EAAA;AAAA,IACrC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,8BAA8B,EAAA;AAAA,IAC9C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,8BAA8B,EAAA;AAAA,IAC9C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACtC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,wBAAwB,EAAA;AAAA,IACxC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,yBAAyB,EAAA;AAAA,IACzC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,6BAA6B,EAAA;AAAA,IAC7C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACtC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,qBAAqB,EAAA;AAAA,IACrC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,+BAA+B,EAAA;AAAA,IAC/C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,2BAA2B,EAAA;AAAA,IAC3C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,gCAAgC,EAAA;AAAA,IAChD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,4BAA4B,EAAA;AAAA,IAC5C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,qBAAqB,EAAA;AAAA,IACrC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,IACjC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,qCAAqC,EAAA;AAAA,IACrD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,iCAAiC,EAAA;AAAA,IACjD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,mCAAmC,EAAA;AAAA,IACnD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,4CAA4C,EAAA;AAAA,IAC5D,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,0BAA0B,EAAA;AAAA,IAC1C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,+BAA+B,EAAA;AAAA,IAC/C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,+BAA+B,EAAA;AAAA,IAC/C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,oCAAoC,EAAA;AAAA,IACpD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,6CAA6C,EAAA;AAAA,IAC7D,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,0BAA0B,EAAA;AAAA,IAC1C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,0BAA0B,EAAA;AAAA,IAC1C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,+BAA+B,EAAA;AAAA,IAC/C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACtC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACtC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,+BAA+B,EAAA;AAAA,IAC/C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,gCAAgC,EAAA;AAAA,IAChD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,kCAAkC,EAAA;AAAA,IAClD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,4BAA4B,EAAA;AAAA,IAC5C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,wBAAwB,EAAA;AAAA,IACxC,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,gCAAgC,EAAA;AAAA,IAChD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,4BAA4B,EAAA;AAAA,IAC5C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,kCAAkC,EAAA;AAAA,IAClD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,8BAA8B,EAAA;AAAA,IAC9C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,mCAAmC,EAAA;AAAA,IACnD,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,2BAA2B,EAAA;AAAA,IAC3C,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,2CAA2C,EAAA;AAAA,IAC3D,EAAC,EAAA,EAAI,GAAK,EAAA,IAAA,EAAM,kCAAkC,EAAA;AAAA,GACpD;AACF,EAAA;AAKO,MAAM,+BAAoCA,eAAA,CAAA,QAAA;AAAA,EAC/C,sBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,oBAAoB,EAAA;AAAA,IAClC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,IACjC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,IACjC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,GACnC;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,QAAA;AAAA,EACnD,0BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,IAC9B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC7B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,iBAAiB,EAAA;AAAA,GACjC;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,QAAA;AAAA,EACrD,4BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,2BAA2B,EAAA;AAAA,IACzC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,yBAAyB,EAAA;AAAA,IACvC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,2BAA2B,EAAA;AAAA,GAC3C;AACF,EAAA;AAKO,MAAM,gCAAqCA,eAAA,CAAA,QAAA;AAAA,EAChD,uBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,EAAA;AAAA,IACjC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,EAAA;AAAA,IACtC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,YAAY,EAAA;AAAA,IAC1B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,kBAAkB,EAAA;AAAA,IAChC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAW,EAAA;AAAA,GAC3B;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,QAAA;AAAA,EACpD,2BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,sBAAsB,EAAA;AAAA,IACpC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,qBAAqB,EAAA;AAAA,IACnC,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,yBAAyB,EAAA;AAAA,GACzC;AACF,EAAA;AAKO,MAAM,6BAAkCA,eAAA,CAAA,QAAA;AAAA,EAC7C,oBAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,MAAM,EAAA;AAAA,IACpB,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,GAC/B;AACF,EAAA;AAKO,MAAM,mCAAwCA,eAAA,CAAA,QAAA;AAAA,EACnD,0BAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,aAAa,EAAA;AAAA,IAC3B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,aAAa,EAAA;AAAA,IAC3B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,GAC9B;AACF,EAAA;AAOO,MAAM,4BAAiCA,eAAA,CAAA,eAAA;AAAA,EAC5C,mBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,aAAa,CAAE,EAAA;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACxE;AACF,EAAA;AAMO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,mBAAA,EAAqB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjG;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,uBAAA,EAAyB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACrG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC/F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC3E;AACF,EAAA;AAMO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,MAAA,EAAQ,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,sBAAsB,CAAE,EAAA;AAAA,IACpF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,mBAAA,EAAqB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjG;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,iBAAA;AAAA,MAAmB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC/E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,WAAA,EAAa,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,uBAAA,EAAyB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACrG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAChG;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC3E;AACF,EAAA;AAKO,MAAM,yCAA8CA,eAAA,CAAA,QAAA;AAAA,EACzD,gCAAA;AAAA,EACA;AAAA,IACE,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,cAAc,EAAA;AAAA,IAC5B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,eAAe,EAAA;AAAA,IAC7B,EAAC,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,gBAAgB,EAAA;AAAA,GAChC;AACF,EAAA;AAKO,MAAM,+CAAoDA,eAAA,CAAA,eAAA;AAAA,EAC/D,sCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,GAClE;AACF,EAAA;AAKO,MAAM,+CAAoDA,eAAA,CAAA,eAAA;AAAA,EAC/D,sCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACvF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,mBAAA,EAAqB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC/F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACvH,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,yBAAyB,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACtI,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,yBAAyB,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACtI,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,gBAAgB,CAAE,EAAA;AAAA,IACzF,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,mBAAmB,IAAM,EAAA,SAAA,EAAW,GAAGE,iBAAS,EAAA;AAAA,IAChE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,qBAAqB,IAAM,EAAA,SAAA,EAAW,GAAGA,iBAAS,EAAA;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC5E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,kBAAA,EAAoB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAF,eAAA,CAAO,WAAY,CAAA,kBAAkB,CAAE,EAAA;AAAA,GAC9F;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACzD,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,qBAAqB,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACnE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACjE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACnF,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,kBAAoB,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,kBAAkB,CAAA,EAAG,KAAK,IAAK,EAAA;AAAA,GACxG;AACF,EAAA;AAKO,MAAM,gDAAqDA,eAAA,CAAA,eAAA;AAAA,EAChE,uCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,oBAAqB,EAAA;AAAA,GACnE;AACF,EAAA;AAKO,MAAM,gDAAqDA,eAAA,CAAA,eAAA;AAAA,EAChE,uCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,oBAAsB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACpF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,sBAAwB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,GACvF;AACF,EAAA;AAKO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC9E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACvF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACvH,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,yBAAyB,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACtI,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,yBAAyB,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACtI,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,gBAAgB,CAAE,EAAA;AAAA,IACzF,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,kBAAA,EAAoB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,kBAAkB,CAAE,EAAA;AAAA,GAC9F;AACF,EAAA;AAKO,MAAM,yCAA8CA,eAAA,CAAA,eAAA;AAAA,EACzD,gCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAClF,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,WAAa,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,YAAY,CAAA,EAAG,KAAK,IAAK,EAAA;AAAA,IACzF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,qBAAA,EAAuB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC9F,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACzD,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,eAAA,EAAiB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACnF,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,kBAAoB,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,kBAAkB,CAAA,EAAG,KAAK,IAAK,EAAA;AAAA,GACxG;AACF,EAAA;AAKO,MAAM,4CAAiDA,eAAA,CAAA,eAAA;AAAA,EAC5D,mCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC9E;AACF,EAAA;AAKO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,GAClE;AACF,EAAA;AAKO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC9E;AACF,EAAA;AAKO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,oBAAqB,EAAA;AAAA,GACnE;AACF,EAAA;AAMO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,GACxD;AACF,EAAA;AAMO,MAAM,uCAA4CA,eAAA,CAAA,eAAA;AAAA,EACvD,8BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,YAAc,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC3E;AACF,EAAA;AAOO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACtD,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACzF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACzF;AACF,EAAA;AAKO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAClF;AACF,EAAA;AAOO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACtD,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACzF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACzF;AACF,EAAA;AAKO,MAAM,+CAAoDA,eAAA,CAAA,eAAA;AAAA,EAC/D,sCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,oBAAsB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GACnF;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC9E;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACrE;AACF,EAAA;AAKO,MAAM,4CAAiDA,eAAA,CAAA,eAAA;AAAA,EAC5D,mCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACrE;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,KAAA;AAAA,MAAO,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACzE;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,sBAAA,EAAwB,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,MAAO,EAAA;AAAA,IAChG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,0BAAA,EAA4B,MAAM,SAAW,EAAA,CAAA,EAAG,yBAA2B,EAAA,KAAA,EAAO,MAAO,EAAA;AAAA,IACxG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,sBAAA,EAAwB,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,MAAO,EAAA;AAAA,GAClG;AACF,EAAA;AAKO,MAAM,+CAAoDA,eAAA,CAAA,eAAA;AAAA,EAC/D,sCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,iBAAiB,IAAM,EAAA,SAAA,EAAW,GAAG,mBAAoB,EAAA;AAAA,IACzE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,IAC3D,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACzF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC/F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IAC1H;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,iBAAkB,EAAA;AAAA,GACtE;AACF,EAAA;AAKO,MAAM,+CAAoDA,eAAA,CAAA,eAAA;AAAA,EAC/D,sCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,SAAA,EAAW,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,IACnF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,QAAA,EAAU,MAAM,SAAW,EAAA,CAAA,EAAG,qBAAuB,EAAA,KAAA,EAAO,QAAS,EAAA;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,sCAA2CA,eAAA,CAAA,eAAA;AAAA,EACtD,6BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,IAC3D,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACzF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IAC/F;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IAC1H;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,iBAAkB,EAAA;AAAA,IACrE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC5E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,kBAAA,EAAoB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,kBAAkB,CAAE,EAAA;AAAA,GAC9F;AACF,EAAA;AAKO,MAAM,wCAA6CA,eAAA,CAAA,eAAA;AAAA,EACxD,+BAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,aAAa,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IAC3D,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,IAC3D,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,MAAA,EAAQ,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IAC/E,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,UAAA,EAAY,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,GAAA,EAAK,IAAK,EAAA;AAAA,IACnF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,cAAc,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IAC1H,EAAE,EAAA,EAAI,CAAG,EAAA,IAAA,EAAM,kBAAoB,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,kBAAkB,CAAA,EAAG,KAAK,IAAK,EAAA;AAAA,GACxG;AACF,EAAA;AAOO,MAAM,6CAAkDA,eAAA,CAAA,eAAA;AAAA,EAC7D,oCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IACtD,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,mBAAA,EAAqB,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,IACjG,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,MAAM,QAAU,EAAA,CAAA,EAAG,CAA2B,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAC3F;AACF,EAAA;AAKO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,OAAA,EAAS,MAAM,SAAW,EAAA,CAAA,EAAG,mBAAqB,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,GAClF;AACF,EAAA;AAKO,MAAM,+CAAoDA,eAAA,CAAA,eAAA;AAAA,EAC/D,sCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACtF;AACF,EAAA;AAKO,MAAM,oCAAyCA,eAAA,CAAA,eAAA;AAAA,EACpD,2BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACnF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC7E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,yBAAyB,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACrI,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,yBAAyB,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,GACvI;AACF,EAAA;AAQO,MAAM,8CAAmDA,eAAA,CAAA,eAAA;AAAA,EAC9D,qCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,cAAA;AAAA,MAAgB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,iBAAkB,EAAA;AAAA,IAC/D;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAChF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,0BAA0B,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACtI;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,mBAAA;AAAA,MAAqB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAChF,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACxH,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,iBAAA,EAAmB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,gBAAgB,CAAE,EAAA;AAAA,IACzF,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,mBAAmB,IAAM,EAAA,SAAA,EAAW,GAAGE,iBAAS,EAAA;AAAA,IAChE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,qBAAqB,IAAM,EAAA,SAAA,EAAW,GAAGA,iBAAS,EAAA;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC5E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,kBAAA,EAAoB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAF,eAAA,CAAO,WAAY,CAAA,kBAAkB,CAAE,EAAA;AAAA,IAC5F;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,qBAAA;AAAA,MAAuB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,GACpF;AACF,EAAA;AAKO,MAAM,qCAA0CA,eAAA,CAAA,eAAA;AAAA,EACrD,4BAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,gBAAA;AAAA,MAAkB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC9E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GAC7E;AACF,EAAA;AAKO,MAAM,gDAAqDA,eAAA,CAAA,eAAA;AAAA,EAChE,uCAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAwB;AAAA,IAC3E,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,WAAW,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACvH,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,mBAAmB,IAAM,EAAA,SAAA,EAAW,GAAGE,iBAAS,EAAA;AAAA,GACjE;AACF,EAAA;AAKO,MAAM,8BAAmCF,eAAA,CAAA,eAAA;AAAA,EAC9C,qBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACxE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,QAAA;AAAA,MAAU,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,WAAA;AAAA,MAAa,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,sBAAA;AAAA,MAAwB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpF,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,0BAA0B,IAAM,EAAA,KAAA,EAAO,CAAG,EAAA,CAAA,EAA2B,CAAG,EAAA;AAAA,MAAC,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA2B,EAAA;AAAA,IACvI,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,YAAY,IAAM,EAAA,SAAA,EAAW,GAAG,MAAO,EAAA;AAAA,IACtD,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,UAAU,IAAM,EAAA,SAAA,EAAW,GAAG,MAAO,EAAA;AAAA,IACpD;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC1E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,UAAA;AAAA,MAAY,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACxE,EAAE,EAAA,EAAI,EAAI,EAAA,IAAA,EAAM,kBAAoB,EAAA,IAAA,EAAM,MAAQ,EAAA,CAAA,EAAGA,eAAO,CAAA,WAAA,CAAY,UAAU,CAAA,EAAG,UAAU,IAAK,EAAA;AAAA,IACpG,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,gBAAA,EAAkB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,gBAAgB,CAAE,EAAA;AAAA,IACxF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,aAAA,EAAe,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,aAAa,CAAE,EAAA;AAAA,IACjF;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,eAAA;AAAA,MAAiB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC7E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IAC3E,EAAE,EAAI,EAAA,EAAA,EAAI,IAAM,EAAA,mBAAA,EAAqB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,gBAAgB,CAAE,EAAA;AAAA,IAC3F;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACtE,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,oBAAoB,IAAM,EAAA,SAAA,EAAW,GAAG,SAAU,EAAA;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC5E;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,kBAAA;AAAA,MAAoB,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,GACnF;AACF,EAAA;AAKO,MAAM,kCAAuCA,eAAA,CAAA,eAAA;AAAA,EAClD,yBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,SAAA;AAAA,MAAW,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACvE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAC3E;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,0BAAA;AAAA,MAA4B,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,0BAAA;AAAA,MAA4B,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACvF,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,iBAAA,EAAmB,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,iBAAiB,CAAE,EAAA;AAAA,IACzF;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,wBAAwB,IAAM,EAAA,SAAA,EAAW,GAAG,SAAU,EAAA;AAAA,GACvE;AACF,EAAA;AAKO,MAAM,yBAA8BA,eAAA,CAAA,eAAA;AAAA,EACzC,gBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACpE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,MAAA;AAAA,MAAQ,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,EAAA;AAAA;AAAA,KAA2B;AAAA,IACrE,EAAE,EAAI,EAAA,CAAA,EAAG,IAAM,EAAA,WAAA,EAAa,IAAM,EAAA,MAAA,EAAQ,CAAG,EAAAA,eAAA,CAAO,WAAY,CAAA,YAAY,CAAE,EAAA;AAAA,GAChF;AACF;;ACtuBO,MAAM,+BAAoCA,eAAA,CAAA,eAAA;AAAA,EAC/C,sBAAA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,OAAA;AAAA,MAAS,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IACrE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,QAAQ,IAAM,EAAA,SAAA,EAAW,GAAG,IAAK,EAAA;AAAA,IAChD,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,eAAgB,EAAA;AAAA,IAClE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,eAAe,IAAM,EAAA,SAAA,EAAW,GAAG,UAAW,EAAA;AAAA,IAC7D,EAAE,IAAI,EAAI,EAAA,IAAA,EAAM,gBAAgB,IAAM,EAAA,SAAA,EAAW,GAAG,WAAY,EAAA;AAAA,IAChE,EAAE,IAAI,CAAG,EAAA,IAAA,EAAM,SAAS,IAAM,EAAA,SAAA,EAAW,GAAG,SAAU,EAAA;AAAA,IACtD;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,IAAA;AAAA,MAAM,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAA0B;AAAA,IAClE;AAAA,MAAE,EAAI,EAAA,CAAA;AAAA,MAAG,IAAM,EAAA,YAAA;AAAA,MAAc,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,IACzE;AAAA,MAAE,EAAI,EAAA,EAAA;AAAA,MAAI,IAAM,EAAA,aAAA;AAAA,MAAe,IAAM,EAAA,QAAA;AAAA,MAAU,CAAG,EAAA,CAAA;AAAA;AAAA,KAAyB;AAAA,GAC7E;AACF;;ACvCO,MAAM,OAAU,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}