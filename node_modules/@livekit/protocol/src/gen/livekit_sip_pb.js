// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_sip.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { Duration, proto3 } from "@bufbuild/protobuf";
import { DisconnectReason, ListUpdate, Pagination } from "./livekit_models_pb.js";
import { RoomConfiguration } from "./livekit_room_pb.js";

/**
 * @generated from enum livekit.SIPStatusCode
 */
export const SIPStatusCode = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPStatusCode",
  [
    {no: 0, name: "SIP_STATUS_UNKNOWN"},
    {no: 100, name: "SIP_STATUS_TRYING"},
    {no: 180, name: "SIP_STATUS_RINGING"},
    {no: 181, name: "SIP_STATUS_CALL_IS_FORWARDED"},
    {no: 182, name: "SIP_STATUS_QUEUED"},
    {no: 183, name: "SIP_STATUS_SESSION_PROGRESS"},
    {no: 200, name: "SIP_STATUS_OK"},
    {no: 202, name: "SIP_STATUS_ACCEPTED"},
    {no: 301, name: "SIP_STATUS_MOVED_PERMANENTLY"},
    {no: 302, name: "SIP_STATUS_MOVED_TEMPORARILY"},
    {no: 305, name: "SIP_STATUS_USE_PROXY"},
    {no: 400, name: "SIP_STATUS_BAD_REQUEST"},
    {no: 401, name: "SIP_STATUS_UNAUTHORIZED"},
    {no: 402, name: "SIP_STATUS_PAYMENT_REQUIRED"},
    {no: 403, name: "SIP_STATUS_FORBIDDEN"},
    {no: 404, name: "SIP_STATUS_NOTFOUND"},
    {no: 405, name: "SIP_STATUS_METHOD_NOT_ALLOWED"},
    {no: 406, name: "SIP_STATUS_NOT_ACCEPTABLE"},
    {no: 407, name: "SIP_STATUS_PROXY_AUTH_REQUIRED"},
    {no: 408, name: "SIP_STATUS_REQUEST_TIMEOUT"},
    {no: 409, name: "SIP_STATUS_CONFLICT"},
    {no: 410, name: "SIP_STATUS_GONE"},
    {no: 413, name: "SIP_STATUS_REQUEST_ENTITY_TOO_LARGE"},
    {no: 414, name: "SIP_STATUS_REQUEST_URI_TOO_LONG"},
    {no: 415, name: "SIP_STATUS_UNSUPPORTED_MEDIA_TYPE"},
    {no: 416, name: "SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE"},
    {no: 420, name: "SIP_STATUS_BAD_EXTENSION"},
    {no: 421, name: "SIP_STATUS_EXTENSION_REQUIRED"},
    {no: 423, name: "SIP_STATUS_INTERVAL_TOO_BRIEF"},
    {no: 480, name: "SIP_STATUS_TEMPORARILY_UNAVAILABLE"},
    {no: 481, name: "SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS"},
    {no: 482, name: "SIP_STATUS_LOOP_DETECTED"},
    {no: 483, name: "SIP_STATUS_TOO_MANY_HOPS"},
    {no: 484, name: "SIP_STATUS_ADDRESS_INCOMPLETE"},
    {no: 485, name: "SIP_STATUS_AMBIGUOUS"},
    {no: 486, name: "SIP_STATUS_BUSY_HERE"},
    {no: 487, name: "SIP_STATUS_REQUEST_TERMINATED"},
    {no: 488, name: "SIP_STATUS_NOT_ACCEPTABLE_HERE"},
    {no: 500, name: "SIP_STATUS_INTERNAL_SERVER_ERROR"},
    {no: 501, name: "SIP_STATUS_NOT_IMPLEMENTED"},
    {no: 502, name: "SIP_STATUS_BAD_GATEWAY"},
    {no: 503, name: "SIP_STATUS_SERVICE_UNAVAILABLE"},
    {no: 504, name: "SIP_STATUS_GATEWAY_TIMEOUT"},
    {no: 505, name: "SIP_STATUS_VERSION_NOT_SUPPORTED"},
    {no: 513, name: "SIP_STATUS_MESSAGE_TOO_LARGE"},
    {no: 600, name: "SIP_STATUS_GLOBAL_BUSY_EVERYWHERE"},
    {no: 603, name: "SIP_STATUS_GLOBAL_DECLINE"},
    {no: 604, name: "SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE"},
    {no: 606, name: "SIP_STATUS_GLOBAL_NOT_ACCEPTABLE"},
  ],
);

/**
 * @generated from enum livekit.SIPTransport
 */
export const SIPTransport = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPTransport",
  [
    {no: 0, name: "SIP_TRANSPORT_AUTO"},
    {no: 1, name: "SIP_TRANSPORT_UDP"},
    {no: 2, name: "SIP_TRANSPORT_TCP"},
    {no: 3, name: "SIP_TRANSPORT_TLS"},
  ],
);

/**
 * @generated from enum livekit.SIPHeaderOptions
 */
export const SIPHeaderOptions = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPHeaderOptions",
  [
    {no: 0, name: "SIP_NO_HEADERS"},
    {no: 1, name: "SIP_X_HEADERS"},
    {no: 2, name: "SIP_ALL_HEADERS"},
  ],
);

/**
 * @generated from enum livekit.SIPMediaEncryption
 */
export const SIPMediaEncryption = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPMediaEncryption",
  [
    {no: 0, name: "SIP_MEDIA_ENCRYPT_DISABLE"},
    {no: 1, name: "SIP_MEDIA_ENCRYPT_ALLOW"},
    {no: 2, name: "SIP_MEDIA_ENCRYPT_REQUIRE"},
  ],
);

/**
 * @generated from enum livekit.SIPCallStatus
 */
export const SIPCallStatus = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPCallStatus",
  [
    {no: 0, name: "SCS_CALL_INCOMING"},
    {no: 1, name: "SCS_PARTICIPANT_JOINED"},
    {no: 2, name: "SCS_ACTIVE"},
    {no: 3, name: "SCS_DISCONNECTED"},
    {no: 4, name: "SCS_ERROR"},
  ],
);

/**
 * @generated from enum livekit.SIPTransferStatus
 */
export const SIPTransferStatus = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPTransferStatus",
  [
    {no: 0, name: "STS_TRANSFER_ONGOING"},
    {no: 1, name: "STS_TRANSFER_FAILED"},
    {no: 2, name: "STS_TRANSFER_SUCCESSFUL"},
  ],
);

/**
 * @generated from enum livekit.SIPFeature
 */
export const SIPFeature = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPFeature",
  [
    {no: 0, name: "NONE"},
    {no: 1, name: "KRISP_ENABLED"},
  ],
);

/**
 * @generated from enum livekit.SIPCallDirection
 */
export const SIPCallDirection = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPCallDirection",
  [
    {no: 0, name: "SCD_UNKNOWN"},
    {no: 1, name: "SCD_INBOUND"},
    {no: 2, name: "SCD_OUTBOUND"},
  ],
);

/**
 * SIPStatus is returned as an error detail in CreateSIPParticipant.
 *
 * @generated from message livekit.SIPStatus
 */
export const SIPStatus = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPStatus",
  () => [
    { no: 1, name: "code", kind: "enum", T: proto3.getEnumType(SIPStatusCode) },
    { no: 2, name: "status", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.CreateSIPTrunkRequest
 * @deprecated
 */
export const CreateSIPTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateSIPTrunkRequest",
  () => [
    { no: 1, name: "inbound_addresses", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "outbound_address", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "outbound_number", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "inbound_numbers_regex", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 9, name: "inbound_numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 5, name: "inbound_username", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "inbound_password", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "outbound_username", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "outbound_password", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.SIPTrunkInfo
 * @deprecated
 */
export const SIPTrunkInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPTrunkInfo",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 14, name: "kind", kind: "enum", T: proto3.getEnumType(SIPTrunkInfo_TrunkKind) },
    { no: 2, name: "inbound_addresses", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 3, name: "outbound_address", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "outbound_number", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 13, name: "transport", kind: "enum", T: proto3.getEnumType(SIPTransport) },
    { no: 5, name: "inbound_numbers_regex", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 10, name: "inbound_numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 6, name: "inbound_username", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "inbound_password", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "outbound_username", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "outbound_password", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 12, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from enum livekit.SIPTrunkInfo.TrunkKind
 */
export const SIPTrunkInfo_TrunkKind = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SIPTrunkInfo.TrunkKind",
  [
    {no: 0, name: "TRUNK_LEGACY"},
    {no: 1, name: "TRUNK_INBOUND"},
    {no: 2, name: "TRUNK_OUTBOUND"},
  ],
);

/**
 * @generated from message livekit.CreateSIPInboundTrunkRequest
 */
export const CreateSIPInboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateSIPInboundTrunkRequest",
  () => [
    { no: 1, name: "trunk", kind: "message", T: SIPInboundTrunkInfo },
  ],
);

/**
 * @generated from message livekit.UpdateSIPInboundTrunkRequest
 */
export const UpdateSIPInboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateSIPInboundTrunkRequest",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "replace", kind: "message", T: SIPInboundTrunkInfo, oneof: "action" },
    { no: 3, name: "update", kind: "message", T: SIPInboundTrunkUpdate, oneof: "action" },
  ],
);

/**
 * @generated from message livekit.SIPInboundTrunkInfo
 */
export const SIPInboundTrunkInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPInboundTrunkInfo",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 5, name: "allowed_addresses", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 6, name: "allowed_numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 7, name: "auth_username", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "auth_password", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "headers", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 10, name: "headers_to_attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 14, name: "attributes_to_headers", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 15, name: "include_headers", kind: "enum", T: proto3.getEnumType(SIPHeaderOptions) },
    { no: 11, name: "ringing_timeout", kind: "message", T: Duration },
    { no: 12, name: "max_call_duration", kind: "message", T: Duration },
    { no: 13, name: "krisp_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 16, name: "media_encryption", kind: "enum", T: proto3.getEnumType(SIPMediaEncryption) },
  ],
);

/**
 * @generated from message livekit.SIPInboundTrunkUpdate
 */
export const SIPInboundTrunkUpdate = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPInboundTrunkUpdate",
  () => [
    { no: 1, name: "numbers", kind: "message", T: ListUpdate },
    { no: 2, name: "allowed_addresses", kind: "message", T: ListUpdate },
    { no: 3, name: "allowed_numbers", kind: "message", T: ListUpdate },
    { no: 4, name: "auth_username", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "auth_password", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 6, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 7, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 8, name: "media_encryption", kind: "enum", T: proto3.getEnumType(SIPMediaEncryption), opt: true },
  ],
);

/**
 * @generated from message livekit.CreateSIPOutboundTrunkRequest
 */
export const CreateSIPOutboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateSIPOutboundTrunkRequest",
  () => [
    { no: 1, name: "trunk", kind: "message", T: SIPOutboundTrunkInfo },
  ],
);

/**
 * @generated from message livekit.UpdateSIPOutboundTrunkRequest
 */
export const UpdateSIPOutboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateSIPOutboundTrunkRequest",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "replace", kind: "message", T: SIPOutboundTrunkInfo, oneof: "action" },
    { no: 3, name: "update", kind: "message", T: SIPOutboundTrunkUpdate, oneof: "action" },
  ],
);

/**
 * @generated from message livekit.SIPOutboundTrunkInfo
 */
export const SIPOutboundTrunkInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPOutboundTrunkInfo",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "address", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 14, name: "destination_country", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "transport", kind: "enum", T: proto3.getEnumType(SIPTransport) },
    { no: 6, name: "numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 7, name: "auth_username", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "auth_password", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "headers", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 10, name: "headers_to_attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 11, name: "attributes_to_headers", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 12, name: "include_headers", kind: "enum", T: proto3.getEnumType(SIPHeaderOptions) },
    { no: 13, name: "media_encryption", kind: "enum", T: proto3.getEnumType(SIPMediaEncryption) },
  ],
);

/**
 * @generated from message livekit.SIPOutboundTrunkUpdate
 */
export const SIPOutboundTrunkUpdate = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPOutboundTrunkUpdate",
  () => [
    { no: 1, name: "address", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 2, name: "transport", kind: "enum", T: proto3.getEnumType(SIPTransport), opt: true },
    { no: 9, name: "destination_country", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 3, name: "numbers", kind: "message", T: ListUpdate },
    { no: 4, name: "auth_username", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "auth_password", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 6, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 7, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 8, name: "media_encryption", kind: "enum", T: proto3.getEnumType(SIPMediaEncryption), opt: true },
  ],
);

/**
 * @generated from message livekit.GetSIPInboundTrunkRequest
 */
export const GetSIPInboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.GetSIPInboundTrunkRequest",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.GetSIPInboundTrunkResponse
 */
export const GetSIPInboundTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.GetSIPInboundTrunkResponse",
  () => [
    { no: 1, name: "trunk", kind: "message", T: SIPInboundTrunkInfo },
  ],
);

/**
 * @generated from message livekit.GetSIPOutboundTrunkRequest
 */
export const GetSIPOutboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.GetSIPOutboundTrunkRequest",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.GetSIPOutboundTrunkResponse
 */
export const GetSIPOutboundTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.GetSIPOutboundTrunkResponse",
  () => [
    { no: 1, name: "trunk", kind: "message", T: SIPOutboundTrunkInfo },
  ],
);

/**
 * @generated from message livekit.ListSIPTrunkRequest
 * @deprecated
 */
export const ListSIPTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListSIPTrunkRequest",
  () => [
    { no: 1, name: "page", kind: "message", T: Pagination },
  ],
);

/**
 * @generated from message livekit.ListSIPTrunkResponse
 * @deprecated
 */
export const ListSIPTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListSIPTrunkResponse",
  () => [
    { no: 1, name: "items", kind: "message", T: SIPTrunkInfo, repeated: true },
  ],
);

/**
 * ListSIPInboundTrunkRequest lists inbound trunks for given filters. If no filters are set, all trunks are listed.
 *
 * @generated from message livekit.ListSIPInboundTrunkRequest
 */
export const ListSIPInboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListSIPInboundTrunkRequest",
  () => [
    { no: 3, name: "page", kind: "message", T: Pagination },
    { no: 1, name: "trunk_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ],
);

/**
 * @generated from message livekit.ListSIPInboundTrunkResponse
 */
export const ListSIPInboundTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListSIPInboundTrunkResponse",
  () => [
    { no: 1, name: "items", kind: "message", T: SIPInboundTrunkInfo, repeated: true },
  ],
);

/**
 * ListSIPOutboundTrunkRequest lists outbound trunks for given filters. If no filters are set, all trunks are listed.
 *
 * @generated from message livekit.ListSIPOutboundTrunkRequest
 */
export const ListSIPOutboundTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListSIPOutboundTrunkRequest",
  () => [
    { no: 3, name: "page", kind: "message", T: Pagination },
    { no: 1, name: "trunk_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ],
);

/**
 * @generated from message livekit.ListSIPOutboundTrunkResponse
 */
export const ListSIPOutboundTrunkResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListSIPOutboundTrunkResponse",
  () => [
    { no: 1, name: "items", kind: "message", T: SIPOutboundTrunkInfo, repeated: true },
  ],
);

/**
 * @generated from message livekit.DeleteSIPTrunkRequest
 */
export const DeleteSIPTrunkRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeleteSIPTrunkRequest",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.SIPDispatchRuleDirect
 */
export const SIPDispatchRuleDirect = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPDispatchRuleDirect",
  () => [
    { no: 1, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "pin", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.SIPDispatchRuleIndividual
 */
export const SIPDispatchRuleIndividual = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPDispatchRuleIndividual",
  () => [
    { no: 1, name: "room_prefix", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "pin", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.SIPDispatchRuleCallee
 */
export const SIPDispatchRuleCallee = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPDispatchRuleCallee",
  () => [
    { no: 1, name: "room_prefix", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "pin", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "randomize", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.SIPDispatchRule
 */
export const SIPDispatchRule = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPDispatchRule",
  () => [
    { no: 1, name: "dispatch_rule_direct", kind: "message", T: SIPDispatchRuleDirect, oneof: "rule" },
    { no: 2, name: "dispatch_rule_individual", kind: "message", T: SIPDispatchRuleIndividual, oneof: "rule" },
    { no: 3, name: "dispatch_rule_callee", kind: "message", T: SIPDispatchRuleCallee, oneof: "rule" },
  ],
);

/**
 * @generated from message livekit.CreateSIPDispatchRuleRequest
 */
export const CreateSIPDispatchRuleRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateSIPDispatchRuleRequest",
  () => [
    { no: 10, name: "dispatch_rule", kind: "message", T: SIPDispatchRuleInfo },
    { no: 1, name: "rule", kind: "message", T: SIPDispatchRule },
    { no: 2, name: "trunk_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 3, name: "hide_phone_number", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "inbound_numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 8, name: "room_preset", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "room_config", kind: "message", T: RoomConfiguration },
  ],
);

/**
 * @generated from message livekit.UpdateSIPDispatchRuleRequest
 */
export const UpdateSIPDispatchRuleRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateSIPDispatchRuleRequest",
  () => [
    { no: 1, name: "sip_dispatch_rule_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "replace", kind: "message", T: SIPDispatchRuleInfo, oneof: "action" },
    { no: 3, name: "update", kind: "message", T: SIPDispatchRuleUpdate, oneof: "action" },
  ],
);

/**
 * @generated from message livekit.SIPDispatchRuleInfo
 */
export const SIPDispatchRuleInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPDispatchRuleInfo",
  () => [
    { no: 1, name: "sip_dispatch_rule_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "rule", kind: "message", T: SIPDispatchRule },
    { no: 3, name: "trunk_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "hide_phone_number", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 7, name: "inbound_numbers", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 5, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 9, name: "room_preset", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "room_config", kind: "message", T: RoomConfiguration },
    { no: 11, name: "krisp_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 12, name: "media_encryption", kind: "enum", T: proto3.getEnumType(SIPMediaEncryption) },
  ],
);

/**
 * @generated from message livekit.SIPDispatchRuleUpdate
 */
export const SIPDispatchRuleUpdate = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPDispatchRuleUpdate",
  () => [
    { no: 1, name: "trunk_ids", kind: "message", T: ListUpdate },
    { no: 2, name: "rule", kind: "message", T: SIPDispatchRule },
    { no: 3, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 4, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 6, name: "media_encryption", kind: "enum", T: proto3.getEnumType(SIPMediaEncryption), opt: true },
  ],
);

/**
 * ListSIPDispatchRuleRequest lists dispatch rules for given filters. If no filters are set, all rules are listed.
 *
 * @generated from message livekit.ListSIPDispatchRuleRequest
 */
export const ListSIPDispatchRuleRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListSIPDispatchRuleRequest",
  () => [
    { no: 3, name: "page", kind: "message", T: Pagination },
    { no: 1, name: "dispatch_rule_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "trunk_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ],
);

/**
 * @generated from message livekit.ListSIPDispatchRuleResponse
 */
export const ListSIPDispatchRuleResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListSIPDispatchRuleResponse",
  () => [
    { no: 1, name: "items", kind: "message", T: SIPDispatchRuleInfo, repeated: true },
  ],
);

/**
 * @generated from message livekit.DeleteSIPDispatchRuleRequest
 */
export const DeleteSIPDispatchRuleRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeleteSIPDispatchRuleRequest",
  () => [
    { no: 1, name: "sip_dispatch_rule_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.SIPOutboundConfig
 */
export const SIPOutboundConfig = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPOutboundConfig",
  () => [
    { no: 1, name: "hostname", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "destination_country", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "transport", kind: "enum", T: proto3.getEnumType(SIPTransport) },
    { no: 3, name: "auth_username", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "auth_password", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "headers_to_attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 6, name: "attributes_to_headers", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
  ],
);

/**
 * A SIP Participant is a singular SIP session connected to a LiveKit room via
 * a SIP Trunk into a SIP DispatchRule
 *
 * @generated from message livekit.CreateSIPParticipantRequest
 */
export const CreateSIPParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateSIPParticipantRequest",
  () => [
    { no: 1, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 20, name: "trunk", kind: "message", T: SIPOutboundConfig },
    { no: 2, name: "sip_call_to", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 15, name: "sip_number", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "participant_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "participant_metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "participant_attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 5, name: "dtmf", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "play_ringtone", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 13, name: "play_dialtone", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 10, name: "hide_phone_number", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 16, name: "headers", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 17, name: "include_headers", kind: "enum", T: proto3.getEnumType(SIPHeaderOptions) },
    { no: 11, name: "ringing_timeout", kind: "message", T: Duration },
    { no: 12, name: "max_call_duration", kind: "message", T: Duration },
    { no: 14, name: "krisp_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 18, name: "media_encryption", kind: "enum", T: proto3.getEnumType(SIPMediaEncryption) },
    { no: 19, name: "wait_until_answered", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.SIPParticipantInfo
 */
export const SIPParticipantInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPParticipantInfo",
  () => [
    { no: 1, name: "participant_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "sip_call_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.TransferSIPParticipantRequest
 */
export const TransferSIPParticipantRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.TransferSIPParticipantRequest",
  () => [
    { no: 1, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "transfer_to", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "play_dialtone", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 5, name: "headers", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 6, name: "ringing_timeout", kind: "message", T: Duration },
  ],
);

/**
 * @generated from message livekit.SIPCallInfo
 */
export const SIPCallInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPCallInfo",
  () => [
    { no: 1, name: "call_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 16, name: "dispatch_rule_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 17, name: "region", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "room_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 18, name: "participant_attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 6, name: "from_uri", kind: "message", T: SIPUri },
    { no: 7, name: "to_uri", kind: "message", T: SIPUri },
    { no: 9, name: "created_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 10, name: "started_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 11, name: "ended_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 14, name: "enabled_features", kind: "enum", T: proto3.getEnumType(SIPFeature), repeated: true },
    { no: 15, name: "call_direction", kind: "enum", T: proto3.getEnumType(SIPCallDirection) },
    { no: 8, name: "call_status", kind: "enum", T: proto3.getEnumType(SIPCallStatus) },
    { no: 22, name: "created_at_ns", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 23, name: "started_at_ns", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 24, name: "ended_at_ns", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 12, name: "disconnect_reason", kind: "enum", T: proto3.getEnumType(DisconnectReason) },
    { no: 13, name: "error", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 19, name: "call_status_code", kind: "message", T: SIPStatus },
    { no: 20, name: "audio_codec", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 21, name: "media_encryption", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.SIPTransferInfo
 */
export const SIPTransferInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPTransferInfo",
  () => [
    { no: 1, name: "transfer_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "call_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "transfer_to", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "transfer_initiated_at_ns", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 5, name: "transfer_completed_at_ns", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 6, name: "transfer_status", kind: "enum", T: proto3.getEnumType(SIPTransferStatus) },
    { no: 7, name: "error", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "transfer_status_code", kind: "message", T: SIPStatus },
  ],
);

/**
 * @generated from message livekit.SIPUri
 */
export const SIPUri = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SIPUri",
  () => [
    { no: 1, name: "user", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "host", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "ip", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "port", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "transport", kind: "enum", T: proto3.getEnumType(SIPTransport) },
  ],
);

