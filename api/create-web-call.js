// Vercel Serverless Function
// Deploy this to Vercel and it will handle the backend automatically

import Retell from 'retell-sdk';

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // <PERSON>le preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { agent_id } = req.body;

    if (!agent_id) {
      return res.status(400).json({ error: 'agent_id is required' });
    }

    // Initialize Retell client with API key from environment variables
    const retellClient = new Retell({
      apiKey: process.env.RETELL_API_KEY,
    });

    // Create web call
    const webCallResponse = await retellClient.call.createWebCall({
      agent_id,
    });

    return res.json({
      access_token: webCallResponse.access_token,
      call_id: webCallResponse.call_id,
      agent_id: webCallResponse.agent_id,
      call_status: webCallResponse.call_status
    });

  } catch (error) {
    console.error('Error creating web call:', error);
    
    if (error.status === 401) {
      return res.status(401).json({ error: 'Invalid Retell API key' });
    } else if (error.status === 404) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    return res.status(500).json({
      error: 'Failed to create web call',
      message: error.message
    });
  }
}
