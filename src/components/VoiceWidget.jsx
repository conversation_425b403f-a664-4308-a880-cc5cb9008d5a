import { useState, useRef, useEffect } from 'react'
import { RetellWebClient } from 'retell-client-js-sdk'
import './VoiceWidget.css'

const VoiceWidget = ({
  agentId = '',
  onCallStart = () => {},
  onCallEnd = () => {},
  onTranscriptUpdate = () => {},
  onError = () => {}
}) => {
  const [isCallActive, setIsCallActive] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [error, setError] = useState('')
  const [callStatus, setCallStatus] = useState('idle') // idle, connecting, active, ended

  const retellWebClientRef = useRef(null)

  useEffect(() => {
    // Initialize Retell Web Client
    retellWebClientRef.current = new RetellWebClient()

    // Set up event listeners
    const retellWebClient = retellWebClientRef.current

    retellWebClient.on('call_started', () => {
      console.log('Call started')
      setIsCallActive(true)
      setIsConnecting(false)
      setCallStatus('active')
      setError('')
      onCallStart()
    })

    retellWebClient.on('call_ended', () => {
      console.log('Call ended')
      setIsCallActive(false)
      setIsConnecting(false)
      setCallStatus('ended')
      onCallEnd()
    })

    retellWebClient.on('agent_start_talking', () => {
      console.log('Agent started talking')
    })

    retellWebClient.on('agent_stop_talking', () => {
      console.log('Agent stopped talking')
    })

    retellWebClient.on('update', (update) => {
      console.log('Update received:', update)
      if (update.transcript) {
        setTranscript(update.transcript)
        onTranscriptUpdate(update.transcript)
      }
    })

    retellWebClient.on('metadata', (metadata) => {
      console.log('Metadata received:', metadata)
    })

    retellWebClient.on('error', (error) => {
      console.error('Retell error:', error)
      setError(`Call error: ${error.message || error}`)
      setIsCallActive(false)
      setIsConnecting(false)
      setCallStatus('idle')
      onError(error)
      retellWebClient.stopCall()
    })

    return () => {
      if (retellWebClient && isCallActive) {
        retellWebClient.stopCall()
      }
    }
  }, [])

  // Create web call and get access token
  const createWebCall = async () => {
    try {
      // Call backend server (adjust URL based on your setup)
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001'
      const response = await fetch(`${backendUrl}/api/create-web-call`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agent_id: agentId,
          metadata: {
            user_id: 'demo_user',
            session_id: Date.now().toString()
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('Web call created:', data)
      return data.access_token
    } catch (error) {
      console.error('Failed to create web call:', error)
      throw new Error(error.message || 'Failed to create web call. Please check your configuration.')
    }
  }

  const startCall = async () => {
    if (!agentId) {
      setError('Agent ID is required to start a call')
      return
    }

    setIsConnecting(true)
    setCallStatus('connecting')
    setError('')
    setTranscript('')

    try {
      const accessToken = await createWebCall()

      await retellWebClientRef.current.startCall({
        accessToken: accessToken,
        sampleRate: 24000,
        captureDeviceId: 'default',
        playbackDeviceId: 'default',
      })
    } catch (error) {
      console.error('Failed to start call:', error)
      setError(error.message || 'Failed to start call')
      setIsConnecting(false)
      setCallStatus('idle')
    }
  }

  const stopCall = () => {
    if (retellWebClientRef.current && isCallActive) {
      retellWebClientRef.current.stopCall()
    }
  }

  const clearConversation = () => {
    setTranscript('')
    setError('')
  }

  if (!agentId) {
    return (
      <div className="voice-widget error">
        <div className="error-message">
          <h3>Configuration Required</h3>
          <p>Please provide an Agent ID to use the voice widget.</p>
          <div className="config-info">
            <p>You need to:</p>
            <ul>
              <li>Set up a Retell AI agent</li>
              <li>Provide the agent ID as a prop</li>
              <li>Configure your backend to create web calls</li>
            </ul>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="voice-widget">
      <div className="widget-header">
        <h2>Retell AI Voice Assistant</h2>
        <button
          className="clear-btn"
          onClick={clearConversation}
          disabled={isConnecting || isCallActive}
        >
          Clear
        </button>
      </div>

      <div className="call-controls">
        <button
          className={`call-button ${callStatus}`}
          onClick={isCallActive ? stopCall : startCall}
          disabled={isConnecting}
        >
          <div className="call-icon">
            {isConnecting ? '⏳' : isCallActive ? '📞' : '🎙️'}
          </div>
          <div className="call-status">
            {isConnecting ? 'Connecting...' : isCallActive ? 'End Call' : 'Start Call'}
          </div>
        </button>
      </div>

      <div className="status-indicator">
        <div className={`status-dot ${callStatus}`}></div>
        <span className="status-text">
          {callStatus === 'idle' && 'Ready to start'}
          {callStatus === 'connecting' && 'Connecting...'}
          {callStatus === 'active' && 'Call in progress'}
          {callStatus === 'ended' && 'Call ended'}
        </span>
      </div>

      {transcript && (
        <div className="transcript-section">
          <h3>Conversation:</h3>
          <div className="transcript-content">
            <pre className="transcript">{transcript}</pre>
          </div>
        </div>
      )}

      {error && (
        <div className="error-section">
          <p className="error">{error}</p>
        </div>
      )}

      <div className="instructions">
        <p>Click "Start Call" to begin your conversation with the AI agent. The conversation will be transcribed in real-time.</p>
        {!agentId && (
          <p className="warning">⚠️ Agent ID not configured. Please provide an agent ID to start calls.</p>
        )}
      </div>
    </div>
  )
}

export default VoiceWidget
