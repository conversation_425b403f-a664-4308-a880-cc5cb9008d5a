import { useState, useRef, useEffect } from 'react'
import './VoiceWidget.css'

const VoiceWidget = () => {
  const [isListening, setIsListening] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [response, setResponse] = useState('')
  const [error, setError] = useState('')
  const [isSupported, setIsSupported] = useState(true)
  
  const recognitionRef = useRef(null)
  const synthRef = useRef(null)

  useEffect(() => {
    // Check if browser supports speech recognition
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      setIsSupported(false)
      setError('Speech recognition is not supported in this browser')
      return
    }

    // Initialize speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    recognitionRef.current = new SpeechRecognition()
    
    recognitionRef.current.continuous = false
    recognitionRef.current.interimResults = true
    recognitionRef.current.lang = 'en-US'

    // Initialize speech synthesis
    synthRef.current = window.speechSynthesis

    recognitionRef.current.onstart = () => {
      setIsListening(true)
      setError('')
    }

    recognitionRef.current.onresult = (event) => {
      let finalTranscript = ''
      let interimTranscript = ''

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript
        if (event.results[i].isFinal) {
          finalTranscript += transcript
        } else {
          interimTranscript += transcript
        }
      }

      setTranscript(finalTranscript || interimTranscript)
    }

    recognitionRef.current.onend = () => {
      setIsListening(false)
      if (transcript.trim()) {
        handleVoiceInput(transcript)
      }
    }

    recognitionRef.current.onerror = (event) => {
      setIsListening(false)
      setError(`Speech recognition error: ${event.error}`)
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [transcript])

  const startListening = () => {
    if (!isSupported || !recognitionRef.current) return
    
    setTranscript('')
    setResponse('')
    setError('')
    
    try {
      recognitionRef.current.start()
    } catch (err) {
      setError('Failed to start speech recognition')
    }
  }

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop()
    }
  }

  const handleVoiceInput = async (text) => {
    setIsProcessing(true)
    
    try {
      // This is where you'll integrate with your voice agent provider
      // For now, we'll simulate a response
      await simulateVoiceAgent(text)
    } catch (err) {
      setError('Failed to process voice input')
    } finally {
      setIsProcessing(false)
    }
  }

  const simulateVoiceAgent = async (input) => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Simulate response - replace this with your actual provider integration
    const mockResponse = `You said: "${input}". This is a simulated response from your voice agent.`
    setResponse(mockResponse)
    
    // Speak the response
    if (synthRef.current && mockResponse) {
      const utterance = new SpeechSynthesisUtterance(mockResponse)
      utterance.rate = 0.8
      utterance.pitch = 1
      utterance.volume = 0.8
      synthRef.current.speak(utterance)
    }
  }

  const clearConversation = () => {
    setTranscript('')
    setResponse('')
    setError('')
    if (synthRef.current) {
      synthRef.current.cancel()
    }
  }

  if (!isSupported) {
    return (
      <div className="voice-widget error">
        <div className="error-message">
          <h3>Speech Recognition Not Supported</h3>
          <p>Your browser doesn't support speech recognition. Please use Chrome, Edge, or Safari.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="voice-widget">
      <div className="widget-header">
        <h2>Voice Assistant</h2>
        <button 
          className="clear-btn"
          onClick={clearConversation}
          disabled={isListening || isProcessing}
        >
          Clear
        </button>
      </div>

      <div className="microphone-container">
        <button
          className={`mic-button ${isListening ? 'listening' : ''} ${isProcessing ? 'processing' : ''}`}
          onClick={isListening ? stopListening : startListening}
          disabled={isProcessing}
        >
          <div className="mic-icon">
            {isListening ? '🎤' : isProcessing ? '⏳' : '🎙️'}
          </div>
          <div className="mic-status">
            {isListening ? 'Listening...' : isProcessing ? 'Processing...' : 'Click to speak'}
          </div>
        </button>
      </div>

      {transcript && (
        <div className="transcript-section">
          <h3>You said:</h3>
          <p className="transcript">{transcript}</p>
        </div>
      )}

      {response && (
        <div className="response-section">
          <h3>Assistant:</h3>
          <p className="response">{response}</p>
        </div>
      )}

      {error && (
        <div className="error-section">
          <p className="error">{error}</p>
        </div>
      )}

      <div className="instructions">
        <p>Click the microphone and speak clearly. The assistant will respond both with text and voice.</p>
      </div>
    </div>
  )
}

export default VoiceWidget
