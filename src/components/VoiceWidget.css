.voice-widget {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.voice-widget.error {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.widget-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.clear-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.clear-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.clear-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.call-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.call-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #333;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.call-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.call-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.call-button.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
}

.call-button.connecting {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
  color: white;
}

.call-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.call-status {
  font-size: 0.9rem;
  text-align: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  gap: 0.5rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.status-dot.idle {
  background: rgba(255, 255, 255, 0.5);
}

.status-dot.connecting {
  background: #feca57;
  animation: pulse 1.5s infinite;
}

.status-dot.active {
  background: #2ed573;
  animation: pulse 2s infinite;
}

.status-dot.ended {
  background: #ff6b6b;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.status-text {
  font-size: 0.9rem;
  opacity: 0.9;
}

.transcript-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.transcript-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.transcript-content {
  max-height: 300px;
  overflow-y: auto;
}

.transcript {
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.error-section {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.error {
  margin: 0;
  color: #ffcccb;
  font-weight: 500;
}

.instructions {
  text-align: center;
  opacity: 0.8;
  font-size: 0.9rem;
  line-height: 1.5;
}

.instructions .warning {
  color: #feca57;
  font-weight: 600;
  margin-top: 0.5rem;
}

.error-message {
  text-align: center;
  padding: 2rem;
}

.error-message h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
}

.error-message p {
  margin: 0 0 1.5rem 0;
  opacity: 0.9;
}

.config-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: left;
  margin-top: 1.5rem;
}

.config-info p {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.config-info ul {
  margin: 0.5rem 0 0 0;
  padding-left: 1.5rem;
}

.config-info li {
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

/* Responsive design */
@media (max-width: 768px) {
  .voice-widget {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .call-button {
    width: 100px;
    height: 100px;
  }
  
  .call-icon {
    font-size: 1.5rem;
  }
  
  .widget-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
