// Mock API for demonstration purposes
// In a real application, this would be your backend endpoint

export const createWebCall = async (agentId) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // This is a mock response - in reality, you would call the Retell API
  // from your backend with your API key
  
  // Example of what your backend should do:
  /*
  import Retell from 'retell-sdk';
  
  const client = new Retell({
    apiKey: 'YOUR_RETELL_API_KEY',
  });
  
  const webCallResponse = await client.call.createWebCall({ 
    agent_id: agentId 
  });
  
  return webCallResponse.access_token;
  */
  
  // For demo purposes, we'll return a mock access token
  // This will fail when actually trying to connect to Retell
  const mockResponse = {
    access_token: 'mock_access_token_' + Date.now(),
    call_id: 'mock_call_id_' + Date.now(),
    agent_id: agentId,
    call_status: 'registered'
  }
  
  // Simulate potential errors
  if (!agentId || agentId === 'invalid') {
    throw new Error('Invalid agent ID provided')
  }
  
  return mockResponse.access_token
}

// Mock fetch implementation for the demo
export const setupMockApi = () => {
  // Override fetch for the /api/create-web-call endpoint
  const originalFetch = window.fetch
  
  window.fetch = async (url, options) => {
    if (url === '/api/create-web-call' && options?.method === 'POST') {
      try {
        const body = JSON.parse(options.body)
        const accessToken = await createWebCall(body.agent_id)
        
        return {
          ok: true,
          status: 200,
          json: async () => ({
            access_token: accessToken,
            call_id: 'mock_call_id_' + Date.now(),
            agent_id: body.agent_id,
            call_status: 'registered'
          })
        }
      } catch (error) {
        return {
          ok: false,
          status: 400,
          json: async () => ({
            error: error.message
          })
        }
      }
    }
    
    // For all other requests, use the original fetch
    return originalFetch(url, options)
  }
}
