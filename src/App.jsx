import { useState } from 'react'
import VoiceWidget from './components/VoiceWidget'
import './App.css'

function App() {
  const [agentId, setAgentId] = useState(import.meta.env.VITE_DEFAULT_AGENT_ID || '')
  const [showConfig, setShowConfig] = useState(!import.meta.env.VITE_DEFAULT_AGENT_ID)

  const handleConfigSubmit = (e) => {
    e.preventDefault()
    if (agentId.trim()) {
      setShowConfig(false)
    }
  }

  const handleCallStart = () => {
    console.log('Call started!')
  }

  const handleCallEnd = () => {
    console.log('Call ended!')
  }

  const handleTranscriptUpdate = (transcript) => {
    console.log('Transcript updated:', transcript)
  }

  const handleError = (error) => {
    console.error('Voice widget error:', error)
  }

  if (showConfig) {
    return (
      <div className="app">
        <div className="container">
          <h1>Retell AI Voice Widget</h1>
          <div className="config-form">
            <h2>Configuration</h2>
            <p>Enter your Retell AI Agent ID to get started:</p>
            <form onSubmit={handleConfigSubmit}>
              <div className="form-group">
                <label htmlFor="agentId">Agent ID:</label>
                <input
                  type="text"
                  id="agentId"
                  value={agentId}
                  onChange={(e) => setAgentId(e.target.value)}
                  placeholder="e.g., oBeDLoLOeuAbiuaMFXRtDOLriTJ5tSxD"
                  required
                />
              </div>
              <button type="submit" disabled={!agentId.trim()}>
                Start Voice Widget
              </button>
            </form>
            <div className="info-box">
              <h3>Setup Instructions:</h3>
              <ol>
                <li>Create a Retell AI account and set up an agent</li>
                <li>Copy your agent ID from the dashboard</li>
                <li>Set up a backend endpoint at <code>/api/create-web-call</code></li>
                <li>Configure your Retell API key in the backend</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="app">
      <div className="container">
        <div className="header">
          <h1>Retell AI Voice Widget Demo</h1>
          <button
            className="config-btn"
            onClick={() => setShowConfig(true)}
          >
            ⚙️ Configure
          </button>
        </div>
        <p>Start a conversation with your AI agent using voice!</p>
        <VoiceWidget
          agentId={agentId}
          onCallStart={handleCallStart}
          onCallEnd={handleCallEnd}
          onTranscriptUpdate={handleTranscriptUpdate}
          onError={handleError}
        />
      </div>
    </div>
  )
}

export default App
