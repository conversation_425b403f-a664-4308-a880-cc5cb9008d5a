import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import Retell from 'retell-sdk';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'], // Allow Vite and CRA dev servers
  credentials: true
}));
app.use(express.json());

// Initialize Retell client
const retellClient = new Retell({
  apiKey: process.env.RETELL_API_KEY,
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'Retell Voice Widget Backend is running',
    hasApiKey: !!process.env.RETELL_API_KEY
  });
});

// Create web call endpoint
app.post('/api/create-web-call', async (req, res) => {
  try {
    const { agent_id, metadata, retell_llm_dynamic_variables } = req.body;

    // Validate required fields
    if (!agent_id) {
      return res.status(400).json({
        error: 'agent_id is required'
      });
    }

    // Check if API key is configured
    if (!process.env.RETELL_API_KEY) {
      return res.status(500).json({
        error: 'RETELL_API_KEY not configured on server'
      });
    }

    console.log('Creating web call for agent:', agent_id);

    // Create web call using Retell SDK
    const webCallResponse = await retellClient.call.createWebCall({
      agent_id,
      metadata: metadata || {},
      retell_llm_dynamic_variables: retell_llm_dynamic_variables || {}
    });

    console.log('Web call created successfully:', {
      call_id: webCallResponse.call_id,
      agent_id: webCallResponse.agent_id,
      call_status: webCallResponse.call_status
    });

    // Return the access token and call info
    res.json({
      access_token: webCallResponse.access_token,
      call_id: webCallResponse.call_id,
      agent_id: webCallResponse.agent_id,
      call_status: webCallResponse.call_status
    });

  } catch (error) {
    console.error('Error creating web call:', error);
    
    // Handle different types of errors
    if (error.status === 401) {
      return res.status(401).json({
        error: 'Invalid Retell API key'
      });
    } else if (error.status === 404) {
      return res.status(404).json({
        error: 'Agent not found. Please check your agent_id'
      });
    } else if (error.status === 422) {
      return res.status(422).json({
        error: 'Invalid request parameters',
        details: error.message
      });
    }

    res.status(500).json({
      error: 'Failed to create web call',
      message: error.message
    });
  }
});

// Get call details endpoint (optional)
app.get('/api/call/:call_id', async (req, res) => {
  try {
    const { call_id } = req.params;

    if (!process.env.RETELL_API_KEY) {
      return res.status(500).json({
        error: 'RETELL_API_KEY not configured on server'
      });
    }

    const callDetails = await retellClient.call.retrieve(call_id);
    res.json(callDetails);

  } catch (error) {
    console.error('Error retrieving call:', error);
    res.status(500).json({
      error: 'Failed to retrieve call details',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Retell Voice Widget Backend running on port ${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🔑 API Key configured: ${!!process.env.RETELL_API_KEY}`);
  
  if (!process.env.RETELL_API_KEY) {
    console.log('⚠️  WARNING: RETELL_API_KEY not found in environment variables');
    console.log('   Please create a .env file with your Retell API key');
  }
});
